<?xml version="1.0"?>
<phpmd-baseline>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Admin/Controller/RefundPayment/UpdateRefundPaymentStateController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Admin/Controller/RefundPayment/UpdateRefundPaymentStateController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Admin/Form/Type/RefundPaymentType.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Admin/Form/Type/SupplierCountryShippingType.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Admin/Promotion/Action/OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommand.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Admin/Promotion/Action/OrderFixedDiscountForEachItemAfterNthItemOfTypeConfigurationType.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Applicator/OrderStateMachineTransitionApplicator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Api/Applicator/OrderStateMachineTransitionApplicatorInterface.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Account/RegisterCustomer.php"/>
  <violation rule="PHPMD\Rule\CleanCode\BooleanArgumentFlag" file="src/Api/Command/Account/RegisterCustomer.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Account/UpdateAccountInformation.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Account/UpdateAccountInformationRequest.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Admin/Order/AddOrderItemOperation.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Cart/AddItem.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Cart/RemoveRelatedCartItem.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Cart/RemoveRelatedCartItems.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Checkout/Order/ApproveMedication.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Notify/OrderShipmentStatus.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Command/Notify/OrderShipmentStatusWasUpdated.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Account/Password/RequestPasswordResetHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Account/RegisterCustomerHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Account/UpdateAccountInformationHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Admin/Order/AddOrderItemOperationHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Admin/Order/UpdateOrderItemOperationHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Cart/AddItemsHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Cart/AddItemsHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Cart/ApplyCouponHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Cart/ModifyItemQuantityHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Cart/PickupCartHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Cart/RemoveRelatedCartItemsHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Checkout/Address/SetBillingAndShippingAddressHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Checkout/Address/SetBillingAndShippingAddressHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Checkout/ChoosePaymentMethodHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Checkout/CompleteOrderHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Checkout/CompleteOrderHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Checkout/Order/ApproveMedicationHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Checkout/TransitionPrescriptionStateHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Notify/OrderShipmentStatusHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Order/CancelOrderHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/CommandHandler/Order/PayOrderHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Api/CommandHandler/Shipment/GetOrderTrackingWhenOrderShipmentStatusWasUpdatedHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Shipment/GetOrderTrackingWhenOrderShipmentStatusWasUpdatedHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/CommandHandler/Shipment/UpdateShipmentTrackingHandler.php"/>
  <violation rule="PHPMD\Rule\Design\NumberOfChildren" file="src/Api/Controller/AbstractController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/AbstractController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Account/GetAccountController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Account/UpdateAccountInformationController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Addresses/GetAddressesController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Addresses/GetAddressesController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Api/Controller/Admin/Order/AbstractTransitionOrderPrescriptionStateController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Admin/Order/AbstractTransitionOrderPrescriptionStateController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Admin/Order/ApproveMedicationController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Admin/Order/GetOrderController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Admin/Order/GetOrdersController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Admin/Order/ReplaceOrderItemsController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Admin/Order/ReplaceOrderItemsController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Admin/Order/UpdateShipmentTrackingController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Admin/Order/UpdateShipmentTrackingController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Cart/AddItemsToCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/AddItemsToCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/ApplyCouponToCartController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Cart/CreateCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/CreateCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/GetCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/ModifyItemQuantityController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/RemoveCouponFromCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/RemoveItemFromCartController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Cart/RemoveRelatedCartItemsController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Checkout/Address/SetBillingAndShippingAddressController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Checkout/Address/SetBillingAndShippingAddressController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Checkout/CompleteOrderController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Checkout/GetPaymentMethodsController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Notify/NotifyOrderShipmentStatusController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/CancelOrderController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Order/Communication/ReplyCommunicationController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/Communication/ReplyCommunicationController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/GetOrderCompletionInformationController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/GetOrderController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/GetOrdersController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/GetPaymentMethodsController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/InvoiceController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Order/PaymentController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Product/GetProductController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Product/GetProductController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Product/GetProductEnrichmentController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Product/GetProductInListController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Controller/Product/GetProductVariantsController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/Product/GetProductVariantsController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Controller/TermQuestion/GetTermQuestionsController.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/EventSubscriber/JsonRequestBodyDeserializationSubscriber.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/EventSubscriber/JsonRequestBodyDeserializationSubscriber.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Api/Exception/RelatedProductVariantIsNoConsultException.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Modifier/OrderItemModifier.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Modifier/OrderItemModifierInterface.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Request/ProductListFilter.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Serializer/ProductDefaultVariantNormalizer.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Serializer/ShipmentTrackAndTraceLinkNormalizer.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Api/Validator/ChannelAllowedForShippingAddressValidator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Validator/CouponCodeAllowedValidator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Validator/PasswordResetTokenValidValidator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Validator/ProductVariantExistsValidator.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Validator/ProductVariantMaxQuantitiesValidator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Validator/ProductVariantMaxQuantitiesValidator.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Validator/RelatedProductVariantCodesValidator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Validator/RelatedProductVariantCodesValidator.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Api/Validator/StrongPasswordValidator.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Api/Validator/UserAllowedToModifyOrderValidator.php"/>
  <violation rule="PHPMD\Rule\CyclomaticComplexity" file="src/Api/Validator/UserAllowedToModifyOrderValidator.php" method="validate"/>
  <violation rule="PHPMD\Rule\Design\NpathComplexity" file="src/Api/Validator/UserAllowedToModifyOrderValidator.php" method="validate"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/CanopyDeploy/Dto/Customer.php"/>
  <violation rule="PHPMD\Rule\CleanCode\IfStatementAssignment" file="src/CanopyDeploy/Dto/Customer.php" method="fromEntity"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/CanopyDeploy/Dto/Customer/LoyaltyPointsAccount.php"/>
  <violation rule="PHPMD\Rule\Design\TooManyFields" file="src/CanopyDeploy/Dto/Order.php"/>
  <violation rule="PHPMD\Rule\CleanCode\IfStatementAssignment" file="src/CanopyDeploy/Dto/PasswordReset.php" method="fromEntity"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/CanopyDeploy/MessageHandler/CreateOrUpdateCartOrOrderHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/CanopyDeploy/MessageHandler/RemoveItemFromCartOrOrderHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/CanopyDeploy/MessageHandler/SendCustomerDataHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/CanopyDeploy/MessageHandler/SendPasswordResetDataHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/CanopyDeploy/MessageHandler/SendPasswordResetDataHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Catalog/Currency/CeilRoundingCurrencyConverter.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Command/ShopConfigurationLoadCommand.php"/>
  <violation rule="PHPMD\Rule\CyclomaticComplexity" file="src/Controller/Payment/ReturnController.php" method="__invoke"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Controller/Payment/ReturnController.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/BusinessUnit/BusinessUnit.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Channel/Channel.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Channel/ChannelInterface.php"/>
  <violation rule="PHPMD\Rule\Design\WeightedMethodCount" file="src/Entity/Order/Order.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Order/Order.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Order/OrderInterface.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Order/OrderItem.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Order/PreviousOrderItem.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Product/Product.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Product/ProductVariant.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Product/ProductVariantInterface.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Shipping/Shipment.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Entity/Supplier/Supplier.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Exception/UnableToTransformSupplierServiceOrderShipmentStatusException.php"/>
  <violation rule="PHPMD\Rule\CleanCode\BooleanArgumentFlag" file="src/Factory/Order/AdjustmentFactory.php"/>
  <violation rule="PHPMD\Rule\CleanCode\BooleanArgumentFlag" file="src/Factory/Order/AdjustmentFactoryInterface.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Fixture/Factory/ProductExampleFactory.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Fixture/Factory/ProductExampleFactory.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Fixture/Factory/ProductOptionExampleFactory.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Fixture/Listener/ExcludeShopConfigurationTableFixtureListener.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Fixture/Listener/ExcludeShopConfigurationTableFixtureListener.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Fixture/Listener/FollowUpOrderLinkerAfterFixturesSuiteListener.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Fixture/Listener/FollowUpOrderLinkerAfterFixturesSuiteListener.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Fixture/Listener/LinkMenuTaxonToChannelAfterFixtureSuiteListener.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Message/Handler/SupplierService/CancelSupplierServiceShipmentHandler.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Message/Handler/SupplierService/CreateSupplierServiceShipmentHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Message/Handler/SupplierService/CreateSupplierServiceShipmentHandler.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/OrderProcessing/CostPrice/HandlingFeeAndShippingCostPriceProcessor.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/OrderProcessing/FollowUpOrder/CompleteOrderAndApproveMedicationProcessor.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/OrderProcessing/FollowUpOrder/CompleteOrderAndApproveMedicationProcessor.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/OrderProcessing/FollowUpOrder/CompleteOrderAndApproveMedicationProcessor.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/OrderProcessing/FollowUpOrder/CustomerAndAddressProcessor.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/OrderProcessing/FollowUpOrder/CustomerAndAddressProcessor.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/OrderProcessing/Warnings/WarnWhenMaximumQuantityPerOrderIsExceededOrderProcessor.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/OrderProcessing/Warnings/WarnWhenMultipleSuppliersAreAddedOrderProcessor.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/Payum/Action/CaptureAction.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Payum/PaymentService/CreateInvoiceFactory.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Projection/Product/ProductDetailProjection.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Projection/Product/ProductVariantDetailProjection.php"/>
  <violation rule="PHPMD\Rule\CyclomaticComplexity" file="src/Projection/Product/ProductVariantDetailProjection.php" method="getHighestRankedUniqueProductVariants"/>
  <violation rule="PHPMD\Rule\CleanCode\ElseExpression" file="src/Projection/Product/ProductVariantDetailProjection.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/Repository/ProductVariantRepository.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/BusinessUnitConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/ChannelConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/CompositeConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/CountryConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/CustomerPoolConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/PaymentMethodConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/ShopConfiguration/ProductAssociationTypeConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/ProductAssociationTypeConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/ProductAttributeConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/ProductOptionConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/ShippingMethodConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/SupplierConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/ShopConfiguration/TermQuestionConfigurationLoader.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/StateMachine/Callback/CreateFollowUpOrder.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/StateMachine/Callback/CreateRefund.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/StateMachine/Callback/CreateRefund.php"/>
  <violation rule="PHPMD\Rule\Design\CouplingBetweenObjects" file="src/StateMachine/Callback/CreateRefundForOverpaidOrder.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/StateMachine/Callback/CreateRefundForOverpaidOrder.php"/>
  <violation rule="PHPMD\Rule\Design\EmptyCatchBlock" file="src/StateMachine/Callback/GenerateInvoiceForShipmentService.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/StateMachine/StateResolver/OrderPrescriptionStateResolver.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/StateMachine/StateResolver/OrderShippingStateResolver.php"/>
  <violation rule="PHPMD\Rule\Naming\LongVariable" file="src/StateMachine/StateResolver/ShipmentStateResolver.php"/>
  <violation rule="PHPMD\Rule\Naming\LongClassName" file="src/Transformer/SupplierServiceOrderShipmentStatusTransformer.php"/>
</phpmd-baseline>
