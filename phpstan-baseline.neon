parameters:
	ignoreErrors:
		-
			message: "#^Method App\\\\Admin\\\\Controller\\\\Order\\\\GetAvailableSuppliersController\\:\\:__invoke\\(\\) has parameter \\$orderItemIds with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Controller/Order/GetAvailableSuppliersController.php

		-
			message: "#^Method App\\\\Admin\\\\Controller\\\\Order\\\\GetAvailableSuppliersController\\:\\:getVariantCodesFromOrderItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Controller/Order/GetAvailableSuppliersController.php

		-
			message: "#^Method App\\\\Admin\\\\Form\\\\ProductVariantEligiblePromotion\\:\\:getAllTranslatedOptions\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Form/ProductVariantEligiblePromotion.php

		-
			message: "#^Method App\\\\Admin\\\\Form\\\\Type\\\\AbstractSwitchSupplierType\\:\\:addFormTypes\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Form/Type/AbstractSwitchSupplierType.php

		-
			message: "#^Method App\\\\Admin\\\\Form\\\\Type\\\\OrderSwitchSupplierType\\:\\:addFormTypes\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Form/Type/OrderSwitchSupplierType.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\FieldTypes\\\\OrderSuppliers\\:\\:render\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/FieldTypes/OrderSuppliers.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\FieldTypes\\\\ShipmentSupplier\\:\\:render\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/FieldTypes/ShipmentSupplier.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\FieldTypes\\\\UserDateTimeFieldType\\:\\:render\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/FieldTypes/UserDateTimeFieldType.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\BusinessUnitFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/BusinessUnitFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\CustomerNameFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/CustomerNameFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\DoctorNameFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/DoctorNameFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\OrderSupplierFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/OrderSupplierFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\ShipmentMethodFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/ShipmentMethodFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\ShippingAddressPostcodeFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/ShippingAddressPostcodeFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\UserDateTimeZoneFilter\\:\\:apply\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/UserDateTimeZoneFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\UserDateTimeZoneFilter\\:\\:getDateTime\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/UserDateTimeZoneFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Filter\\\\UserDateTimeZoneFilter\\:\\:getDateTime\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Filter/UserDateTimeZoneFilter.php

		-
			message: "#^Method App\\\\Admin\\\\Grid\\\\Orm\\\\Driver\\:\\:getDataSource\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Admin/Grid/Orm/Driver.php

		-
			message: "#^Method App\\\\Affiliate\\\\Command\\\\ImportConversionsCommand\\:\\:updateConversion\\(\\) has parameter \\$orderData with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Affiliate/Command/ImportConversionsCommand.php

		-
			message: "#^Property App\\\\Affiliate\\\\Command\\\\ImportConversionsCommand\\:\\:\\$skippedOrders type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Affiliate/Command/ImportConversionsCommand.php

		-
			message: "#^Method App\\\\Affiliate\\\\Partnerize\\\\Encoder\\\\ConversionUriEncoder\\:\\:encodeArray\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Affiliate/Partnerize/Encoder/ConversionUriEncoder.php

		-
			message: "#^Method App\\\\Affiliate\\\\Partnerize\\\\Encoder\\\\ConversionUriEncoder\\:\\:supportsEncoding\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Affiliate/Partnerize/Encoder/ConversionUriEncoder.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Cart\\\\ApplyCoupon\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Cart/ApplyCoupon.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Cart\\\\ValidateCart\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Cart/ValidateCart.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Checkout\\\\CompleteQuestionnaire\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Checkout/CompleteQuestionnaire.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Checkout\\\\RegisterQuestionnaireReference\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Checkout/RegisterQuestionnaireReference.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Payment\\\\AbstractUpdatePaymentOnOrder\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Payment/AbstractUpdatePaymentOnOrder.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Payment\\\\UpdatePaymentOnCart\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Payment/UpdatePaymentOnCart.php

		-
			message: "#^Method App\\\\Api\\\\Command\\\\Payment\\\\UpdatePaymentOnOrder\\:\\:getAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Command/Payment/UpdatePaymentOnOrder.php

		-
			message: "#^Method App\\\\Api\\\\CommandHandler\\\\Cart\\\\RemoveChildCartItemsHandler\\:\\:getCodes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/CommandHandler/Cart/RemoveChildCartItemsHandler.php

		-
			message: "#^Property App\\\\Api\\\\CommandHandler\\\\Cart\\\\RemoveChildCartItemsHandler\\:\\:\\$removedItems type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/CommandHandler/Cart/RemoveChildCartItemsHandler.php

		-
			message: "#^Method App\\\\Api\\\\CommandHandler\\\\Cart\\\\ValidateCartHandler\\:\\:__invoke\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/CommandHandler/Cart/ValidateCartHandler.php

		-
			message: "#^Parameter \\#2 \\$key of static method Webmozart\\\\Assert\\\\Assert\\:\\:keyExists\\(\\) expects int\\|string, string\\|null given\\.$#"
			count: 1
			path: src/Api/CommandHandler/Checkout/ChoosePaymentMethodHandler.php

		-
			message: "#^Method App\\\\Api\\\\CommandHandler\\\\Notify\\\\ConsultRequestStatusHandler\\:\\:getLogEntriesForCompositeOrderItemOperations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/CommandHandler/Notify/ConsultRequestStatusHandler.php

		-
			message: "#^Method App\\\\Api\\\\CommandHandlerDecoration\\\\Cart\\\\ResetCartCommandHandlerDecoration\\:\\:__invoke\\(\\) has parameter \\$command with no type specified\\.$#"
			count: 1
			path: src/Api/CommandHandlerDecoration/Cart/ResetCartCommandHandlerDecoration.php

		-
			message: "#^Trying to invoke Symfony\\\\Component\\\\Messenger\\\\Handler\\\\MessageHandlerInterface but it might not be a callable\\.$#"
			count: 1
			path: src/Api/CommandHandlerDecoration/Cart/ResetCartCommandHandlerDecoration.php

		-
			message: "#^Method App\\\\Api\\\\Controller\\\\AbstractCommandController\\:\\:dispatchCommand\\(\\) has parameter \\$stamps with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Controller/AbstractCommandController.php

		-
			message: "#^PHPDoc tag @var for variable \\$result has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Controller/Cart/ValidateCartController.php

		-
			message: "#^Method App\\\\Api\\\\EventSubscriber\\\\IsGrantedSubscriber\\:\\:throwException\\(\\) has parameter \\$routeParams with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/EventSubscriber/IsGrantedSubscriber.php

		-
			message: "#^Method App\\\\Api\\\\Exception\\\\AccessDeniedHttpException\\:\\:__construct\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Exception/AccessDeniedHttpException.php

		-
			message: "#^Method App\\\\Api\\\\Request\\\\Filter\\\\ProductVariant\\\\SearchByCodesFilter\\:\\:getValue\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Request/Filter/ProductVariant/SearchByCodesFilter.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\AddItemsDenormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/AddItemsDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\AddItemsDenormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/AddItemsDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\OrderItemOperationDenormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/OrderItemOperationDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\OrderItemOperationDenormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/OrderItemOperationDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\OrderItemOperationsDenormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/OrderItemOperationsDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\OrderItemOperationsDenormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/OrderItemOperationsDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\RemoveChildCartItemsDenormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/RemoveChildCartItemsDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\Command\\\\RemoveChildCartItemsDenormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/Command/RemoveChildCartItemsDenormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\UuidNormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/UuidNormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\UuidNormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/UuidNormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Serializer\\\\UuidNormalizer\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Serializer/UuidNormalizer.php

		-
			message: "#^Method App\\\\Api\\\\Validator\\\\AllowedState\\:\\:__construct\\(\\) has parameter \\$allowedStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Validator/AllowedState.php

		-
			message: "#^Method App\\\\Api\\\\Validator\\\\CanTransitionState\\:\\:__construct\\(\\) has parameter \\$validStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Validator/CanTransitionState.php

		-
			message: "#^Method App\\\\Api\\\\Validator\\\\DisallowedState\\:\\:__construct\\(\\) has parameter \\$disallowedStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Validator/DisallowedState.php

		-
			message: "#^Method App\\\\Api\\\\Validator\\\\OrderItemIsOfType\\:\\:__construct\\(\\) has parameter \\$allowedTypes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Validator/OrderItemIsOfType.php

		-
			message: "#^Method App\\\\Api\\\\Validator\\\\ServiceProductAllowedValidator\\:\\:getServiceProductOrderItemCodes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Api/Validator/ServiceProductAllowedValidator.php

		-
			message: "#^Method App\\\\Auditing\\\\AuditLogger\\:\\:log\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Auditing/AuditLogger.php

		-
			message: "#^Method App\\\\Auditing\\\\AuditLoggerInterface\\:\\:log\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Auditing/AuditLoggerInterface.php

		-
			message: "#^Method App\\\\CanopyDeploy\\\\Client\\\\CanopyDeployRetryStrategy\\:\\:__construct\\(\\) has parameter \\$statusCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CanopyDeploy/Client/CanopyDeployRetryStrategy.php

		-
			message: "#^Method App\\\\CanopyDeploy\\\\Client\\\\Webhook\\\\CanopyDeployWebhookClient\\:\\:__construct\\(\\) has parameter \\$bearerTokens with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CanopyDeploy/Client/Webhook/CanopyDeployWebhookClient.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Client\\\\Query\\:\\:__construct\\(\\) has parameter \\$productTypes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Client/Query.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Client\\\\Query\\:\\:getQueryStringParams\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Client/Query.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getAttributeCollection\\(\\) has parameter \\$specs with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getChannelCollection\\(\\) has parameter \\$limitedToStores with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getChannelPricingCollection\\(\\) has parameter \\$prices with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getProductTranslationCollection\\(\\) has parameter \\$localizedProperties with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getProductVariantTranslations\\(\\) has parameter \\$localizedProperties with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:getTaxonCollection\\(\\) has parameter \\$collections with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:reformatLocalizedProperties\\(\\) has parameter \\$localizedProperties with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\AbstractUpsertProductNormalizer\\:\\:reformatLocalizedProperties\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/AbstractUpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertProductNormalizer\\:\\:createUpsertProduct\\(\\) has parameter \\$productData with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertProductNormalizer\\:\\:denormalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertProductNormalizer\\:\\:getConsultProductCollection\\(\\) has parameter \\$collections with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertProductNormalizer\\:\\:getImageCollection\\(\\) has parameter \\$collection with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertProductNormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizer\\:\\:createSimpleProduct\\(\\) has parameter \\$simpleProductData with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizer\\:\\:denormalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizer\\:\\:getUpsertProductVariant\\(\\) has parameter \\$localizedProperties with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizer\\:\\:getUpsertProductVariant\\(\\) has parameter \\$simpleProductData with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizer\\:\\:isDigitalProduct\\(\\) has parameter \\$specs with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\UpsertProductVariantNormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/UpsertProductVariantNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\UpsertTaxonNormalizer\\:\\:getLocalizedProperty\\(\\) has parameter \\$localizedProperties with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/UpsertTaxonNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\UpsertTaxonNormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/UpsertTaxonNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\UpsertTaxonNormalizer\\:\\:supportsDenormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Loader/Katana/Normalizer/UpsertTaxonNormalizer.php

		-
			message: "#^Method App\\\\Catalog\\\\Message\\\\AbstractCollection\\:\\:map\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Message/AbstractCollection.php

		-
			message: "#^Property App\\\\Catalog\\\\Message\\\\AbstractCollection\\:\\:\\$elements type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Message/AbstractCollection.php

		-
			message: "#^Method App\\\\Catalog\\\\Message\\\\Attribute\\:\\:__construct\\(\\) has parameter \\$value with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Message/Attribute.php

		-
			message: "#^Method App\\\\Catalog\\\\Processor\\\\Product\\\\CompositeSimpleProductProcessor\\:\\:__construct\\(\\) has parameter \\$upsertProcessors with no value type specified in iterable type iterable\\.$#"
			count: 1
			path: src/Catalog/Processor/Product/CompositeSimpleProductProcessor.php

		-
			message: "#^Method App\\\\Catalog\\\\Processor\\\\Product\\\\ProductTaxonProcessor\\:\\:addChannelsToProductTaxons\\(\\) has parameter \\$channels with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Processor/Product/ProductTaxonProcessor.php

		-
			message: "#^Method App\\\\Catalog\\\\Processor\\\\Product\\\\ProductTaxonProcessor\\:\\:addChannelsToProductTaxons\\(\\) has parameter \\$taxons with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Processor/Product/ProductTaxonProcessor.php

		-
			message: "#^Method App\\\\Catalog\\\\Processor\\\\Product\\\\ProductTaxonProcessor\\:\\:getTaxon\\(\\) has parameter \\$taxons with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Processor/Product/ProductTaxonProcessor.php

		-
			message: "#^Method App\\\\Catalog\\\\Processor\\\\ProductVariant\\\\ProductOptionProcessor\\:\\:fetchOrCreateProductOptionValues\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Catalog/Processor/ProductVariant/ProductOptionProcessor.php

		-
			message: "#^Property App\\\\Command\\\\AnonymizeDatabaseCommand\\:\\:\\$doctorRegistrationNumbers type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/AnonymizeDatabaseCommand.php

		-
			message: "#^Method App\\\\Command\\\\ForgetAccountCommand\\:\\:getCustomerPoolCodes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/ForgetAccountCommand.php

		-
			message: "#^Method App\\\\Command\\\\ShopConfigurationLoadCommand\\:\\:findAndCombineConfigurationFiles\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/ShopConfigurationLoadCommand.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CompositeOrderItemOperationsBuilder\\:\\:formatDefinitiveItems\\(\\) has parameter \\$definitiveItems with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CompositeOrderItemOperationsBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CompositeOrderItemOperationsBuilder\\:\\:formatDefinitiveItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CompositeOrderItemOperationsBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CompositeOrderItemOperationsBuilder\\:\\:getAddOperations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CompositeOrderItemOperationsBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CompositeOrderItemOperationsBuilder\\:\\:getRemoveOperations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CompositeOrderItemOperationsBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CompositeOrderItemOperationsBuilder\\:\\:getUpdateOperations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CompositeOrderItemOperationsBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CreateConsultRequestBuilder\\:\\:getOrderItem\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CreateConsultRequestBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CreateConsultRequestBuilder\\:\\:getOrderItem\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CreateConsultRequestBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CreateConsultRequestBuilder\\:\\:getPreferredOrderItem\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CreateConsultRequestBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CreateConsultRequestBuilder\\:\\:getProductTags\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CreateConsultRequestBuilder.php

		-
			message: "#^Method App\\\\ConsultSystem\\\\CreateConsultRequestBuilder\\:\\:getProductVariantAttributes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ConsultSystem/CreateConsultRequestBuilder.php

		-
			message: "#^Method App\\\\Entity\\\\Auditing\\\\AbstractAuditEntry\\:\\:interpolate\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Auditing/AbstractAuditEntry.php

		-
			message: "#^Property App\\\\Entity\\\\Auditing\\\\AbstractAuditEntry\\:\\:\\$context type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Auditing/AbstractAuditEntry.php

		-
			message: "#^Method App\\\\Entity\\\\Order\\\\OrderItem\\:\\:getWarnings\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Order/OrderItem.php

		-
			message: "#^Method App\\\\Entity\\\\Order\\\\OrderItem\\:\\:setWarnings\\(\\) has parameter \\$warnings with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Order/OrderItem.php

		-
			message: "#^Property App\\\\Entity\\\\Order\\\\OrderItem\\:\\:\\$warnings type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Order/OrderItem.php

		-
			message: "#^Method App\\\\Entity\\\\Payment\\\\Payment\\:\\:addDetails\\(\\) has parameter \\$details with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Payment/Payment.php

		-
			message: "#^Method App\\\\Entity\\\\Payment\\\\PaymentInterface\\:\\:addDetails\\(\\) has parameter \\$details with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Payment/PaymentInterface.php

		-
			message: "#^Property App\\\\Entity\\\\Product\\\\ProductVariant\\:\\:\\$attributes type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Product/ProductVariant.php

		-
			message: "#^Method App\\\\Entity\\\\Product\\\\ValueObject\\\\Attributes\\:\\:__construct\\(\\) has parameter \\$additionalAttributes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Product/ValueObject/Attributes.php

		-
			message: "#^Method App\\\\Entity\\\\Product\\\\ValueObject\\\\Attributes\\:\\:all\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/Product/ValueObject/Attributes.php

		-
			message: "#^Method App\\\\Entity\\\\TermQuestion\\\\TermQuestionInterface\\:\\:getId\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Entity/TermQuestion/TermQuestionInterface.php

		-
			message: "#^Method App\\\\Exception\\\\InvalidStateException\\:\\:__construct\\(\\) has parameter \\$expectedStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Exception/InvalidStateException.php

		-
			message: "#^Method App\\\\ExchangeRate\\\\ExchangeRateUpdater\\:\\:createOrUpdateExchangeRates\\(\\) has parameter \\$exchangeRates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ExchangeRate/ExchangeRateUpdater.php

		-
			message: "#^Method App\\\\ExchangeRate\\\\ExchangeRateUpdater\\:\\:getAllCurrencies\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ExchangeRate/ExchangeRateUpdater.php

		-
			message: "#^Method App\\\\ExchangeRate\\\\ExchangeRateUpdater\\:\\:getExchangeRates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ExchangeRate/ExchangeRateUpdater.php

		-
			message: "#^Method App\\\\ExchangeRate\\\\ExchangeRateUpdater\\:\\:getRequestOptions\\(\\) has parameter \\$currencies with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ExchangeRate/ExchangeRateUpdater.php

		-
			message: "#^Method App\\\\ExchangeRate\\\\ExchangeRateUpdater\\:\\:getRequestOptions\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ExchangeRate/ExchangeRateUpdater.php

		-
			message: "#^Class App\\\\Factory\\\\Order\\\\AdjustmentFactory extends generic class Sylius\\\\Component\\\\Order\\\\Factory\\\\AdjustmentFactory but does not specify its types\\: T$#"
			count: 1
			path: src/Factory/Order/AdjustmentFactory.php

		-
			message: "#^Method App\\\\Factory\\\\Order\\\\AdjustmentFactoryInterface\\:\\:createForCostPrice\\(\\) has parameter \\$details with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Factory/Order/AdjustmentFactoryInterface.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Model\\\\FromArrayInterface\\:\\:fromArray\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Model/FromArrayInterface.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Model\\\\ProductVariant\\:\\:fromArray\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Model/ProductVariant.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Model\\\\ProductVariant\\:\\:getDescription\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Model/ProductVariant.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Model\\\\ProductVariant\\:\\:getOptions\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Model/ProductVariant.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Model\\\\ProductVariant\\:\\:getProduct\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Model/ProductVariant.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Model\\\\ProductVariant\\:\\:getShippingCost\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Model/ProductVariant.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Provider\\\\ProductVariantProvider\\:\\:addLegacyProperties\\(\\) has parameter \\$productVariantData with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Provider/ProductVariantProvider.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Provider\\\\ProductVariantProvider\\:\\:fetchOptions\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Provider/ProductVariantProvider.php

		-
			message: "#^Property App\\\\Feed\\\\ProductVariant\\\\Provider\\\\ProductVariantProvider\\:\\:\\$exchangeRates type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Provider/ProductVariantProvider.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\Legacy\\\\DescriptionNormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/DescriptionNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\Legacy\\\\DescriptionNormalizer\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/DescriptionNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\Legacy\\\\DescriptionNormalizer\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/DescriptionNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\Legacy\\\\ProductNormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/ProductNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\Legacy\\\\ProductNormalizer\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/ProductNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\Legacy\\\\ProductNormalizer\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/ProductNormalizer.php

		-
			message: "#^PHPDoc tag @var for variable \\$data has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/Legacy/ProductNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\PriceNormalizer\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/PriceNormalizer.php

		-
			message: "#^Method App\\\\Feed\\\\ProductVariant\\\\Serializer\\\\PriceNormalizer\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Feed/ProductVariant/Serializer/PriceNormalizer.php

		-
			message: "#^Method App\\\\Finance\\\\Statistics\\\\FinancialExport\\:\\:addHighestCompletedOrLastPaymentToResult\\(\\) has parameter \\$row with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^Method App\\\\Finance\\\\Statistics\\\\FinancialExport\\:\\:addHighestCompletedOrLastPaymentToResult\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^Method App\\\\Finance\\\\Statistics\\\\FinancialExport\\:\\:addOrderItemsToResult\\(\\) has parameter \\$row with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^Method App\\\\Finance\\\\Statistics\\\\FinancialExport\\:\\:addOrderItemsToResult\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^Method App\\\\Finance\\\\Statistics\\\\FinancialExport\\:\\:addToCsv\\(\\) has parameter \\$row with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^PHPDoc tag @var for variable \\$orderItems has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^PHPDoc tag @var for variable \\$payment has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Finance/Statistics/FinancialExport.php

		-
			message: "#^Property App\\\\FraudCheck\\\\CompositeFraudChecker\\:\\:\\$markReasons type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/FraudCheck/CompositeFraudChecker.php

		-
			message: "#^Method App\\\\Logging\\\\Monolog\\\\Processor\\\\TagsMonologProcessor\\:\\:addChannelInfoToContext\\(\\) has parameter \\$record with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Monolog/Processor/TagsMonologProcessor.php

		-
			message: "#^Method App\\\\Logging\\\\Monolog\\\\Processor\\\\TagsMonologProcessor\\:\\:addCustomerInfoToContext\\(\\) has parameter \\$record with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Monolog/Processor/TagsMonologProcessor.php

		-
			message: "#^Method App\\\\Logging\\\\Monolog\\\\Processor\\\\TagsMonologProcessor\\:\\:addOrderInfoToContext\\(\\) has parameter \\$record with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Monolog/Processor/TagsMonologProcessor.php

		-
			message: "#^Method App\\\\Logging\\\\Sentry\\\\EventSubscriber\\\\CommandTracingSubscriber\\:\\:__construct\\(\\) has parameter \\$configuredCommands with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Sentry/EventSubscriber/CommandTracingSubscriber.php

		-
			message: "#^Property App\\\\Logging\\\\Sentry\\\\EventSubscriber\\\\CommandTracingSubscriber\\:\\:\\$checkIns type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Sentry/EventSubscriber/CommandTracingSubscriber.php

		-
			message: "#^Method App\\\\Logging\\\\Sentry\\\\SensitiveDataScrubber\\:\\:__construct\\(\\) has parameter \\$sensitiveVarNames with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Sentry/SensitiveDataScrubber.php

		-
			message: "#^Method App\\\\Logging\\\\Sentry\\\\SensitiveDataScrubber\\:\\:getSensitiveValues\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Sentry/SensitiveDataScrubber.php

		-
			message: "#^PHPDoc tag @var for variable \\$vars has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Logging/Sentry/SensitiveDataScrubber.php

		-
			message: "#^Method App\\\\Messenger\\\\Message\\\\ForgetAccount\\:\\:getCustomerPoolCodes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Messenger/Message/ForgetAccount.php

		-
			message: "#^Property App\\\\Messenger\\\\Message\\\\ForgetAccount\\:\\:\\$customerPoolCodes type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Messenger/Message/ForgetAccount.php

		-
			message: "#^Method App\\\\Mollie\\\\Payum\\\\OrderAPI\\\\Request\\\\Model\\\\Order\\:\\:__construct\\(\\) has parameter \\$metadata with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Mollie/Payum/OrderAPI/Request/Model/Order.php

		-
			message: "#^Method App\\\\Mollie\\\\Payum\\\\OrderAPI\\\\Request\\\\Model\\\\Order\\:\\:__construct\\(\\) has parameter \\$payment with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Mollie/Payum/OrderAPI/Request/Model/Order.php

		-
			message: "#^Method App\\\\Mollie\\\\Payum\\\\OrderAPI\\\\Request\\\\Model\\\\OrderLine\\:\\:__construct\\(\\) has parameter \\$metadata with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Mollie/Payum/OrderAPI/Request/Model/OrderLine.php

		-
			message: "#^Method App\\\\Mollie\\\\Payum\\\\PaymentsAPI\\\\CreatePaymentFactory\\:\\:normalizePayment\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Mollie/Payum/PaymentsAPI/CreatePaymentFactory.php

		-
			message: "#^Method App\\\\Mollie\\\\Payum\\\\PaymentsAPI\\\\Model\\\\MolliePayment\\:\\:__construct\\(\\) has parameter \\$metadata with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Mollie/Payum/PaymentsAPI/Model/MolliePayment.php

		-
			message: "#^Method App\\\\Mollie\\\\Payum\\\\PaymentsAPI\\\\Model\\\\MolliePayment\\:\\:__construct\\(\\) has parameter \\$method with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Mollie/Payum/PaymentsAPI/Model/MolliePayment.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/OrderProcessing/OrderPaymentProcessor.php

		-
			message: "#^Method App\\\\Promotion\\\\Action\\\\PercentageDiscountWithExcludedProductsPromotionActionCommand\\:\\:execute\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Action/PercentageDiscountWithExcludedProductsPromotionActionCommand.php

		-
			message: "#^Method App\\\\Promotion\\\\Action\\\\PercentageDiscountWithExcludedProductsPromotionActionCommand\\:\\:isConfigurationValid\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Action/PercentageDiscountWithExcludedProductsPromotionActionCommand.php

		-
			message: "#^Method App\\\\Promotion\\\\Action\\\\PercentageDiscountWithExcludedProductsPromotionActionCommand\\:\\:validate\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Action/PercentageDiscountWithExcludedProductsPromotionActionCommand.php

		-
			message: "#^Method App\\\\Promotion\\\\Checker\\\\Rule\\\\CartItemContainsAttributeRuleChecker\\:\\:isEligible\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Checker/Rule/CartItemContainsAttributeRuleChecker.php

		-
			message: "#^Method App\\\\Promotion\\\\Checker\\\\Rule\\\\CartQuantityItemTypeRuleChecker\\:\\:isEligible\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Checker/Rule/CartQuantityItemTypeRuleChecker.php

		-
			message: "#^Method App\\\\Promotion\\\\Filter\\\\ProductTypeFilter\\:\\:filter\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Filter/ProductTypeFilter.php

		-
			message: "#^Method App\\\\Promotion\\\\Filter\\\\ProductTypeFilter\\:\\:isConfigured\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Filter/ProductTypeFilter.php

		-
			message: "#^Method App\\\\Promotion\\\\Filter\\\\ProductTypeFilter\\:\\:itemIsEligible\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Filter/ProductTypeFilter.php

		-
			message: "#^Method App\\\\Promotion\\\\Promotion\\\\Filter\\\\ExcludedProductsFilter\\:\\:filter\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Promotion/Promotion/Filter/ExcludedProductsFilter.php

		-
			message: "#^Method App\\\\Repository\\\\FraudProductRepository\\:\\:matchProduct\\(\\) has parameter \\$productCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/FraudProductRepository.php

		-
			message: "#^Method App\\\\Repository\\\\FraudProductRepositoryInterface\\:\\:matchProduct\\(\\) has parameter \\$productCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/FraudProductRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\OrderItemUnitRepositoryInterface\\:\\:findByShipment\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/OrderItemUnitRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\OrderRepository\\:\\:countOrders\\(\\) has parameter \\$states with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/OrderRepository.php

		-
			message: "#^Method App\\\\Repository\\\\OrderRepository\\:\\:findOrdersReadyForApprovalBySpecialistOrReadyForShipment\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/OrderRepository.php

		-
			message: "#^Method App\\\\Repository\\\\OrderRepositoryInterface\\:\\:countOrders\\(\\) has parameter \\$states with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/OrderRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\OrderRepositoryInterface\\:\\:findByTimeAgo\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/OrderRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\OrderRepositoryInterface\\:\\:findOrdersReadyForApprovalBySpecialistOrReadyForShipment\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/OrderRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\PaymentMethodRepository\\:\\:findEnabledForChannel\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/PaymentMethodRepository.php

		-
			message: "#^Method App\\\\Repository\\\\ProductRepository\\:\\:addFilterToExcludeByCodes\\(\\) has parameter \\$excludedCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/ProductRepository.php

		-
			message: "#^Method App\\\\Repository\\\\ProductRepository\\:\\:findEnabledByType\\(\\) has parameter \\$excludedCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/ProductRepository.php

		-
			message: "#^Method App\\\\Repository\\\\ProductRepository\\:\\:getAssociatedMedicationProductIdsByConsultCode\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/ProductRepository.php

		-
			message: "#^Method App\\\\Repository\\\\ProductRepository\\:\\:getProductsByCodesQueryBuilder\\(\\) has parameter \\$productCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/ProductRepository.php

		-
			message: "#^Method App\\\\Repository\\\\ProductVariantRepository\\:\\:findAllEnabledByProductAndCountry\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/ProductVariantRepository.php

		-
			message: "#^Method App\\\\Repository\\\\ProductVariantRepositoryInterface\\:\\:findAllEnabledByProductAndCountry\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/ProductVariantRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\SupplierRepositoryInterface\\:\\:findOneBy\\(\\) has parameter \\$criteria with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SupplierRepositoryInterface.php

		-
			message: "#^Method App\\\\Repository\\\\SupplierRepositoryInterface\\:\\:findOneBy\\(\\) has parameter \\$orderBy with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SupplierRepositoryInterface.php

		-
			message: "#^Method App\\\\Security\\\\UserProvider\\\\JwtAdminUserProvider\\:\\:createNewUser\\(\\) has parameter \\$roles with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/UserProvider/JwtAdminUserProvider.php

		-
			message: "#^Method App\\\\Security\\\\UserProvider\\\\UsernameAndCustomerPoolProvider\\:\\:loadUserByIdentifierAndPayload\\(\\) has parameter \\$payload with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/UserProvider/UsernameAndCustomerPoolProvider.php

		-
			message: "#^Method App\\\\Security\\\\UserProvider\\\\UsernameAndCustomerPoolProvider\\:\\:loadUserByUsernameAndPayload\\(\\) has parameter \\$payload with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/UserProvider/UsernameAndCustomerPoolProvider.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\AbstractCustomField\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/AbstractCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\AbstractCustomField\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/AbstractCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\AbstractCustomField\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/AbstractCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Channel\\\\MultipleConsultsAllowed\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Channel/MultipleConsultsAllowed.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Channel\\\\MultipleConsultsAllowed\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Channel/MultipleConsultsAllowed.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Channel\\\\MultipleConsultsAllowed\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Channel/MultipleConsultsAllowed.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Country\\\\Name\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Country/Name.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Country\\\\Name\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Country/Name.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Country\\\\Name\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Country/Name.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\CustomFieldInterface\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/CustomFieldInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\CustomFieldInterface\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/CustomFieldInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\CustomFieldInterface\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/CustomFieldInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\AccountCompletionToken\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/AccountCompletionToken.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\AccountCompletionToken\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/AccountCompletionToken.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\AccountCompletionToken\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/AccountCompletionToken.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\Birthday\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/Birthday.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\Birthday\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/Birthday.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\Birthday\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/Birthday.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\CountryCode\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/CountryCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\CountryCode\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/CountryCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\CountryCode\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/CountryCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\LastOrderAt\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/LastOrderAt.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\LastOrderAt\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/LastOrderAt.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\LastOrderAt\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/LastOrderAt.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\LocaleCode\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/LocaleCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\LocaleCode\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/LocaleCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\LocaleCode\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/LocaleCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\MarketingSubscriptionUuid\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/MarketingSubscriptionUuid.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\MarketingSubscriptionUuid\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/MarketingSubscriptionUuid.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\MarketingSubscriptionUuid\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/MarketingSubscriptionUuid.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderCount\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderCount.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderCount\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderCount.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderCount\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderCount.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValue\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderValue.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValue\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderValue.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValue\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderValue.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValueCurrencyCode\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderValueCurrencyCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValueCurrencyCode\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderValueCurrencyCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValueCurrencyCode\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Customer/TotalOrderValueCurrencyCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\AbstractOptInEmail\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/AbstractOptInEmail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\AbstractOptInEmail\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/AbstractOptInEmail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\AbstractOptInEmail\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/AbstractOptInEmail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\MarketingSubscriptionUuid\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/MarketingSubscriptionUuid.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\MarketingSubscriptionUuid\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/MarketingSubscriptionUuid.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\MarketingSubscriptionUuid\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/MarketingSubscriptionUuid.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInDirectMail\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInDirectMail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInDirectMail\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInDirectMail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInDirectMail\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInDirectMail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInDirectMailSubscription\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInDirectMailSubscription.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInDirectMailSubscription\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInDirectMailSubscription.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInDirectMailSubscription\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInDirectMailSubscription.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInEmail\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInEmail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInEmail\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInEmail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInEmail\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInEmail.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInSms\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInSms.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInSms\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInSms.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInSms\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInSms.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInSmsSubscription\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInSmsSubscription.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInSmsSubscription\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInSmsSubscription.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\EmbeddedInCustomerAsWebhook\\\\OptInSmsSubscription\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/MarketingSubscription/EmbeddedInCustomerAsWebhook/OptInSmsSubscription.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Cancellation\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Cancellation.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Cancellation\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Cancellation.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Cancellation\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Cancellation.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\CancelledBy\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/CancelledBy.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\CancelledBy\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/CancelledBy.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\CancelledBy\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/CancelledBy.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\MedicalQuestionnaire\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/MedicalQuestionnaire.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\MedicalQuestionnaire\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/MedicalQuestionnaire.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\MedicalQuestionnaire\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/MedicalQuestionnaire.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Number\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Number.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Number\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Number.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Number\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Number.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:appendModificationToItem\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:appendModificationToItem\\(\\) has parameter \\$item with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:normalizeItem\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:normalizeItem\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:normalizePreferredItem\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:normalizePreferredItem\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\OrderItems\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/OrderItems.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethod\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethod.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethod\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethod.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethod\\:\\:getLastPayment\\(\\) has parameter \\$excludeStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethod.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethod\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethod.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodName\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodName\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodName\\:\\:getLastPayment\\(\\) has parameter \\$excludeStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodName\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/PaymentMethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Shipments\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Shipments.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Shipments\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Shipments.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Shipments\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Shipments.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Subtotal\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Subtotal.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Subtotal\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Subtotal.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Order\\\\Subtotal\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Order/Subtotal.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\OrderItem\\\\MainTaxon\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/OrderItem/MainTaxon.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\OrderItem\\\\MainTaxon\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/OrderItem/MainTaxon.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\OrderItem\\\\MainTaxon\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/OrderItem/MainTaxon.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\PasswordResetToken\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/PasswordResetToken.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\PasswordResetToken\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/PasswordResetToken.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\PasswordResetToken\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/PasswordResetToken.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Payment\\\\Details\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Payment/Details.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Payment\\\\Details\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Payment/Details.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Payment\\\\Details\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Payment/Details.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Associations\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Associations.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Associations\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Associations.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Associations\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Associations.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Attributes\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Attributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Attributes\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Attributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Attributes\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Attributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Channel\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Channel.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Channel\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Channel.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Channel\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Channel.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\EnabledForChannels\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/EnabledForChannels.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\EnabledForChannels\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/EnabledForChannels.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\EnabledForChannels\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/EnabledForChannels.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\InStock\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/InStock.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\InStock\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/InStock.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\InStock\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/InStock.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Options\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Options.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Options\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Options.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Options\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Options.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPrice\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/StartingPrice.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPrice\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/StartingPrice.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPrice\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/StartingPrice.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPrices\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/StartingPrices.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPrices\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/StartingPrices.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPrices\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/StartingPrices.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Taxons\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Taxons.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Taxons\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Taxons.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Taxons\\:\\:addProductTaxonChannels\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Taxons.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Taxons\\:\\:addProductTaxonChannels\\(\\) has parameter \\$normalizedTaxon with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Taxons.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Taxons\\:\\:addProductTaxonChannels\\(\\) has parameter \\$productTaxonChannelAttributes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Taxons.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Taxons\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Taxons.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Util\\\\OptionsNaturalSorter\\:\\:createOptionCodeParts\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Util/OptionsNaturalSorter.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Util\\\\OptionsNaturalSorter\\:\\:sort\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Util/OptionsNaturalSorter.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Util\\\\ProductAttributesUtil\\:\\:normalizeProductAttributes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Util/ProductAttributesUtil.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Variants\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Variants.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Variants\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Variants.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Product\\\\Variants\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Product/Variants.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Attributes\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Attributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Attributes\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Attributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Attributes\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Attributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Code\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Code.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Code\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Code.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Code\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Code.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\DefaultUsageAdvice\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/DefaultUsageAdvice.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\DefaultUsageAdvice\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/DefaultUsageAdvice.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\DefaultUsageAdvice\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/DefaultUsageAdvice.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Embedded\\\\AbstractEmbeddedCustomField\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Embedded/AbstractEmbeddedCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Embedded\\\\Product\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Embedded/Product.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Embedded\\\\Product\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Embedded/Product.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Image\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Image.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Image\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Image.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Image\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Image.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\OptionValues\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/OptionValues.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\OptionValues\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/OptionValues.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\OptionValues\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/OptionValues.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Price\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Price.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Price\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Price.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Price\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/Price.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\ProductAttributes\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/ProductAttributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\ProductAttributes\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/ProductAttributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\ProductAttributes\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/ProductVariant/ProductAttributes.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\Items\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/Items.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\Items\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/Items.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\Items\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/Items.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\MethodName\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/MethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\MethodName\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/MethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\MethodName\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/MethodName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\SupplierName\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/SupplierName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\SupplierName\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/SupplierName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\SupplierName\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/SupplierName.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackAndTraceLink\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/TrackAndTraceLink.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackAndTraceLink\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/TrackAndTraceLink.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackAndTraceLink\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/TrackAndTraceLink.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackingCode\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/TrackingCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackingCode\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/TrackingCode.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackingCode\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/Shipment/TrackingCode.php

		-
			message: "#^Method App\\\\Serializer\\\\ObjectNormalizer\\:\\:convertBooleanToHumanReadable\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ObjectNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\ObjectNormalizer\\:\\:convertBooleanToHumanReadable\\(\\) has parameter \\$humanReadableBooleans with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ObjectNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\ObjectNormalizer\\:\\:getSupportedTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ObjectNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\ObjectNormalizer\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ObjectNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\ObjectNormalizer\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ObjectNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\Prepr\\\\TaxonList\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/Prepr/TaxonList.php

		-
			message: "#^Method App\\\\Serializer\\\\Prepr\\\\TaxonList\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/Prepr/TaxonList.php

		-
			message: "#^Method App\\\\Serializer\\\\ProductOptionValueTranslationNormalizer\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ProductOptionValueTranslationNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\ProductOptionValueTranslationNormalizer\\:\\:supportsNormalization\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ProductOptionValueTranslationNormalizer.php

		-
			message: "#^PHPDoc tag @var for variable \\$data has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ProductOptionValueTranslationNormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializer\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/Webhook/OpenApiWebhookPayloadSerializer.php

		-
			message: "#^Method App\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializer\\:\\:serialize\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/Webhook/OpenApiWebhookPayloadSerializer.php

		-
			message: "#^Method App\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializerInterface\\:\\:serialize\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/Webhook/OpenApiWebhookPayloadSerializerInterface.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:loadChannelCountries\\(\\) has parameter \\$countryCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:loadChannelCurrencies\\(\\) has parameter \\$currencyCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:loadChannelLocales\\(\\) has parameter \\$localeCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:loadChannelProperties\\(\\) has parameter \\$channelConfiguration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:loadChannelShopBillingData\\(\\) has parameter \\$shopBillingDataConfiguration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:sortShopConfigurationArrayMedicationChannelsFirst\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ChannelConfigurationLoader\\:\\:sortShopConfigurationArrayMedicationChannelsFirst\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ChannelConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ProductOptionConfigurationLoader\\:\\:addOrUpdateProductOptionValues\\(\\) has parameter \\$values with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ProductOptionConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ProductOptionConfigurationLoader\\:\\:updateProductOptionValue\\(\\) has parameter \\$value with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ProductOptionConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ShippingMethodConfigurationLoader\\:\\:loadShippingMethodChannels\\(\\) has parameter \\$channelCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ShippingMethodConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\ShippingMethodConfigurationLoader\\:\\:setShippingMethodConfiguration\\(\\) has parameter \\$channels with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/ShippingMethodConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\TermQuestionConfigurationLoader\\:\\:createOrUpdateTermQuestions\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/TermQuestionConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\TermQuestionConfigurationLoader\\:\\:disableRemovedTermQuestions\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/TermQuestionConfigurationLoader.php

		-
			message: "#^Method App\\\\ShopConfiguration\\\\TermQuestionConfigurationLoader\\:\\:removeRemovedTranslations\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/ShopConfiguration/TermQuestionConfigurationLoader.php

		-
			message: "#^Method App\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuard\\:\\:allShipmentsAreState\\(\\) has parameter \\$filterShippingStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/StateMachine/Guard/OrderShippingStateGuard.php

		-
			message: "#^Method App\\\\Supplier\\\\Dto\\\\Order\\:\\:__construct\\(\\) has parameter \\$orderItems with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Supplier/Dto/Order.php

		-
			message: "#^Method App\\\\Supplier\\\\Dto\\\\Order\\:\\:getFilteredOrderItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Supplier/Dto/Order.php

		-
			message: "#^Method App\\\\Supplier\\\\Dto\\\\Order\\:\\:getOrderItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Supplier/Dto/Order.php

		-
			message: "#^Method App\\\\TrackAndTrace\\\\TrackAndTraceLinkResolver\\:\\:getParameterKeys\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/TrackAndTrace/TrackAndTraceLinkResolver.php

		-
			message: "#^Method App\\\\TrackAndTrace\\\\TrackAndTraceLinkResolver\\:\\:resolveTrackAndTraceLink\\(\\) has parameter \\$parameters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/TrackAndTrace/TrackAndTraceLinkResolver.php

		-
			message: "#^Method class@anonymous/src/Twig/Extension/EnumExtension\\.php\\:29\\:\\:__call\\(\\) has parameter \\$arguments with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Twig/Extension/EnumExtension.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Controller\\\\FraudCheck\\\\ApproveAndHoldControllerTest\\:\\:provideArguments\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Admin/Controller/FraudCheck/ApproveAndHoldControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Admin\\\\Controller\\\\FraudCheck\\\\ApproveAndHoldControllerTest\\:\\:\\$auditLogger with generic interface App\\\\Auditing\\\\AuditLoggerInterface does not specify its types\\: T$#"
			count: 1
			path: tests/Admin/Controller/FraudCheck/ApproveAndHoldControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Admin\\\\Controller\\\\FraudCheck\\\\CancelControllerTest\\:\\:\\$auditLogger with generic interface App\\\\Auditing\\\\AuditLoggerInterface does not specify its types\\: T$#"
			count: 1
			path: tests/Admin/Controller/FraudCheck/CancelControllerTest.php

		-
			message: "#^Call to an undefined method Symfony\\\\Component\\\\HttpFoundation\\\\RequestStack\\:\\:expects\\(\\)\\.$#"
			count: 1
			path: tests/Admin/Controller/RefundPayment/UpdateRefundPaymentStateControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\EventSubscriber\\\\CompletePaymentEventSubscriberTest\\:\\:invalidDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Admin/EventSubscriber/CompletePaymentEventSubscriberTest.php

		-
			message: "#^Parameter \\#1 \\$exception of method PHPUnit\\\\Framework\\\\TestCase\\:\\:expectException\\(\\) expects class\\-string\\<Throwable\\>, string given\\.$#"
			count: 1
			path: tests/Admin/EventSubscriber/CompletePaymentEventSubscriberTest.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Admin\\\\EventSubscriber\\\\UserTimeZoneEventSubscriberTest\\:\\:\\$subscriber\\.$#"
			count: 5
			path: tests/Admin/EventSubscriber/UserTimeZoneEventSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Grid\\\\Filter\\\\UserDateTimeZoneFilterTest\\:\\:createData\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Grid/Filter/UserDateTimeZoneFilterTest.php

		-
			message: "#^Parameter \\#4 \\$orderPaymentStateResolver of class App\\\\Admin\\\\MessageHandler\\\\Order\\\\RemoveOrderItemHandler constructor expects App\\\\StateMachine\\\\StateResolver\\\\OrderPaymentStateResolver, PHPUnit\\\\Framework\\\\MockObject\\\\MockObject&Sylius\\\\Component\\\\Order\\\\StateResolver\\\\StateResolverInterface given\\.$#"
			count: 1
			path: tests/Admin/MessageHandler/Order/RemoveOrderItemHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Checker\\\\Rule\\\\CartItemContainsAttributeRuleCheckerTest\\:\\:configurationDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Admin/Promotion/Checker/Rule/CartItemContainsAttributeRuleCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Checker\\\\Rule\\\\CartItemContainsAttributeRuleCheckerTest\\:\\:createOrderItem\\(\\) has parameter \\$attributes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Promotion/Checker/Rule/CartItemContainsAttributeRuleCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Checker\\\\Rule\\\\CartItemContainsAttributeRuleCheckerTest\\:\\:testIsEligible\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Promotion/Checker/Rule/CartItemContainsAttributeRuleCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Checker\\\\Rule\\\\CartQuantityItemTypeRuleCheckerTest\\:\\:configurationProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Admin/Promotion/Checker/Rule/CartQuantityItemTypeRuleCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Checker\\\\Rule\\\\CartQuantityItemTypeRuleCheckerTest\\:\\:testItCanDetermineIfOrderIsEligible\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Promotion/Checker/Rule/CartQuantityItemTypeRuleCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Checker\\\\Rule\\\\CartQuantityItemTypeRuleCheckerTest\\:\\:testItCanDetermineIfOrderIsEligible\\(\\) has parameter \\$expectedResult with no type specified\\.$#"
			count: 1
			path: tests/Admin/Promotion/Checker/Rule/CartQuantityItemTypeRuleCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Filter\\\\ProductTypeFilterTest\\:\\:configurationProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Admin/Promotion/Filter/ProductTypeFilterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Filter\\\\ProductTypeFilterTest\\:\\:createOrderItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Promotion/Filter/ProductTypeFilterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Promotion\\\\Filter\\\\ProductTypeFilterTest\\:\\:testItCanFilterOrderItems\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Promotion/Filter/ProductTypeFilterTest.php

		-
			message: "#^Property App\\\\Tests\\\\Admin\\\\Promotion\\\\Filter\\\\ProductTypeFilterTest\\:\\:\\$orderItems type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Admin/Promotion/Filter/ProductTypeFilterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Admin\\\\Resolver\\\\UserTimeZoneResolverTest\\:\\:userReturnValueProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Admin/Resolver/UserTimeZoneResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Affiliate\\\\EventSubscriber\\\\OrderEventSubscriberTest\\:\\:getOrderEvent\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Affiliate/EventSubscriber/OrderEventSubscriberTest.php

		-
			message: "#^Call to an undefined method Symfony\\\\Component\\\\Serializer\\\\SerializerInterface\\:\\:method\\(\\)\\.$#"
			count: 2
			path: tests/Affiliate/Partnerize/Client/ClientTest.php

		-
			message: "#^Cannot call method isOfType\\(\\) on App\\\\Entity\\\\Product\\\\ProductInterface\\|null\\.$#"
			count: 1
			path: tests/Affiliate/Partnerize/Encoder/ConversionUriEncoderTest.php

		-
			message: "#^Cannot call method getAccountCompletionTokenRequestedAt\\(\\) on Sylius\\\\Component\\\\User\\\\Model\\\\UserInterface\\|null\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Account/Password/ResetPasswordHandlerTest.php

		-
			message: "#^Cannot call method getPasswordResetToken\\(\\) on Sylius\\\\Component\\\\User\\\\Model\\\\UserInterface\\|null\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Account/Password/ResetPasswordHandlerTest.php

		-
			message: "#^Parameter \\#2 \\$shopUserFactory of class App\\\\Api\\\\CommandHandler\\\\Account\\\\RegisterCustomerHandler constructor expects Sylius\\\\Resource\\\\Factory\\\\Factory, PHPUnit\\\\Framework\\\\MockObject\\\\MockObject&Sylius\\\\Resource\\\\Factory\\\\FactoryInterface given\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Account/RegisterCustomerHandlerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Api\\\\CommandHandler\\\\Account\\\\RegisterCustomerHandlerTest\\:\\:\\$shopUserFactory with generic interface Sylius\\\\Resource\\\\Factory\\\\FactoryInterface does not specify its types\\: T$#"
			count: 1
			path: tests/Api/CommandHandler/Account/RegisterCustomerHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$subscribedToNewsletter of method Sylius\\\\Component\\\\Customer\\\\Model\\\\Customer\\:\\:setSubscribedToNewsletter\\(\\) expects bool, bool\\|null given\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Account/UpdateAccountInformationHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$decoratedFactory of class Sylius\\\\Component\\\\Core\\\\Factory\\\\AddressFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\<T of Sylius\\\\Component\\\\Core\\\\Model\\\\AddressInterface\\>, Sylius\\\\Resource\\\\Factory\\\\Factory given\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Address/CreateAddressHandlerTest.php

		-
			message: "#^Cannot call method getCode\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariantInterface\\|null\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandlerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandlerTest.php

		-
			message: "#^Cannot call method getVariant\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$p of method Doctrine\\\\Common\\\\Collections\\\\Collection\\<\\(int\\|string\\),App\\\\Entity\\\\Order\\\\OrderItem\\>\\:\\:filter\\(\\) expects Closure\\(App\\\\Entity\\\\Order\\\\OrderItem, int\\|string\\)\\: bool, Closure\\(App\\\\Entity\\\\Order\\\\OrderItem\\)\\: \\(bool\\|null\\) given\\.$#"
			count: 2
			path: tests/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandlerTest.php

		-
			message: "#^Parameter \\#2 \\$productVariantCode of class App\\\\Api\\\\Command\\\\Admin\\\\Order\\\\RemoveOrderItemOperation constructor expects string, string\\|null given\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Admin/Order/RemoveOrderItemOperationHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$transitionPrescriptionState of callable App\\\\Api\\\\CommandHandler\\\\Admin\\\\Order\\\\TransitionPrescriptionStateHandler expects App\\\\Api\\\\Command\\\\Admin\\\\Order\\\\TransitionPrescriptionState\\\\AbstractTransition, App\\\\Api\\\\Command\\\\OrderPrescriptionStateTransitionInterface given\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Admin/TransitionPrescriptionStateHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsHandlerTest\\:\\:testInvokeAppliesCorrectCheckoutState\\(\\) has parameter \\$addItems with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:createAddItems\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:dataProviderAddItemsTransitionsFromCartState\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:dataProviderTransitionForDeliveryServiceSelected\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:dataProviderTransitionForPreferredProductsSelected\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:dataProviderTransitionForReset\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:mockGetProductVariant\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:testAddItemsTransitionsFromCartState\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:testTransition\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:testTransition\\(\\) is unused\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:testTransitionForDeliveryServiceSelected\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:testTransitionForPreferredProductsSelected\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\AddItemsTransitionsTest\\:\\:testTransitionForReset\\(\\) has parameter \\$items with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 4
			path: tests/Api/CommandHandler/Cart/AddItemsTransitionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Cart\\\\RemoveItemHandlerTest\\:\\:dataProviderResetCheckoutState\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Cart/RemoveItemHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$decoratedFactory of class Sylius\\\\Component\\\\Core\\\\Factory\\\\AddressFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\<T of Sylius\\\\Component\\\\Core\\\\Model\\\\AddressInterface\\>, Sylius\\\\Resource\\\\Factory\\\\Factory given\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Checkout/Address/SetBillingAndShippingAddressHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Notify\\\\AbstractConsultRequestStatusHandlerTest\\:\\:getOrder\\(\\) has parameter \\$orderData with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Notify/AbstractConsultRequestStatusHandlerTest.php

		-
			message: "#^Cannot call method getReason\\(\\) on Sylius\\\\Component\\\\Core\\\\Model\\\\ShipmentInterface\\|null\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Notify/OrderShipmentStatusHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\CommandHandler\\\\Notify\\\\OrderShipmentStatusHandlerTest\\:\\:provideOrderShipmentStatus\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Notify/OrderShipmentStatusHandlerTest.php

		-
			message: "#^Using nullsafe method call on non\\-nullable type Superbrave\\\\PharmacyServiceClient\\\\Model\\\\Response\\\\Shipment\\. Use \\-\\> instead\\.$#"
			count: 1
			path: tests/Api/CommandHandler/Notify/OrderShipmentStatusHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\EventSubscriber\\\\DeserializedCommand\\\\CommunicationMessageAwareSubscriberTest\\:\\:stateProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Api/EventSubscriber/DeserializedCommand/CommunicationMessageAwareSubscriberTest.php

		-
			message: "#^Property Symfony\\\\Component\\\\HttpFoundation\\\\Request\\:\\:\\$query \\(Symfony\\\\Component\\\\HttpFoundation\\\\InputBag\\<string\\>\\) does not accept PHPUnit\\\\Framework\\\\MockObject\\\\MockObject&Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag\\.$#"
			count: 3
			path: tests/Api/Response/PaginationTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\AbstractConstraintValidatorTestCase extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/AbstractConstraintValidatorTestCase.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\AllowedStateValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/AllowedStateValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\AnamnesisQuestionnaireExistsValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/AnamnesisQuestionnaireExistsValidatorTest.php

		-
			message: "#^Generator expects value type array\\{App\\\\Entity\\\\Order\\\\Order, string\\}, array\\{App\\\\Entity\\\\Order\\\\Order, null\\} given\\.$#"
			count: 1
			path: tests/Api/Validator/Approve/DisallowMultipleSuppliersValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\CanTransitionStateValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/CanTransitionStateValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ChannelAllowedForShippingAddressValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ChannelAllowedForShippingAddressValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ChannelCodeExistsValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ChannelCodeExistsValidatorTest.php

		-
			message: "#^Cannot call method getVariant\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Api/Validator/ConsultRequiredValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ConsultRequiredValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ConsultRequiredValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\CountryCodeExistsValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/CountryCodeExistsValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\CouponCodeAllowedValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/CouponCodeAllowedValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\DisallowedStateValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/DisallowedStateValidatorTest.php

		-
			message: "#^Access to an undefined property Symfony\\\\Component\\\\Validator\\\\Constraint\\:\\:\\$message\\.$#"
			count: 1
			path: tests/Api/Validator/DoctorSupplierIsEnabledValidatorTest.php

		-
			message: "#^Call to an undefined method App\\\\Repository\\\\SupplierDoctorRepository\\:\\:expects\\(\\)\\.$#"
			count: 3
			path: tests/Api/Validator/DoctorSupplierIsEnabledValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\IsValidCaseInBackedEnumValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/IsValidCaseInBackedEnumValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\LocaleCodeExistsValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/LocaleCodeExistsValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ParentProductVariant\\\\CodeExistsInCartOrRequestValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ParentProductVariant/CodeExistsInCartOrRequestValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ParentProductVariant\\\\HasOnePreferredOrderItemValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ParentProductVariant/HasOnePreferredOrderItemValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ParentProductVariant\\\\IsValidForProductVariantCodeTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ParentProductVariant/IsValidForProductVariantCodeTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ParentProductVariant\\\\ParentIsRequiredTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ParentProductVariant/ParentIsRequiredTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\PasswordResetTokenValidValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/PasswordResetTokenValidValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\PaymentMethod\\\\AllowedForNonMatchingAddressesValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/PaymentMethod/AllowedForNonMatchingAddressesValidatorTest.php

		-
			message: "#^Method App\\\\Tests\\\\Api\\\\Validator\\\\PaymentMethod\\\\AllowedForNonMatchingAddressesValidatorTest\\:\\:getAddress\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Api/Validator/PaymentMethod/AllowedForNonMatchingAddressesValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\PaymentMethod\\\\AllowedForNonOrderTotalValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/PaymentMethod/AllowedForNonOrderTotalValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\PaymentMethod\\\\EnabledValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/PaymentMethod/EnabledValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\PaymentMethod\\\\IssuerCodeValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/PaymentMethod/IssuerCodeValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ProductIsConsultValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ProductIsConsultValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ProductVariantAvailableInChannelValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ProductVariantAvailableInChannelValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ProductVariantEnabledValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ProductVariantEnabledValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ProductVariantExistsValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ProductVariantExistsValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ProductVariantIsConsultValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ProductVariantIsConsultValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ProductVariantQuantitiesValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ProductVariantQuantitiesValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\ServiceProductAllowedValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/ServiceProductAllowedValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\StrongPasswordValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/StrongPasswordValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Api\\\\Validator\\\\UniqueEmailInCustomerPoolValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Api/Validator/UniqueEmailInCustomerPoolValidatorTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\AuditLoggerTest\\:\\:auditOrderLogDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Auditing/AuditLoggerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\AuditLoggerTest\\:\\:invalidDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Auditing/AuditLoggerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\AuditLoggerTest\\:\\:testItCanAuditLogAnOrder\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Auditing/AuditLoggerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\OrderItemAuditLoggerTest\\:\\:getUpdateItemOperationModificationDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Auditing/OrderItemAuditLoggerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\OrderItemAuditLoggerTest\\:\\:getUpdateItemOperationModificationDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Auditing/OrderItemAuditLoggerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\OrderItemAuditLoggerTest\\:\\:logUpdateItemDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Auditing/OrderItemAuditLoggerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Auditing\\\\OrderItemAuditLoggerTest\\:\\:logUpdateItemDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Auditing/OrderItemAuditLoggerTest.php

		-
			message: "#^Parameter \\#2 \\$modification of method App\\\\Auditing\\\\OrderItemAuditLogger\\:\\:logUpdateItem\\(\\) expects 'quantity'\\|'quantity_and_usage…'\\|'usageAdvice'\\|null, string given\\.$#"
			count: 1
			path: tests/Auditing/OrderItemAuditLoggerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Auditing\\\\OrderItemAuditLoggerTest\\:\\:\\$auditLoggerMock with generic interface App\\\\Auditing\\\\AuditLoggerInterface does not specify its types\\: T$#"
			count: 1
			path: tests/Auditing/OrderItemAuditLoggerTest.php



		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Behat/Api/CatalogContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Api\\\\CheckoutContext\\:\\:iRequestAvailablePaymentMethodsForCart\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Api/CheckoutContext.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 5
			path: tests/Behat/Api/CheckoutContext.php

		-
			message: "#^Using nullsafe property access on non\\-nullable type stdClass\\. Use \\-\\> instead\\.$#"
			count: 1
			path: tests/Behat/Api/CheckoutContext.php

		-
			message: "#^Class App\\\\Tests\\\\Behat\\\\Api\\\\CustomerContext has an uninitialized readonly property \\$addressCommandTransformer\\. Assign it in the constructor\\.$#"
			count: 1
			path: tests/Behat/Api/CustomerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Api\\\\CustomerContext\\:\\:getUpdateAddressBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Api/CustomerContext.php

		-
			message: "#^Parameter \\#1 \\$object of method Doctrine\\\\Persistence\\\\ObjectManager\\:\\:remove\\(\\) expects object, Sylius\\\\Component\\\\User\\\\Model\\\\UserInterface\\|null given\\.$#"
			count: 1
			path: tests/Behat/Api/CustomerContext.php

		-
			message: "#^Property App\\\\Tests\\\\Behat\\\\Api\\\\CustomerContext\\:\\:\\$addressCommandTransformer is unused\\.$#"
			count: 1
			path: tests/Behat/Api/CustomerContext.php

		-
			message: "#^Call to an undefined method object\\:\\:getOrderId\\(\\)\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Call to an undefined method object\\:\\:getSerializedPayload\\(\\)\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Call to an undefined method object\\:\\:getShipmentId\\(\\)\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Cannot call method getAffiliateConversionId\\(\\) on App\\\\Entity\\\\Order\\\\Order\\|null\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Cannot call method getAffiliateId\\(\\) on App\\\\Entity\\\\Order\\\\Order\\|null\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Cannot call method getId\\(\\) on App\\\\Entity\\\\Order\\\\Order\\|null\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Parameter \\#1 \\$class of method Zenstruck\\\\Messenger\\\\Test\\\\EnvelopeCollection\\:\\:messages\\(\\) expects class\\-string\\<object\\>\\|null, string given\\.$#"
			count: 2
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Parameter \\#1 \\$expected of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertInstanceOf\\(\\) expects class\\-string\\<object\\>, string given\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Parameter \\#1 \\$messageClass of method Zenstruck\\\\Messenger\\\\Test\\\\EnvelopeCollection\\:\\:assertNotContains\\(\\) expects class\\-string, string given\\.$#"
			count: 1
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Unable to resolve the template type T in call to method Zenstruck\\\\Messenger\\\\Test\\\\EnvelopeCollection\\:\\:messages\\(\\)$#"
			count: 2
			path: tests/Behat/Api/EventQueueContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Api\\\\MarketingSubscriptionContext\\:\\:theMarketingSubscriptionShouldHaveAPropertyWithValue\\(\\) has parameter \\$expectedValue with no type specified\\.$#"
			count: 1
			path: tests/Behat/Api/MarketingSubscriptionContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Api\\\\OpenApiContext\\:\\:getFilteredValidationErrors\\(\\) has parameter \\$errors with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Api/OpenApiContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Api\\\\OpenApiContext\\:\\:getFilteredValidationErrors\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Api/OpenApiContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Api\\\\ResponseContext\\:\\:getPropertyByKeyOrPropertyAccess\\(\\) has parameter \\$responseContent with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Api/ResponseContext.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Behat/Api/ResponseContext.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, \\$variable\\-\\>count\\(\\)\\)\\.$#"
			count: 1
			path: tests/Behat/Api/ResponseContext.php

		-
			message: "#^Parameter \\#2 \\$callback of function array_filter expects \\(callable\\(string\\)\\: bool\\)\\|null, Closure\\(string\\)\\: \\(0\\|1\\|false\\) given\\.$#"
			count: 1
			path: tests/Behat/AuditLogContext.php

		-
			message: "#^Match expression does not handle remaining value\\: mixed$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Catalog\\\\Messenger\\\\MessageHandlerContext\\:\\:createAttributeCollection\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Catalog\\\\Messenger\\\\MessageHandlerContext\\:\\:createChannelCollection\\(\\) has parameter \\$channels with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Catalog\\\\Messenger\\\\MessageHandlerContext\\:\\:createConsultProductCollection\\(\\) has parameter \\$consultProducts with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Catalog\\\\Messenger\\\\MessageHandlerContext\\:\\:createImageCollection\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Catalog\\\\Messenger\\\\MessageHandlerContext\\:\\:createTaxonCollection\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Catalog\\\\Messenger\\\\MessageHandlerContext\\:\\:createTranslationCollection\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Parameter \\#1 \\$productType of class App\\\\Catalog\\\\Message\\\\AttributeCollection constructor expects App\\\\Catalog\\\\Message\\\\ProductTypeAttribute, App\\\\Catalog\\\\Message\\\\Attribute given\\.$#"
			count: 1
			path: tests/Behat/Catalog/Messenger/MessageHandlerContext.php

		-
			message: "#^Cannot call method getCode\\(\\) on Sylius\\\\Component\\\\Core\\\\Model\\\\TaxonInterface\\|null\\.$#"
			count: 1
			path: tests/Behat/Catalog/ProductContext.php

		-
			message: "#^Parameter \\#1 \\$p of method Doctrine\\\\Common\\\\Collections\\\\Collection\\<\\(int\\|string\\),Sylius\\\\Resource\\\\Model\\\\TranslationInterface\\>\\:\\:filter\\(\\) expects Closure\\(Sylius\\\\Resource\\\\Model\\\\TranslationInterface, int\\|string\\)\\: bool, Closure\\(App\\\\Entity\\\\Product\\\\ProductVariantTranslationInterface\\)\\: bool given\\.$#"
			count: 1
			path: tests/Behat/Catalog/ProductVariantContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Console\\\\ConsoleCommandContext\\:\\:iExecuteTheConsoleCommand\\(\\) has parameter \\$input with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Console/ConsoleCommandContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\Console\\\\ConsoleCommandContext\\:\\:iExecuteTheConsoleCommand\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/Console/ConsoleCommandContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\ContextStorage\\:\\:__construct\\(\\) has parameter \\$parameters with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/ContextStorage.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\FraudCheckContext\\:\\:getTableRowsAsText\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/FraudCheckContext.php

		-
			message: "#^Parameter \\#1 \\$node of class Symfony\\\\Component\\\\DomCrawler\\\\Crawler constructor expects array\\|DOMNode\\|\\(DOMNodeList&iterable\\<DOMNode\\>\\)\\|string\\|null, string\\|false given\\.$#"
			count: 1
			path: tests/Behat/FraudCheckContext.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:addAccessTokenFromContextStorage\\(\\) has parameter \\$server with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:getResponse\\(\\) has parameter \\$key with no type specified\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:jsonRequest\\(\\) has parameter \\$jsonRequestBody with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:jsonRequest\\(\\) has parameter \\$server with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:request\\(\\) has parameter \\$parameters with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:request\\(\\) has parameter \\$server with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Property App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:\\$responses type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Anonymous function should return string but returns string\\|null\\.$#"
			count: 2
			path: tests/Behat/ProductContext.php

		-
			message: "#^Cannot call method setCostPrice\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariant\\|null\\.$#"
			count: 1
			path: tests/Behat/ProductContext.php

		-
			message: "#^Cannot call method setPreferredSupplier\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariant\\|null\\.$#"
			count: 1
			path: tests/Behat/ProductContext.php

		-
			message: "#^Cannot call method setPreferredVariantForMinimumDailyOrders\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariant\\|null\\.$#"
			count: 1
			path: tests/Behat/ProductContext.php

		-
			message: "#^Method App\\\\Repository\\\\ProductVariantRepositoryInterface\\:\\:findProductVariantsFilteredByRanking\\(\\) invoked with 4 parameters, 2\\-3 required\\.$#"
			count: 1
			path: tests/Behat/ProductContext.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: tests/Behat/ProductContext.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string\\|null and App\\\\Entity\\\\Product\\\\ProductType\\:\\:MEDICATION will always evaluate to false\\.$#"
			count: 1
			path: tests/Behat/ProductContext.php

		-
			message: "#^Cannot call method setResponseFactory\\(\\) on object\\|null\\.$#"
			count: 4
			path: tests/Behat/SupplierServiceContext.php

		-
			message: "#^Method App\\\\Tests\\\\CanopyDeploy\\\\EventSubscriber\\\\OrderEventSubscriberTest\\:\\:provideExcludedOrderEvents\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/CanopyDeploy/EventSubscriber/OrderEventSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\CanopyDeploy\\\\EventSubscriber\\\\OrderEventSubscriberTest\\:\\:provideOrderEvent\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/CanopyDeploy/EventSubscriber/OrderEventSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\CanopyDeploy\\\\EventSubscriber\\\\OrderEventSubscriberTest\\:\\:provideOrderEventWithoutCustomer\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/CanopyDeploy/EventSubscriber/OrderEventSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\CanopyDeploy\\\\RetryStrategyTest\\:\\:provideDelay\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/CanopyDeploy/RetryStrategyTest.php

		-
			message: "#^Method App\\\\Tests\\\\CanopyDeploy\\\\RetryStrategyTest\\:\\:provideNotRetryable\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/CanopyDeploy/RetryStrategyTest.php

		-
			message: "#^Method App\\\\Tests\\\\CanopyDeploy\\\\RetryStrategyTest\\:\\:provideRetryable\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/CanopyDeploy/RetryStrategyTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Attachment\\\\HasherTest\\:\\:hasherDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Catalog/Attachment/HasherTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Loader\\\\Katana\\\\Client\\\\FileDownloaderTest\\:\\:supportsDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Client/FileDownloaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Loader\\\\Katana\\\\Client\\\\FileDownloaderTest\\:\\:supportsDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Client/FileDownloaderTest.php

		-
			message: "#^Parameter \\#1 \\$body of class Symfony\\\\Component\\\\HttpClient\\\\Response\\\\MockResponse constructor expects iterable\\<string\\|Throwable\\>\\|string, string\\|false given\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Client/FileDownloaderTest.php

		-
			message: "#^Parameter \\#1 \\$body of class Symfony\\\\Component\\\\HttpClient\\\\Response\\\\MockResponse constructor expects iterable\\<string\\|Throwable\\>\\|string, string\\|false given\\.$#"
			count: 8
			path: tests/Catalog/Loader/Katana/Client/KatanaClientTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertProductNormalizerTest\\:\\:getTranslations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Catalog/Loader/Katana/Normalizer/Product/UpsertProductNormalizerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\Product\\\\UpsertSimpleProductNormalizerTest\\:\\:getTranslations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Catalog/Loader/Katana/Normalizer/Product/UpsertSimpleProductNormalizerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\UpsertTaxonNormalizerTest\\:\\:getSampleItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Normalizer/UpsertTaxonNormalizerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Loader\\\\Katana\\\\Normalizer\\\\UpsertTaxonNormalizerTest\\:\\:provideSupportsDenormalizationData\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Normalizer/UpsertTaxonNormalizerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Catalog/Loader/Katana/Normalizer/UpsertTaxonNormalizerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: string$#"
			count: 1
			path: tests/Catalog/MessageHandler/AbstractUpsertProductVariantHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$factory of class Sylius\\\\Component\\\\Product\\\\Factory\\\\ProductVariantFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\<T of Sylius\\\\Component\\\\Product\\\\Model\\\\ProductVariantInterface\\>, Sylius\\\\Resource\\\\Factory\\\\Factory given\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/AbstractUpsertProductVariantHandlerTest.php

		-
			message: "#^Comparison operation \"\\>\" between 1 and 1 is always false\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: mixed$#"
			count: 2
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: non\\-falsy\\-string$#"
			count: 1
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\CreateProductVariantWithUpsertProductVariantHandlerTest\\:\\:arrangeChannels\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\CreateProductVariantWithUpsertProductVariantHandlerTest\\:\\:arrangeExpectedChannelPricing\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\CreateProductVariantWithUpsertProductVariantHandlerTest\\:\\:arrangeExpectedTranslations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\CreateProductVariantWithUpsertProductVariantHandlerTest\\:\\:createExpectedProductVariant\\(\\) has parameter \\$optionValues with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/CreateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Cannot unset offset 'leafletUrl' on array\\{id\\: 200, locale\\: 'nl', name\\: 'Viagra 25 mg 4 tabl\\.', defaultUsageAdvice\\: 'Neem 1 tablet 30…'\\}\\|array\\{id\\: 300, locale\\: 'de', name\\: 'Viagra 25 mg 4 Tabl\\.', defaultUsageAdvice\\: 'Nehmen Sie 1 Tabl\\.…'\\}\\|array\\{id\\: 555, locale\\: 'da', name\\: 'Viagra 25 mg 4 tabl\\.', defaultUsageAdvice\\: 'Tag 1 tablet 30 til…'\\}\\|array\\{locale\\: 'es', name\\: 'Viagra 25 mg 4 past\\.', defaultUsageAdvice\\: 'Tome 1 comprimido…'\\}\\|array\\{locale\\: 'fr', name\\: 'Viagra 25 mg 4…', defaultUsageAdvice\\: 'Prenez 1 comprimé…'\\}\\|array\\{locale\\: 'it', name\\: 'Viagra 25 mg 4…', defaultUsageAdvice\\: '1 compressa ogni 24…'\\}\\|array\\{locale\\: 'pl', name\\: 'Viagra 25 mg 4 tabl\\.', defaultUsageAdvice\\: 'Przyjmować 1…'\\}\\|array\\{locale\\: 'sv', name\\: 'Viagra 25 mg 4 tabl\\.', defaultUsageAdvice\\: 'Ta 1 tablett under…'\\}\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: mixed$#"
			count: 2
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: non\\-falsy\\-string$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpdateProductVariantWithUpsertProductVariantHandlerTest\\:\\:arrangeExpectedChannelPricing\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpdateProductVariantWithUpsertProductVariantHandlerTest\\:\\:arrangeExpectedTranslations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpdateProductVariantWithUpsertProductVariantHandlerTest\\:\\:createExpectedProductVariant\\(\\) has parameter \\$optionValues with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpdateProductVariantWithUpsertProductVariantHandlerTest\\:\\:existingProductVariant\\(\\) has parameter \\$optionValues with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpdateProductVariantWithUpsertProductVariantHandlerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: array$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Match expression does not handle remaining value\\: string$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpsertProductHandlerTest\\:\\:getChannels\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpsertProductHandlerTest\\:\\:getExpectedProduct\\(\\) has parameter \\$channels with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpsertProductHandlerTest\\:\\:getExpectedProduct\\(\\) has parameter \\$expectedConsultProducts with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Parameter \\#4 \\$addictiveAttribute of method App\\\\Tests\\\\Catalog\\\\MessageHandler\\\\UpsertProductHandlerTest\\:\\:getExpectedProduct\\(\\) expects App\\\\Entity\\\\Product\\\\ProductAttributeInterface, Sylius\\\\Component\\\\Product\\\\Model\\\\ProductAttributeInterface given\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Trying to mock an undefined method findOneByCode\\(\\) on class Sylius\\\\Component\\\\Product\\\\Repository\\\\ProductRepositoryInterface\\.$#"
			count: 1
			path: tests/Catalog/MessageHandler/UpsertProductHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Processor\\\\Product\\\\ChannelHandlerTest\\:\\:getChannels\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ChannelHandlerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Catalog\\\\Processor\\\\Product\\\\ChannelHandlerTest\\:\\:\\$channels type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ChannelHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$upsertProduct of method App\\\\Catalog\\\\Processor\\\\Product\\\\CompositeProductProcessor\\:\\:execute\\(\\) expects App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProduct, App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProductInterface given\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/CompositeProductHandlerTest.php

		-
			message: "#^Cannot call method getAssociatedProducts\\(\\) on Sylius\\\\Component\\\\Product\\\\Model\\\\ProductAssociationInterface\\|false\\.$#"
			count: 3
			path: tests/Catalog/Processor/Product/ConsultProductAssociationHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Catalog\\\\Processor\\\\Product\\\\ConsultProductAssociationHandlerTest\\:\\:getExistingConsults\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ConsultProductAssociationHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$upsertProduct of method App\\\\Catalog\\\\Processor\\\\Product\\\\ConsultProductAssociationProcessor\\:\\:execute\\(\\) expects App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProduct, App\\\\Catalog\\\\Message\\\\Product\\\\UpsertSimpleProduct given\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ConsultProductAssociationHandlerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Catalog\\\\Processor\\\\Product\\\\ConsultProductAssociationHandlerTest\\:\\:\\$existingConsults type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ConsultProductAssociationHandlerTest.php

		-
			message: "#^Generator expects value type App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProduct, array\\<int, App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProduct\\> given\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ProductAttributeHandlerTest.php

		-
			message: "#^Generator expects value type App\\\\Catalog\\\\Message\\\\Product\\\\UpsertSimpleProduct, array\\<int, App\\\\Catalog\\\\Message\\\\Product\\\\UpsertSimpleProduct\\> given\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ProductAttributeHandlerTest.php

		-
			message: "#^Match expression does not handle remaining values\\: int\\<min, 0\\>\\|int\\<3, max\\>$#"
			count: 2
			path: tests/Catalog/Processor/Product/ProductAttributeHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$upsertProduct of method App\\\\Catalog\\\\Processor\\\\Product\\\\ProductImageProcessor\\:\\:execute\\(\\) expects App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProduct, App\\\\Catalog\\\\Message\\\\Product\\\\UpsertSimpleProduct given\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ProductImageHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$upsertProduct of method App\\\\Catalog\\\\Processor\\\\Product\\\\ProductImageProcessor\\:\\:execute\\(\\) expects App\\\\Catalog\\\\Message\\\\Product\\\\UpsertProduct, App\\\\Catalog\\\\Message\\\\Product\\\\UpsertSimpleProduct given\\.$#"
			count: 1
			path: tests/Catalog/Processor/Product/ProductImageProcessorTest.php

		-
			message: "#^Parameter \\#4 \\$taxon of static method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:addTaxon\\(\\) expects App\\\\Entity\\\\Taxonomy\\\\Taxon\\|null, Sylius\\\\Component\\\\Core\\\\Model\\\\TaxonInterface given\\.$#"
			count: 3
			path: tests/Catalog/Processor/Product/ProductTaxonHandlerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Catalog\\\\Processor\\\\ProductVariant\\\\AddChannelPricingTest\\:\\:\\$channelPricingFactory with generic interface Sylius\\\\Resource\\\\Factory\\\\FactoryInterface does not specify its types\\: T$#"
			count: 1
			path: tests/Catalog/Processor/ProductVariant/AddChannelPricingTest.php

		-
			message: "#^Method App\\\\Tests\\\\Command\\\\CommandKernelTestCase\\:\\:assertCommandOutputDisplayContainsLines\\(\\) has parameter \\$expectedOutputLines with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Command/CommandKernelTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Command\\\\CommandTestCase\\:\\:assertCommandOutputDisplayContainsLines\\(\\) has parameter \\$expectedOutputLines with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Command/CommandTestCase.php

		-
			message: "#^Parameter \\#1 \\$command of class Symfony\\\\Component\\\\Console\\\\Tester\\\\CommandTester constructor expects Symfony\\\\Component\\\\Console\\\\Command\\\\Command, object\\|null given\\.$#"
			count: 1
			path: tests/Command/ForgetAccountCommandTest.php

		-
			message: "#^Property App\\\\Tests\\\\Command\\\\ForgetAccountCommandTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Command/ForgetAccountCommandTest.php

		-
			message: "#^Cannot call method addPreferredItem\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/ConsultSystem/CreateConsultRequestBuilderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ConsultSystem\\\\CreateConsultRequestBuilderTest\\:\\:createExpectedConsultRequestWithItems\\(\\) has parameter \\$createItems with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ConsultSystem/CreateConsultRequestBuilderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ConsultSystem\\\\CreateConsultRequestHandlerTest\\:\\:createOrder\\(\\) has parameter \\$orderData with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ConsultSystem/CreateConsultRequestHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\ConsultSystem\\\\MessageHandler\\\\RegisterOnDisabledConsultSystemHandlerTest\\:\\:consultSystemReferenceIsSetDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/ConsultSystem/MessageHandler/RegisterOnDisabledConsultSystemHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\ConsultSystem\\\\MessageHandler\\\\RegisterOnDisabledConsultSystemHandlerTest\\:\\:eventTypesDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/ConsultSystem/MessageHandler/RegisterOnDisabledConsultSystemHandlerTest.php

		-
			message: "#^Parameter \\#1 \\$serializationContextBuilder of class App\\\\Api\\\\Controller\\\\AsyncDelayedCommandController constructor expects Nijens\\\\OpenapiBundle\\\\Serialization\\\\SerializationContextBuilder, Nijens\\\\OpenapiBundle\\\\Serialization\\\\SerializationContextBuilderInterface&PHPUnit\\\\Framework\\\\MockObject\\\\Stub given\\.$#"
			count: 1
			path: tests/Controller/AsyncDelayedCommandControllerTest.php

		-
			message: "#^Property Symfony\\\\Component\\\\HttpFoundation\\\\Request\\:\\:\\$attributes \\(Symfony\\\\Component\\\\HttpFoundation\\\\ParameterBag\\) does not accept PHPUnit\\\\Framework\\\\MockObject\\\\Stub&Symfony\\\\Component\\\\DependencyInjection\\\\ParameterBag\\\\ParameterBagInterface\\.$#"
			count: 1
			path: tests/Controller/AsyncDelayedCommandControllerTest.php

		-
			message: "#^Call to an undefined method Twig\\\\Environment\\:\\:expects\\(\\)\\.$#"
			count: 1
			path: tests/Controller/FrontendControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Doctrine\\\\EventListener\\\\OrderCountForSupplierListenerTest\\:\\:irrelevantChangeSetProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Doctrine/EventListener/OrderCountForSupplierListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Doctrine\\\\EventListener\\\\OrderCountForSupplierListenerTest\\:\\:relevantChangeSetProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Doctrine/EventListener/OrderCountForSupplierListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Doctrine\\\\EventListener\\\\OrderCountForSupplierListenerTest\\:\\:testItSkipsIfReferenceIsNotChanged\\(\\) has parameter \\$changeSet with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Doctrine/EventListener/OrderCountForSupplierListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Doctrine\\\\EventListener\\\\OrderCountForSupplierListenerTest\\:\\:testPostUpdateWithSupplierShipmentReferenceChange\\(\\) has parameter \\$changeSet with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Doctrine/EventListener/OrderCountForSupplierListenerTest.php

		-
			message: "#^Parameter \\#1 \\$shipment of method App\\\\Doctrine\\\\EventListener\\\\OrderCountForSupplierListener\\:\\:postUpdate\\(\\) expects App\\\\Entity\\\\Shipping\\\\Shipment, App\\\\Entity\\\\Shipping\\\\ShipmentInterface given\\.$#"
			count: 2
			path: tests/Doctrine/EventListener/OrderCountForSupplierListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Doctrine\\\\Types\\\\SubscriptionEnumTypeTest\\:\\:platformProviderForGetSqlDeclarationWithoutDefaultValue\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Doctrine/Types/SubscriptionEnumTypeTest.php

		-
			message: "#^Method App\\\\Tests\\\\Doctrine\\\\Types\\\\SubscriptionEnumTypeTest\\:\\:testGetSqlDeclarationWithoutDefaultValue\\(\\) has parameter \\$fieldDeclaration with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Doctrine/Types/SubscriptionEnumTypeTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/E2E/Api/Cart/RemoveChildCartItemsControllerTest.php

		-
			message: "#^Parameter \\#1 \\$productTaxonChannels of method App\\\\Entity\\\\Channel\\\\Channel\\:\\:setProductTaxonChannels\\(\\) expects Doctrine\\\\Common\\\\Collections\\\\Collection\\<int, App\\\\Entity\\\\Product\\\\ProductTaxonChannelInterface\\>, Doctrine\\\\Common\\\\Collections\\\\ArrayCollection\\<int, App\\\\Entity\\\\Product\\\\ProductTaxonChannel\\> given\\.$#"
			count: 1
			path: tests/Entity/Channel/ChannelTest.php

		-
			message: "#^Parameter \\#1 \\$adjustment of method Sylius\\\\Component\\\\Order\\\\Model\\\\Order\\:\\:removeAdjustment\\(\\) expects Sylius\\\\Component\\\\Order\\\\Model\\\\AdjustmentInterface, Sylius\\\\Component\\\\Order\\\\Model\\\\AdjustmentInterface\\|false given\\.$#"
			count: 1
			path: tests/Entity/Order/OrderTest.php

		-
			message: "#^Parameter \\#1 \\$item of method App\\\\Entity\\\\Order\\\\Order\\:\\:removeItem\\(\\) expects Sylius\\\\Component\\\\Order\\\\Model\\\\OrderItemInterface, App\\\\Entity\\\\Order\\\\OrderItem\\|false given\\.$#"
			count: 1
			path: tests/Entity/Order/OrderTest.php

		-
			message: "#^Parameter \\#1 \\$productTaxonChannels of method App\\\\Entity\\\\Product\\\\ProductTaxon\\:\\:setProductTaxonChannels\\(\\) expects Doctrine\\\\Common\\\\Collections\\\\Collection\\<\\(int\\|string\\), App\\\\Entity\\\\Product\\\\ProductTaxonChannelInterface\\>, Doctrine\\\\Common\\\\Collections\\\\ArrayCollection\\<int, App\\\\Entity\\\\Product\\\\ProductTaxonChannel\\> given\\.$#"
			count: 1
			path: tests/Entity/Product/ProductTaxonTest.php

		-
			message: "#^Method App\\\\Tests\\\\EventListener\\\\ErrorListenerTest\\:\\:exceptionDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/EventListener/ErrorListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\EventListener\\\\ErrorListenerTest\\:\\:exceptionLogMethodDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/EventListener/ErrorListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\EventSubscriber\\\\CustomerLocalizationEventSubscriberTest\\:\\:provideOrderEventWithChanges\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/EventSubscriber/CustomerLocalizationEventSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\EventSubscriber\\\\CustomerLocalizationEventSubscriberTest\\:\\:provideOrderEventWithNoChanges\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/EventSubscriber/CustomerLocalizationEventSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\EventSubscriber\\\\CustomerLocalizationEventSubscriberTest\\:\\:provideOrderEventWithoutCustomer\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/EventSubscriber/CustomerLocalizationEventSubscriberTest.php

		-
			message: "#^Parameter \\#1 \\$body of class Symfony\\\\Component\\\\HttpClient\\\\Response\\\\MockResponse constructor expects iterable\\<string\\|Throwable\\>\\|string, string\\|false given\\.$#"
			count: 2
			path: tests/ExchangeRate/ExchangeRateUpdaterTest.php

		-
			message: "#^Property App\\\\Tests\\\\ExchangeRate\\\\ExchangeRateUpdaterTest\\:\\:\\$mockResponses with generic class ArrayIterator does not specify its types\\: TKey, TValue$#"
			count: 1
			path: tests/ExchangeRate/ExchangeRateUpdaterTest.php

		-
			message: "#^Parameter \\#1 \\$adjustmentFactory of class App\\\\Factory\\\\Order\\\\AdjustmentFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\<Sylius\\\\Component\\\\Order\\\\Model\\\\AdjustmentInterface\\>, Sylius\\\\Resource\\\\Factory\\\\Factory given\\.$#"
			count: 1
			path: tests/Factory/AdjustmentFactoryTest.php

		-
			message: "#^Call to an undefined method Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\:\\:expects\\(\\)\\.$#"
			count: 2
			path: tests/Factory/CustomerFactoryTest.php

		-
			message: "#^Parameter \\#1 \\$customerFactory of class App\\\\Factory\\\\Customer\\\\CustomerFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\Factory, PHPUnit\\\\Framework\\\\MockObject\\\\MockObject&Sylius\\\\Resource\\\\Factory\\\\FactoryInterface given\\.$#"
			count: 1
			path: tests/Factory/CustomerFactoryTest.php

		-
			message: "#^Property App\\\\Tests\\\\Factory\\\\CustomerFactoryTest\\:\\:\\$customerFactoryMock with generic interface Sylius\\\\Resource\\\\Factory\\\\FactoryInterface does not specify its types\\: T$#"
			count: 1
			path: tests/Factory/CustomerFactoryTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PaymentMethodFraudCheckerTest\\:\\:willMatchOrderDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PaymentMethodFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PaymentMethodFraudCheckerTest\\:\\:willMatchOrderDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PaymentMethodFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PaymentMethodFraudCheckerTest\\:\\:willNotMatchOrderDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PaymentMethodFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PaymentMethodFraudCheckerTest\\:\\:willNotMatchOrderDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PaymentMethodFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PostcodeFraudCheckerTest\\:\\:willMatchOrderDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PostcodeFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PostcodeFraudCheckerTest\\:\\:willMatchOrderDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PostcodeFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PostcodeFraudCheckerTest\\:\\:willNotMatchOrderDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PostcodeFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\PostcodeFraudCheckerTest\\:\\:willNotMatchOrderDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/PostcodeFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\ProductFraudCheckerTest\\:\\:willMatchOrderDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/ProductFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\ProductFraudCheckerTest\\:\\:willMatchOrderDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/ProductFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\ProductFraudCheckerTest\\:\\:willNotMatchOrderDataProvider\\(\\) is unused\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/ProductFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Fraud\\\\FraudCheckers\\\\ProductFraudCheckerTest\\:\\:willNotMatchOrderDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Fraud/FraudCheckers/ProductFraudCheckerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Admin\\\\Order\\\\SwitchSupplierOnOrderTest\\:\\:wrongOrderStateProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Admin/Order/SwitchSupplierOnOrderTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Admin\\\\Order\\\\SwitchSupplierOnOrderTest\\:\\:\\$alternativeSuppliersResolver \\(App\\\\Supplier\\\\Resolver\\\\AlternativeSuppliersResolverInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Admin/Order/SwitchSupplierOnOrderTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Admin\\\\Order\\\\SwitchSupplierOnOrderTest\\:\\:\\$requestStack \\(Symfony\\\\Component\\\\HttpFoundation\\\\RequestStack\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Admin/Order/SwitchSupplierOnOrderTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Admin\\\\Order\\\\SwitchSupplierOnOrderTest\\:\\:\\$requestStack is never read, only written\\.$#"
			count: 1
			path: tests/Functional/Admin/Order/SwitchSupplierOnOrderTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Admin\\\\Order\\\\SwitchSupplierOnOrderTest\\:\\:\\$stateMachineFactory \\(SM\\\\Factory\\\\Factory\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Admin/Order/SwitchSupplierOnOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractGetPaymentMethodsTest\\:\\:disallowedOrderStateProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractGetPaymentMethodsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractGetPaymentMethodsTest\\:\\:maximumOrderTotalChannelProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractGetPaymentMethodsTest.php

		-
			message: "#^Call to an undefined method Doctrine\\\\ORM\\\\EntityRepository\\<App\\\\Entity\\\\Order\\\\Order\\>\\:\\:findOneByTokenValue\\(\\)\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method close\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method get\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 13
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method refresh\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:addItemsToCart\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:addMedicalQuestionnaire\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:cancelOrder\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:cancelPayment\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:clearChildItemsFromCart\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:completeMedicalQuestionnaire\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:completeOrder\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:createCart\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:deleteItemFromCart\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:getResponseBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:loginAccount\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:prepareDatabaseWithProducts\\(\\) has parameter \\$channelCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:setAddress\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:setPaymentMethod\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Account/GetAccountTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Account/GetAccountTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Account/GetAccountTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Account/GetAccountTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Account/GetAccountTest.php

		-
			message: "#^Parameter \\#1 \\$client of class App\\\\Tests\\\\Functional\\\\Api\\\\ShopUserAuthenticationTestHelper constructor expects Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser, Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Account/GetAccountTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Account/RegisterCustomerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 8
			path: tests/Functional/Api/Account/RegisterCustomerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 12
			path: tests/Functional/Api/Account/RegisterCustomerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Account\\\\RegisterCustomerTest\\:\\:createRequestBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Account/RegisterCustomerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 8
			path: tests/Functional/Api/Account/RegisterCustomerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Account\\\\UpdateAccountInformationTest\\:\\:createRequestBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Account/UpdateAccountInformationTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Account/UpdateAccountInformationTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Account\\\\UpdateAccountInformationTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Account/UpdateAccountInformationTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Address\\\\GetAddressesControllerTest\\:\\:channelCodeProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetAddressesControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetAddressesControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetAddressesControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetAddressesControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetAddressesControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Address\\\\GetAddressesControllerTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetAddressesControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Address/GetCountriesControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Address/GetCountriesControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Address\\\\RemoveAddressesControllerTest\\:\\:channelCodeProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/Address/RemoveAddressesControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Address\\\\UpdateAddressControllerTest\\:\\:getUpdateAddressBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Address/UpdateAddressControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/GetOrderTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/GetOrderTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/GetOrderTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Admin\\\\Order\\\\GetOrderTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/GetOrderTest.php

		-
			message: "#^Cannot call method getCode\\(\\) on Sylius\\\\Component\\\\Shipping\\\\Model\\\\ShippingMethodInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^Cannot call method getShipments\\(\\) on App\\\\Entity\\\\Order\\\\Order\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^Cannot call method getState\\(\\) on Sylius\\\\Component\\\\Core\\\\Model\\\\ShipmentInterface\\|false\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^Cannot call method getTracking\\(\\) on App\\\\Entity\\\\Shipping\\\\Shipment\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^Parameter \\#1 \\$body of class Symfony\\\\Component\\\\HttpClient\\\\Response\\\\MockResponse constructor expects iterable\\<string\\|Throwable\\>\\|string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Admin/Order/UpdateShipmentTrackingTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$decodedResponseBody has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AuthenticateTest.php

		-
			message: "#^Cannot call method find\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 11
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 15
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\AddItemsToCartControllerTest\\:\\:dataProviderCannotExceedMaximumNumberOfConsults\\(\\) is unused\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\AddItemsToCartControllerTest\\:\\:provideDataForAddItemGivesCorrectShippingMethodForCart\\(\\) is unused\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$client of class App\\\\Tests\\\\Functional\\\\Api\\\\ShopUserAuthenticationTestHelper constructor expects Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser, Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/AddItemsToCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\ApplyCouponToCartControllerTest\\:\\:createExpectedViolations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ApplyCouponToCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/ApplyCouponToCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\CreateCartControllerTest\\:\\:createExpectedJsonResponse\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/CreateCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/CreateCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualResponseBody of method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\CreateCartControllerTest\\:\\:createExpectedJsonResponse\\(\\) expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/CreateCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/CreateCartControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 6
			path: tests/Functional/Api/Cart/CreateCartControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\CreateCartControllerTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/CreateCartControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$client of class App\\\\Tests\\\\Functional\\\\Api\\\\ShopUserAuthenticationTestHelper constructor expects Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser, Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$product of method App\\\\Tests\\\\Functional\\\\Api\\\\TaxonTestFactory\\:\\:createProductTaxon\\(\\) expects Sylius\\\\Component\\\\Core\\\\Model\\\\ProductInterface, App\\\\Entity\\\\Product\\\\Product\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/GetCartControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 5
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 5
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 7
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\ModifyItemQuantityControllerTest\\:\\:getExpectedResponseItems\\(\\) has parameter \\$expectedAttributes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\ModifyItemQuantityControllerTest\\:\\:getExpectedResponseItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Parameter \\#1 \\$channel of method Sylius\\\\Component\\\\Core\\\\Model\\\\Promotion\\:\\:addChannel\\(\\) expects Sylius\\\\Component\\\\Channel\\\\Model\\\\ChannelInterface, App\\\\Entity\\\\Channel\\\\Channel\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/ModifyItemQuantityControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Cart/RemoveChildCartItemsControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveChildCartItemsControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/RemoveChildCartItemsControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/RemoveChildCartItemsControllerTest.php

		-
			message: "#^Cannot call method find\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveCouponFromCartControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveCouponFromCartControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/RemoveCouponFromCartControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/RemoveCouponFromCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\RemoveCouponFromCartControllerTest\\:\\:provideDisallowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveCouponFromCartControllerTest.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\RemoveItemFromCartControllerTest\\:\\:\\$entityManager\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Cannot call method getCode\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariantInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 5
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Cannot call method getVariant\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 2
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Cannot call method setParentOrderItem\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 3
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Cannot call method setParentOrderItem\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/RemoveItemFromCartControllerTest.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\ValidateCartControllerTest\\:\\:\\$authenticationHelper\\.$#"
			count: 2
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Cannot call method setWarnings\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\ValidateCartControllerTest\\:\\:getExpectedInvalidStateBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Cart\\\\ValidateCartControllerTest\\:\\:getExpectedResponseBodyForInvalidCart\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Cart/ValidateCartControllerTest.php

		-
			message: "#^Call to an undefined method Doctrine\\\\ORM\\\\EntityRepository\\<App\\\\Entity\\\\Channel\\\\Channel\\>\\:\\:findOneByCode\\(\\)\\.$#"
			count: 4
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Cannot call method addAttribute\\(\\) on App\\\\Entity\\\\Product\\\\Product\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Cannot call method createForPromotion\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Cannot call method createNew\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:addAttributeToProduct\\(\\) has parameter \\$attributeCode with no type specified\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:addAttributeToProduct\\(\\) has parameter \\$productCode with no type specified\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:createCartWithProductVariants\\(\\) has parameter \\$productVariantCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:createConsultForProductVariants\\(\\) has parameter \\$maxQuantitiesPerOrder with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:createConsultForProductVariants\\(\\) has parameter \\$productVariants with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:createProductAndProductVariants\\(\\) has parameter \\$maxQuantitiesPerOrder with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:createProductAndProductVariants\\(\\) has parameter \\$productVariantCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:createProductVariant\\(\\) has parameter \\$maxQuantitiesPerOrder with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^PHPDoc tag @var for variable \\$productFactory contains generic interface Sylius\\\\Component\\\\Product\\\\Factory\\\\ProductFactoryInterface but does not specify its types\\: T$#"
			count: 2
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^PHPDoc tag @var for variable \\$productTranslationFactory contains generic interface Sylius\\\\Resource\\\\Factory\\\\FactoryInterface but does not specify its types\\: T$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Variable \\$productTranslationFactory in PHPDoc tag @var does not match assigned variable \\$productImageFactory\\.$#"
			count: 1
			path: tests/Functional/Api/CartTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\AddressOrderTest\\:\\:provideInvalidAddress\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/AddressOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\AddressOrderTest\\:\\:testItValidatesAddress\\(\\) has parameter \\$expectedViolations with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/AddressOrderTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CheckoutStateBackAndForwardTransitionTest.php

		-
			message: "#^Cannot call method refresh\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CheckoutStateBackAndForwardTransitionTest.php

		-
			message: "#^Call to an undefined method App\\\\Repository\\\\AddressRepositoryInterface\\:\\:findByCustomer\\(\\)\\.$#"
			count: 2
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method disableReboot\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 6
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method getConnection\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 6
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 8
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Expression \"\\$this\\-\\>addItemsToCart\\(\\$tokenValue, \\$channelCode, \\[\\['productVariantCode' \\=\\> self\\:\\:PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC, 'quantity' \\=\\> 1\\]\\]\\)\\['items'\\]\\[1\\]\\['id'\\]\" on a separate line does not do anything\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Parameter \\#1 \\$client of class App\\\\Tests\\\\Functional\\\\Api\\\\ShopUserAuthenticationTestHelper constructor expects Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser, Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteOrderTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Checkout/CompleteQuestionnaireTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteQuestionnaireTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteQuestionnaireTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Checkout/CompleteQuestionnaireTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\CompleteQuestionnaireTest\\:\\:makeRequest\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/CompleteQuestionnaireTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\GetPaymentMethodsTest\\:\\:disallowedOrderStateProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/GetPaymentMethodsTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/PlaceConsultOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\PlaceConsultOrderTest\\:\\:addPreferredVariantToCart\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/PlaceConsultOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\PlaceConsultOrderTest\\:\\:deleteRelatedItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/PlaceConsultOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\PlaceConsultOrderTest\\:\\:getListRelatedMedicationProducts\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/PlaceConsultOrderTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Checkout/PlaceConsultOrderTest.php

		-
			message: "#^Parameter \\#2 \\$haystack of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertStringContainsString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/PlaceConsultOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\RegisterQuestionnaireReferenceTest\\:\\:makeRequest\\(\\) has parameter \\$requestBody with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/RegisterQuestionnaireReferenceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Checkout\\\\RegisterQuestionnaireReferenceTest\\:\\:makeRequest\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Checkout/RegisterQuestionnaireReferenceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\EventSubscriber\\\\DeserializedCommand\\\\OrderAwareSubscriberTest\\:\\:provideCorrectOrderStates\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/EventSubscriber/DeserializedCommand/OrderAwareSubscriberTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\EventSubscriber\\\\DeserializedCommand\\\\OrderAwareSubscriberTest\\:\\:provideIncorrectOrderStates\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/EventSubscriber/DeserializedCommand/OrderAwareSubscriberTest.php

		-
			message: "#^Cannot call method getContainer\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\MarketingSubscription\\\\CreateMarketingSubscriptionControllerTest\\:\\:provideCreateRequestBody\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\MarketingSubscription\\\\CreateMarketingSubscriptionControllerTest\\:\\:provideInvalidCreateRequestBody\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\MarketingSubscription\\\\CreateMarketingSubscriptionControllerTest\\:\\:provideOptInEmailSubscription\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\MarketingSubscription\\\\CreateMarketingSubscriptionControllerTest\\:\\:testCanCreateSubscription\\(\\) has parameter \\$requestBody with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\MarketingSubscription\\\\CreateMarketingSubscriptionControllerTest\\:\\:testCannotCreateSubscriptionWithInvalidRequestBody\\(\\) has parameter \\$expectedErrorResponse with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\MarketingSubscription\\\\CreateMarketingSubscriptionControllerTest\\:\\:testCannotCreateSubscriptionWithInvalidRequestBody\\(\\) has parameter \\$requestBody with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/CreateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/GetMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/GetMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/GetMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method request\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/MarketingSubscription/GetMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method find\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/UnsubscribeMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/UnsubscribeMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/UnsubscribeMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/UnsubscribeMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method request\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/MarketingSubscription/UnsubscribeMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/MarketingSubscription/UpdateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/UpdateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/MarketingSubscription/UpdateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/UpdateMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/VerifyMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/VerifyMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/VerifyMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method refresh\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/MarketingSubscription/VerifyMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method request\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/MarketingSubscription/VerifyMarketingSubscriptionControllerTest.php

		-
			message: "#^Cannot call method getCode\\(\\) on Sylius\\\\Component\\\\Shipping\\\\Model\\\\ShippingMethodInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Notify\\\\NotifyOrderShipmentStatusControllerTest\\:\\:supplierOrderStatusProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Notify\\\\NotifyOrderShipmentStatusControllerTest\\:\\:testThatTheStatusIsUpdatedAfterReceivingMessage\\(\\) has parameter \\$expectedStates with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Notify\\\\NotifyOrderShipmentStatusControllerTest\\:\\:testThatTheStatusIsUpdatedAfterReceivingMessage\\(\\) has parameter \\$states with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$body of class Symfony\\\\Component\\\\HttpClient\\\\Response\\\\MockResponse constructor expects iterable\\<string\\|Throwable\\>\\|string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Notify\\\\NotifyOrderShipmentStatusControllerTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Notify\\\\NotifyOrderShipmentStatusControllerTest\\:\\:\\$mockHttpClient \\(Symfony\\\\Component\\\\HttpClient\\\\MockHttpClient\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Notify/NotifyOrderShipmentStatusControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\BaseFunctionalOrderTestCase\\:\\:createAddresses\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/BaseFunctionalOrderTestCase.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\BaseFunctionalOrderTestCase\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/BaseFunctionalOrderTestCase.php

		-
			message: "#^Cannot call method getBy\\(\\) on App\\\\Entity\\\\Order\\\\Cancellation\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/CancelOrderControllerTest.php

		-
			message: "#^Cannot call method getReason\\(\\) on App\\\\Entity\\\\Order\\\\Cancellation\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/CancelOrderControllerTest.php

		-
			message: "#^Cannot call method getPrescriptionState\\(\\) on App\\\\Entity\\\\Order\\\\Order\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/Communication/CreateCommunicationMessageControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\Communication\\\\CreateCommunicationMessageControllerTest\\:\\:appendAuthenticationToken\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/Communication/CreateCommunicationMessageControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\Communication\\\\CreateCommunicationMessageControllerTest\\:\\:appendAuthenticationToken\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/Communication/CreateCommunicationMessageControllerTest.php

		-
			message: "#^Parameter \\#1 \\$object of method Doctrine\\\\ORM\\\\EntityManagerInterface\\:\\:refresh\\(\\) expects object, App\\\\Entity\\\\Order\\\\Order\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/Communication/CreateCommunicationMessageControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\Communication\\\\ListControllerTest\\:\\:appendAuthenticationToken\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/Communication/ListControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\Communication\\\\ListControllerTest\\:\\:appendAuthenticationToken\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/Communication/ListControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on Sylius\\\\Component\\\\Core\\\\Model\\\\PaymentInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderCompletionInformationControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderCompletionInformationControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderCompletionInformationControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderCompletionInformationControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\GetOrderCompletionInformationControllerTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderCompletionInformationControllerTest.php

		-
			message: "#^Cannot call method addPreferredItem\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 4
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Cannot call method getVariant\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 6
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Cannot call method removePreferredItem\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\GetOrderTest\\:\\:provideExpectedResponseProperties\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\GetOrderTest\\:\\:testGetOrderResponseWithOriginalOrderItems\\(\\) has parameter \\$expectedResponseProperties with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Parameter \\#1 \\$orderItem of class App\\\\Entity\\\\Order\\\\PreferredOrderItem constructor expects App\\\\Entity\\\\Order\\\\OrderItem, App\\\\Entity\\\\Order\\\\OrderItem\\|false given\\.$#"
			count: 4
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Parameter \\#2 \\$item of method App\\\\Tests\\\\Functional\\\\Api\\\\CartTestFactory\\:\\:removeItemFromCart\\(\\) expects App\\\\Entity\\\\Order\\\\OrderItem, App\\\\Entity\\\\Order\\\\OrderItem\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Parameter \\#2 \\$variant of class App\\\\Entity\\\\Order\\\\PreferredOrderItem constructor expects App\\\\Entity\\\\Product\\\\ProductVariantInterface, App\\\\Entity\\\\Product\\\\ProductVariantInterface\\|null given\\.$#"
			count: 2
			path: tests/Functional/Api/Order/GetOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\GetPaymentMethodsTest\\:\\:disallowedOrderStateProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Order/GetPaymentMethodsTest.php

		-
			message: "#^Cannot call method disableReboot\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Parameter \\#3 \\$body of class GuzzleHttp\\\\Psr7\\\\Response constructor expects Psr\\\\Http\\\\Message\\\\StreamInterface\\|resource\\|string\\|null, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\PaymentTest\\:\\:\\$mockHandler \\(GuzzleHttp\\\\Handler\\\\MockHandler\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PaymentTest.php

		-
			message: "#^Cannot call method flush\\(\\) on object\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/Order/PrescriptionControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on object\\|null\\.$#"
			count: 7
			path: tests/Functional/Api/Order/PrescriptionControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Order\\\\PrescriptionControllerTest\\:\\:validShippingStateProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/Order/PrescriptionControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Payment/CancelPaymentControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Payment/CancelPaymentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Payment\\\\CancelPaymentControllerTest\\:\\:provideInvalidPaymentStates\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/Payment/CancelPaymentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Payment\\\\CancelPaymentControllerTest\\:\\:provideValidPaymentStates\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/Payment/CancelPaymentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Payment\\\\UpdatePaymentControllerTest\\:\\:getExpectedValidKlarnaResponse\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Payment/UpdatePaymentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Payment\\\\UpdatePaymentControllerTest\\:\\:invalidStateForCheckoutEndpointProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Payment/UpdatePaymentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Payment\\\\UpdatePaymentControllerTest\\:\\:invalidStateForOrderEndpointProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Payment/UpdatePaymentControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Api/Payment/UpdatePaymentControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Api/Payment/UpdatePaymentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Prepr\\\\Taxon\\\\ListControllerTest\\:\\:testCanList\\(\\) has parameter \\$query with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Prepr/Taxon/ListControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\AbstractProductListControllerTest\\:\\:createProductVariants\\(\\) has parameter \\$productOptions with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/AbstractProductListControllerTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Cannot call method getResponse\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Cannot call method jsonRequest\\(\\) on Symfony\\\\Bundle\\\\FrameworkBundle\\\\KernelBrowser\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductControllerTest\\:\\:createExpectedResponseBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/Product/GetProductControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductEnrichmentControllerTest\\:\\:createProduct\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductEnrichmentControllerTest\\:\\:createProductTaxonomy\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$productFactory contains generic interface Sylius\\\\Component\\\\Product\\\\Factory\\\\ProductFactoryInterface but does not specify its types\\: T$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^Parameter \\#1 \\$channel of method Sylius\\\\Component\\\\Channel\\\\Model\\\\ChannelsAwareInterface\\:\\:addChannel\\(\\) expects Sylius\\\\Component\\\\Channel\\\\Model\\\\ChannelInterface, App\\\\Entity\\\\Channel\\\\Channel\\|null given\\.$#"
			count: 2
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^Parameter \\#1 \\$productTaxonChannels of method App\\\\Entity\\\\Product\\\\ProductTaxon\\:\\:setProductTaxonChannels\\(\\) expects Doctrine\\\\Common\\\\Collections\\\\Collection\\<\\(int\\|string\\), App\\\\Entity\\\\Product\\\\ProductTaxonChannelInterface\\>, Doctrine\\\\Common\\\\Collections\\\\ArrayCollection\\<int, App\\\\Entity\\\\Product\\\\ProductTaxonChannel\\> given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Api/Product/GetProductEnrichmentControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductVariantsControllerTest\\:\\:createExpectedResponseBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductVariantsControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductVariantsControllerTest\\:\\:createExpectedResponseBodyForMostEligibleVariant\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductVariantsControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductVariantsControllerTest\\:\\:getEndpoint\\(\\) has parameter \\$query with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductVariantsControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductVariantsControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductVariantsControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductsInListControllerTest\\:\\:validateResponse\\(\\) has parameter \\$expectedResponses with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductsInListControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Product\\\\GetProductsInListControllerTest\\:\\:validateResponse\\(\\) has parameter \\$response with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductsInListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$productAssociationFactory of class Sylius\\\\Bundle\\\\CoreBundle\\\\Fixture\\\\Factory\\\\ProductAssociationExampleFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\<Sylius\\\\Component\\\\Product\\\\Model\\\\ProductAssociationInterface\\>, object\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductsInListControllerTest.php

		-
			message: "#^Parameter \\#2 \\$productAssociationTypeRepository of class Sylius\\\\Bundle\\\\CoreBundle\\\\Fixture\\\\Factory\\\\ProductAssociationExampleFactory constructor expects Sylius\\\\Component\\\\Product\\\\Repository\\\\ProductAssociationTypeRepositoryInterface, object\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductsInListControllerTest.php

		-
			message: "#^Parameter \\#3 \\$productRepository of class Sylius\\\\Bundle\\\\CoreBundle\\\\Fixture\\\\Factory\\\\ProductAssociationExampleFactory constructor expects Sylius\\\\Component\\\\Core\\\\Repository\\\\ProductRepositoryInterface, object\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/Product/GetProductsInListControllerTest.php

		-
			message: "#^Call to an undefined method Sylius\\\\Component\\\\Product\\\\Model\\\\ProductVariantTranslationInterface\\:\\:setLeaflet\\(\\)\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Cannot call method createNew\\(\\) on object\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Cannot call method getCountries\\(\\) on App\\\\Entity\\\\Channel\\\\Channel\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:addOptionValuesToVariant\\(\\) has parameter \\$productOptions with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:createOrGetSupplier\\(\\) has parameter \\$countries with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:createProduct\\(\\) has parameter \\$productOptions with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:createProductOptionsAndValues\\(\\) has parameter \\$productOptions with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:createProductVariant\\(\\) has parameter \\$channelPricings with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:createProductVariant\\(\\) has parameter \\$productOptions with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:createProductVariant\\(\\) has parameter \\$productVariantOption with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:setChannelPricings\\(\\) has parameter \\$channelPricings with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Method Doctrine\\\\Persistence\\\\ObjectManager\\:\\:flush\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Parameter \\#1 \\$productAssociationFactory of class Sylius\\\\Bundle\\\\CoreBundle\\\\Fixture\\\\Factory\\\\ProductAssociationExampleFactory constructor expects Sylius\\\\Resource\\\\Factory\\\\FactoryInterface\\<Sylius\\\\Component\\\\Product\\\\Model\\\\ProductAssociationInterface\\>, object\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Parameter \\#2 \\$productAssociationTypeRepository of class Sylius\\\\Bundle\\\\CoreBundle\\\\Fixture\\\\Factory\\\\ProductAssociationExampleFactory constructor expects Sylius\\\\Component\\\\Product\\\\Repository\\\\ProductAssociationTypeRepositoryInterface, object\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Parameter \\#3 \\$productRepository of class Sylius\\\\Bundle\\\\CoreBundle\\\\Fixture\\\\Factory\\\\ProductAssociationExampleFactory constructor expects Sylius\\\\Component\\\\Core\\\\Repository\\\\ProductRepositoryInterface, object\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\ProductTestFactory\\:\\:\\$objectManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/ProductTestFactory.php

		-
			message: "#^Cannot call method getShippingTotal\\(\\) on App\\\\Entity\\\\Order\\\\Order\\|null\\.$#"
			count: 2
			path: tests/Functional/Api/Promotion/FreeShippingMethodTest.php

		-
			message: "#^Cannot call method setConfiguration\\(\\) on App\\\\Entity\\\\Shipping\\\\ShippingMethod\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/ShipmentTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ShipmentTestFactory\\:\\:updateShippingMethod\\(\\) should return App\\\\Entity\\\\Shipping\\\\ShippingMethod but returns App\\\\Entity\\\\Shipping\\\\ShippingMethod\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/ShipmentTestFactory.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\ShipmentTestFactory\\:\\:\\$objectManager \\(Doctrine\\\\Persistence\\\\ObjectManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/ShipmentTestFactory.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/ShopUserAuthenticationTestHelper.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\ShopUserToken\\\\GetAuthenticationTokenTest\\:\\:providePasswordMigrationHashTypes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$customerFactory contains generic interface Sylius\\\\Resource\\\\Factory\\\\FactoryInterface but does not specify its types\\: T$#"
			count: 1
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$customerPoolRepository contains generic interface Sylius\\\\Resource\\\\Doctrine\\\\Persistence\\\\RepositoryInterface but does not specify its types\\: T$#"
			count: 1
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$shopUserFactory contains generic interface Sylius\\\\Resource\\\\Factory\\\\FactoryInterface but does not specify its types\\: T$#"
			count: 1
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^Parameter \\#2 \\$string of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertStringStartsWith\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\ShopUserToken\\\\GetAuthenticationTokenTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/ShopUserToken/GetAuthenticationTokenTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\AftercareStateMachineTest\\:\\:createOrder\\(\\) has parameter \\$prescriptionRequired with no type specified\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\AftercareStateMachineTest\\:\\:provideCompletedOrders\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\AftercareStateMachineTest\\:\\:providePlacedOrders\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\AftercareStateMachineTest\\:\\:testCancelledOrderHasCorrectState\\(\\) has parameter \\$aftercareState with no type specified\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\AftercareStateMachineTest\\:\\:testCancelledOrderHasCorrectState\\(\\) has parameter \\$expectedAftercareState with no type specified\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\AftercareStateMachineTest\\:\\:\\$stateMachineFactory \\(SM\\\\Factory\\\\FactoryInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/AftercareStateMachineTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 4
			path: tests/Functional/Api/StateMachine/PrescriptionStateMachineTest.php

		-
			message: "#^Cannot call method persist\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 3
			path: tests/Functional/Api/StateMachine/PrescriptionStateMachineTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\StateMachine\\\\PrescriptionStateMachineTest\\:\\:\\$stateMachineFactory \\(SM\\\\Factory\\\\FactoryInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/StateMachine/PrescriptionStateMachineTest.php

		-
			message: "#^Parameter \\#1 \\$productTaxonChannels of method App\\\\Entity\\\\Product\\\\ProductTaxon\\:\\:setProductTaxonChannels\\(\\) expects Doctrine\\\\Common\\\\Collections\\\\Collection\\<\\(int\\|string\\), App\\\\Entity\\\\Product\\\\ProductTaxonChannelInterface\\>, Doctrine\\\\Common\\\\Collections\\\\ArrayCollection\\<int, App\\\\Entity\\\\Product\\\\ProductTaxonChannel\\> given\\.$#"
			count: 1
			path: tests/Functional/Api/TaxonTestFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\TermQuestion\\\\GetTermQuestionsControllerTest\\:\\:localeCodeProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/TermQuestion/GetTermQuestionsControllerTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/TermQuestion/GetTermQuestionsControllerTest.php

		-
			message: "#^Parameter \\#1 \\$expectedJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/TermQuestion/GetTermQuestionsControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/TermQuestion/GetTermQuestionsControllerTest.php

		-
			message: "#^Parameter \\#2 \\$haystack of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertStringContainsString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/TermQuestion/GetTermQuestionsControllerTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Api\\\\TermQuestion\\\\GetTermQuestionsControllerTest\\:\\:\\$objectManager \\(Doctrine\\\\Persistence\\\\ObjectManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/TermQuestion/GetTermQuestionsControllerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\CanopyDeploy\\\\Client\\\\AbstractWebhookClientTest\\:\\:expectedData\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/CanopyDeploy/Client/AbstractWebhookClientTest.php

		-
			message: "#^Parameter \\#2 \\$serializationContextBuilder of class App\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializer constructor expects Nijens\\\\OpenapiBundle\\\\Serialization\\\\SerializationContextBuilder, Nijens\\\\OpenapiBundle\\\\Serialization\\\\SerializationContextBuilderInterface given\\.$#"
			count: 1
			path: tests/Functional/CanopyDeploy/Client/AbstractWebhookClientTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\CanopyDeploy\\\\Client\\\\WebhookClientForOrderItemRemoveTest\\:\\:expectedData\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/CanopyDeploy/Client/WebhookClientForOrderItemRemoveTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\CanopyDeploy\\\\Client\\\\WebhookClientForOrderTest\\:\\:expectedData\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/CanopyDeploy/Client/WebhookClientForOrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\CanopyDeploy\\\\Client\\\\WebhookClientForPasswordResetDataTest\\:\\:expectedData\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/CanopyDeploy/Client/WebhookClientForPasswordResetDataTest.php

		-
			message: "#^Cannot call method getAmountOrdersToday\\(\\) on App\\\\Entity\\\\Supplier\\\\Supplier\\|null\\.$#"
			count: 1
			path: tests/Functional/Doctrine/OrderCountForSupplierListenerTest.php

		-
			message: "#^Parameter \\#1 \\$object of method Doctrine\\\\ORM\\\\EntityManagerInterface\\:\\:refresh\\(\\) expects object, App\\\\Entity\\\\Supplier\\\\Supplier\\|null given\\.$#"
			count: 1
			path: tests/Functional/Doctrine/OrderCountForSupplierListenerTest.php

		-
			message: "#^Call to an undefined method App\\\\Entity\\\\Order\\\\PreferredOrderItemInterface\\:\\:setUsageAdvice\\(\\)\\.$#"
			count: 1
			path: tests/Functional/Finance/Statistics/FinancialExportTest.php

		-
			message: "#^Cannot call method createQueryBuilder\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 1
			path: tests/Functional/Finance/Statistics/FinancialExportTest.php

		-
			message: "#^Cannot call method flush\\(\\) on Doctrine\\\\ORM\\\\EntityManagerInterface\\|null\\.$#"
			count: 4
			path: tests/Functional/Finance/Statistics/FinancialExportTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Finance\\\\Statistics\\\\FinancialExportTest\\:\\:provideExpectedCsvForGetCsvContentAsStringReturnsCorrectCsvString\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Finance/Statistics/FinancialExportTest.php

		-
			message: "#^Cannot call method addPreferredItem\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Functional/OrderProcessing/FollowUpOrder/AddOrderItemsProcessorTest.php

		-
			message: "#^Parameter \\#1 \\$orderItem of class App\\\\Entity\\\\Order\\\\PreferredOrderItem constructor expects App\\\\Entity\\\\Order\\\\OrderItem, App\\\\Entity\\\\Order\\\\OrderItem\\|false given\\.$#"
			count: 1
			path: tests/Functional/OrderProcessing/FollowUpOrder/AddOrderItemsProcessorTest.php

		-
			message: "#^Cannot call method getRepository\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Repository/ChannelRepositoryTest.php

		-
			message: "#^Property App\\\\Tests\\\\Functional\\\\Repository\\\\ChannelRepositoryTest\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManagerInterface\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/Functional/Repository/ChannelRepositoryTest.php

		-
			message: "#^Call to an undefined method Sylius\\\\Component\\\\Locale\\\\Provider\\\\LocaleProviderInterface\\:\\:method\\(\\)\\.$#"
			count: 2
			path: tests/Locale/Context/RequestAcceptLanguageLocaleContextTest.php

		-
			message: "#^Method App\\\\Tests\\\\Locale\\\\Context\\\\RequestAcceptLanguageLocaleContextTest\\:\\:provideAcceptHeadersAndAvailableLocaleCodes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Locale/Context/RequestAcceptLanguageLocaleContextTest.php

		-
			message: "#^Method App\\\\Tests\\\\Locale\\\\Context\\\\RequestAcceptLanguageLocaleContextTest\\:\\:setLocalesOnLocaleProvider\\(\\) has parameter \\$availableLocaleCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Locale/Context/RequestAcceptLanguageLocaleContextTest.php

		-
			message: "#^Method App\\\\Tests\\\\Locale\\\\Context\\\\RequestAcceptLanguageLocaleContextTest\\:\\:testCanReturnLocaleBasedOnAcceptLanguageHeader\\(\\) has parameter \\$availableLocaleCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Locale/Context/RequestAcceptLanguageLocaleContextTest.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\AnamnesisServiceClient\\:\\:getQuestionnaireSession\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/AnamnesisServiceClient.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\AnamnesisServiceClient\\:\\:updateQuestionnaireSession\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/AnamnesisServiceClient.php

		-
			message: "#^Access to private property \\$id of parent class App\\\\Entity\\\\Order\\\\PreferredOrderItem\\.$#"
			count: 1
			path: tests/Mocks/Entity/TestPreferredOrderItem.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Mocks\\\\Entity\\\\TestSupplierDoctor\\:\\:\\$id\\.$#"
			count: 1
			path: tests/Mocks/Entity/TestSupplierDoctor.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\KatanaClient\\:\\:getResponses\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/KatanaClient.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\KatanaClient\\:\\:request\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/KatanaClient.php

		-
			message: "#^Parameter \\#1 \\$body of class Symfony\\\\Component\\\\HttpClient\\\\Response\\\\MockResponse constructor expects iterable\\<string\\|Throwable\\>\\|string, string\\|false given\\.$#"
			count: 5
			path: tests/Mocks/KatanaClient.php

		-
			message: "#^Method class@anonymous/tests/Mocks/MollieApiClientMock\\.php\\:15\\:\\:send\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/MollieApiClientMock.php

		-
			message: "#^Call to an undefined method Mollie\\\\Api\\\\Endpoints\\\\PaymentEndpoint\\:\\:expects\\(\\)\\.$#"
			count: 3
			path: tests/Mollie/Payum/PaymentsAPI/Action/AuthorizeActionTest.php

		-
			message: "#^Parameter \\#1 \\$normalizer of class App\\\\Mollie\\\\Payum\\\\PaymentsAPI\\\\CreatePaymentFactory constructor expects Symfony\\\\Component\\\\Serializer\\\\Normalizer\\\\NormalizerInterface, object\\|null given\\.$#"
			count: 1
			path: tests/Mollie/Payum/PaymentsAPI/Action/AuthorizeActionTest.php

		-
			message: "#^Call to an undefined method Mollie\\\\Api\\\\Endpoints\\\\PaymentEndpoint\\:\\:expects\\(\\)\\.$#"
			count: 1
			path: tests/Mollie/Payum/PaymentsAPI/Action/StatusActionTest.php

		-
			message: "#^Call to an undefined method Mollie\\\\Api\\\\Endpoints\\\\PaymentEndpoint\\:\\:method\\(\\)\\.$#"
			count: 1
			path: tests/Mollie/Payum/PaymentsAPI/Action/StatusActionTest.php

		-
			message: "#^Parameter \\#1 \\$normalizer of class App\\\\Mollie\\\\Payum\\\\PaymentsAPI\\\\CreatePaymentFactory constructor expects Symfony\\\\Component\\\\Serializer\\\\Normalizer\\\\NormalizerInterface, object\\|null given\\.$#"
			count: 1
			path: tests/Mollie/Payum/PaymentsAPI/CreatePaymentFactoryTest.php

		-
			message: "#^Method App\\\\Tests\\\\Order\\\\EventListener\\\\AbandonedCartListenerTest\\:\\:postUpdateOrderProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Order/EventListener/AbandonedCartListenerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Order\\\\EventListener\\\\AbandonedCartListenerTest\\:\\:preUpdateOrderProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Order/EventListener/AbandonedCartListenerTest.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Order\\\\MessageHandler\\\\CheckCartAbandonmentHandlerTest\\:\\:\\$handler\\.$#"
			count: 2
			path: tests/Order/MessageHandler/CheckCartAbandonmentHandlerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Order\\\\MessageHandler\\\\CheckCartAbandonmentHandlerTest\\:\\:orderProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Order/MessageHandler/CheckCartAbandonmentHandlerTest.php

		-
			message: "#^Method OrderProcessing\\\\AdminOrderProcessingTest\\:\\:provideNonAllowedPrescriptionStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/OrderProcessing/AdminOrderProcessingTest.php

		-
			message: "#^Method OrderProcessing\\\\AdminOrderProcessingTest\\:\\:provideNonAllowedStates\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/OrderProcessing/AdminOrderProcessingTest.php

		-
			message: "#^Cannot call method setCostPrice\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariantInterface\\|null\\.$#"
			count: 8
			path: tests/OrderProcessing/OrderCostPricesRecalculatorTest.php

		-
			message: "#^Method App\\\\Tests\\\\OrderProcessing\\\\OrderCostPricesRecalculatorTest\\:\\:orderProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/OrderProcessing/OrderCostPricesRecalculatorTest.php

		-
			message: "#^Method App\\\\Tests\\\\OrderProcessing\\\\OrderCostPricesRecalculatorTest\\:\\:testCanProcess\\(\\) has parameter \\$expectedOrderItemCostPriceTotals with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/OrderProcessing/OrderCostPricesRecalculatorTest.php

		-
			message: "#^Parameter \\#1 \\$subject of method App\\\\Promotion\\\\Action\\\\PercentageDiscountWithExcludedProductsPromotionActionCommand\\:\\:execute\\(\\) expects App\\\\Entity\\\\Order\\\\Order, PHPUnit\\\\Framework\\\\MockObject\\\\MockObject&Sylius\\\\Component\\\\Promotion\\\\Model\\\\PromotionSubjectInterface given\\.$#"
			count: 1
			path: tests/Promotion/Action/PercentageDiscountWithExcludedProductsPromotionActionCommandTest.php

		-
			message: "#^Method App\\\\Tests\\\\Security\\\\Cors\\\\CorsAllowOriginTester\\:\\:provideEnvironmentAndUrls\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Security/Cors/CorsAllowOriginTester.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Security/Cors/CorsAllowOriginTester.php

		-
			message: "#^Method App\\\\Tests\\\\Security\\\\UserProvider\\\\UsernameAndCustomerPoolProviderTest\\:\\:provideUnmodifiedMethodCalls\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Security/UserProvider/UsernameAndCustomerPoolProviderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Security\\\\UserProvider\\\\UsernameAndCustomerPoolProviderTest\\:\\:testItForwardsCallsToUnmodifiedMethodsToDecoratedUserProvider\\(\\) has parameter \\$arguments with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Security/UserProvider/UsernameAndCustomerPoolProviderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\AccountCompletionTokenTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/AccountCompletionTokenTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\CountryCodeTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/CountryCodeTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\LastOrderAtTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/LastOrderAtTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\MarketingSubscriptionUuidTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/MarketingSubscriptionUuidTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\OptInDirectMailTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/OptInDirectMailTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderCountTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/TotalOrderCountTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValueCurrencyCodeTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/TotalOrderValueCurrencyCodeTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Customer\\\\TotalOrderValueTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Customer/TotalOrderValueTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\MarketingSubscriptionUuidTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/MarketingSubscription/MarketingSubscriptionUuidTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\OptInEmailSubscriptionTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/MarketingSubscription/OptInEmailSubscriptionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\OptInEmailTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/MarketingSubscription/OptInEmailTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\MarketingSubscription\\\\OptInSmsSubscriptionTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/MarketingSubscription/OptInSmsSubscriptionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\CancelledByTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/CancelledByTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\CancelledByTest\\:\\:provideInvalidContext\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/CancelledByTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\CancelledByTest\\:\\:testSupportsWithInvalidContextReturnsFalse\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/CancelledByTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodNameTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/PaymentMethodNameTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodNameTest\\:\\:paymentsProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/PaymentMethodNameTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/PaymentMethodTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\PaymentMethodTest\\:\\:paymentsProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/PaymentMethodTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\ShipmentsTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/ShipmentsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\ShipmentsTest\\:\\:provideInvalidContext\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/ShipmentsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\ShipmentsTest\\:\\:testItDoesNotSupportOrderWithoutValidContext\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/ShipmentsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Order\\\\SubtotalTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Order/SubtotalTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\OrderItem\\\\MainTaxonTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/OrderItem/MainTaxonTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Payment\\\\DetailsTest\\:\\:getContext\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Payment/DetailsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\AssociationsTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/AssociationsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\ChannelTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/ChannelTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\InStockTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/InStockTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\OptionsTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/OptionsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPriceTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/StartingPriceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\StartingPricesTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/StartingPricesTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\TaxonsTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/TaxonsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\Util\\\\OptionsNaturalSorterTest\\:\\:provideRandomizedOptions\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/Util/OptionsNaturalSorterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\Util\\\\OptionsNaturalSorterTest\\:\\:testCanNaturalSortOptions\\(\\) has parameter \\$actualOptions with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/Util/OptionsNaturalSorterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\VariantsTest\\:\\:invalidSupportsProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/VariantsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Product\\\\VariantsTest\\:\\:testSupportsReturnsFalse\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Product/VariantsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\CodeTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/CodeTest.php

		-
			message: "#^Method Serializer\\\\CustomField\\\\ProductVariant\\\\DefaultUsageAdviceTest\\:\\:provideInvalidContext\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/DefaultUsageAdviceTest.php

		-
			message: "#^Method Serializer\\\\CustomField\\\\ProductVariant\\\\DefaultUsageAdviceTest\\:\\:testItDoesNotSupportInvalidContext\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/DefaultUsageAdviceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Embedded\\\\ProductTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/Embedded/ProductTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Embedded\\\\ProductTest\\:\\:supportsEmbeddedProductDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/Embedded/ProductTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\Embedded\\\\ProductTest\\:\\:testSupportsEmbeddedProduct\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/Embedded/ProductTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\ImageTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/ImageTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\OptionValuesTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/OptionValuesTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\PriceTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/PriceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\ProductVariant\\\\ProductAttributesTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/ProductVariant/ProductAttributesTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Shipment\\\\ItemsTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Shipment/ItemsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Shipment\\\\ItemsTest\\:\\:provideInvalidContext\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Shipment/ItemsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Shipment\\\\ItemsTest\\:\\:testItDoesNotSupportShipmentWithoutValidContext\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Shipment/ItemsTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackAndTraceLinkTest\\:\\:getContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/CustomField/Shipment/TrackAndTraceLinkTest.php

		-
			message: "#^Parameter \\#1 \\$object of method App\\\\Serializer\\\\CustomField\\\\Shipment\\\\TrackAndTraceLink\\:\\:add\\(\\) expects App\\\\Entity\\\\Shipping\\\\ShipmentInterface, Sylius\\\\Component\\\\Core\\\\Model\\\\ShipmentInterface given\\.$#"
			count: 4
			path: tests/Serializer/CustomField/Shipment/TrackAndTraceLinkTest.php

		-
			message: "#^Cannot call method setMainTaxon\\(\\) on App\\\\Entity\\\\Product\\\\ProductInterface\\|null\\.$#"
			count: 1
			path: tests/Serializer/Webhook/OpenApiWebhookPayloadSerializerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializerTest\\:\\:getExpectedErrorsForOrderAsWebhookPayload\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/Webhook/OpenApiWebhookPayloadSerializerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializerTest\\:\\:testWebhooks\\(\\) has parameter \\$expectedErrors with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Serializer/Webhook/OpenApiWebhookPayloadSerializerTest.php

		-
			message: "#^Method App\\\\Tests\\\\Serializer\\\\Webhook\\\\OpenApiWebhookPayloadSerializerTest\\:\\:webhookPayloadProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Serializer/Webhook/OpenApiWebhookPayloadSerializerTest.php

		-
			message: "#^Call to an undefined method Doctrine\\\\Persistence\\\\ObjectManager\\|PHPUnit\\\\Framework\\\\MockObject\\\\MockObject\\:\\:expects\\(\\)\\.$#"
			count: 10
			path: tests/ShopConfiguration/ChannelConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ChannelConfigurationLoaderTest\\:\\:createConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ChannelConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ChannelConfigurationLoaderTest\\:\\:createProcessedConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ChannelConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ChannelConfigurationLoaderTest\\:\\:createProcessedConfigurationWithConsultAndMedicationChannels\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ChannelConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\CustomerPoolConfigurationLoaderTest\\:\\:createConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/CustomerPoolConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\CustomerPoolConfigurationLoaderTest\\:\\:createProcessedConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/CustomerPoolConfigurationLoaderTest.php

		-
			message: "#^Call to an undefined method Doctrine\\\\Persistence\\\\ObjectManager\\|PHPUnit\\\\Framework\\\\MockObject\\\\MockObject\\:\\:expects\\(\\)\\.$#"
			count: 2
			path: tests/ShopConfiguration/ProductAssociationTypeConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ProductOptionConfigurationLoaderTest\\:\\:createConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ProductOptionConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ProductOptionConfigurationLoaderTest\\:\\:createProductOptionWithValues\\(\\) has parameter \\$productOptionValues with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ProductOptionConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ShippingCategoryConfigurationLoaderTest\\:\\:createConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ShippingCategoryConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\ShippingCategoryConfigurationLoaderTest\\:\\:createProcessedConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/ShippingCategoryConfigurationLoaderTest.php

		-
			message: "#^Cannot call method getCode\\(\\) on Sylius\\\\Component\\\\Channel\\\\Model\\\\ChannelInterface\\|false\\.$#"
			count: 1
			path: tests/ShopConfiguration/ShippingMethodConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\SupplierConfigurationLoaderTest\\:\\:createConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/SupplierConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\SupplierConfigurationLoaderTest\\:\\:createProcessedConfiguration\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/SupplierConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\ShopConfiguration\\\\TermQuestionConfigurationLoaderTest\\:\\:assertTermQuestionEqualsConfiguration\\(\\) has parameter \\$configuration with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/ShopConfiguration/TermQuestionConfigurationLoaderTest.php

		-
			message: "#^Parameter \\#1 \\$objectManager of class App\\\\ShopConfiguration\\\\TermQuestionConfigurationLoader constructor expects Doctrine\\\\Persistence\\\\ObjectManager, object\\|null given\\.$#"
			count: 1
			path: tests/ShopConfiguration/TermQuestionConfigurationLoaderTest.php

		-
			message: "#^Static property App\\\\Tests\\\\ShopConfiguration\\\\TermQuestionConfigurationLoaderTest\\:\\:\\$objectManager \\(Doctrine\\\\Persistence\\\\ObjectManager\\) does not accept object\\|null\\.$#"
			count: 1
			path: tests/ShopConfiguration/TermQuestionConfigurationLoaderTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Callback\\\\CancelConsultRequestTest\\:\\:stateProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Callback/CancelConsultRequestTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Callback\\\\DispatchOrderEventTest\\:\\:orderEventNameProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Callback/DispatchOrderEventTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderAftercareStateGuardTest\\:\\:provideCanCreate\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderAftercareStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderAftercareStateGuardTest\\:\\:provideCanOpen\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderAftercareStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderAftercareStateGuardTest\\:\\:provideCanSkip\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderAftercareStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderAftercareStateGuardTest\\:\\:testCanCreate\\(\\) has parameter \\$expectedResult with no type specified\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderAftercareStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderAftercareStateGuardTest\\:\\:testCanOpen\\(\\) has parameter \\$expectedResult with no type specified\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderAftercareStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderAftercareStateGuardTest\\:\\:testCanSkip\\(\\) has parameter \\$expectedResult with no type specified\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderAftercareStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanAddress\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanComplete\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanCompleteMedicalQuestionnaire\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSelectDeliveryService\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSelectPayment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSelectPreferredProducts\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSelectShipping\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSkipMedicalQuestionnaire\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSkipPayment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideCanSkipShipping\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanAddress\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanComplete\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanCompleteMedicalQuestionnaire\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanSelectDeliveryService\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanSelectPayment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanSelectPreferredProducts\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanSelectShipping\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanSkipMedicalQuestionnaire\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidCanSkipShipping\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderCheckoutStateGuardTest\\:\\:provideInvalidOrderForChannelForOrderAllowsSkippingPaymentMethodStep\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderCheckoutStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanAuthorize\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanCancel\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanPartiallyAuthorize\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanPartiallyPay\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanPay\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanRefund\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideCanRequestPayment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanAuthorize\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanCancel\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanPartiallyAuthorize\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanPartiallyPay\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanPay\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanRefund\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPaymentStateGuardTest\\:\\:provideInvalidCanRequestPayment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPaymentStateGuardTest.php

		-
			message: "#^Cannot call method setCode\\(\\) on App\\\\Entity\\\\Product\\\\ProductInterface\\|null\\.$#"
			count: 2
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideCanApprove\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideCanFollowUpOrder\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideCanOrder\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideCanSkip\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideInvalidCanApprove\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideInvalidCanFollowUpOrder\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideInvalidCanOrder\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideInvalidCanSkip\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideInvalidOrderForCanReady\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderPrescriptionStateGuardTest\\:\\:provideValidOrderForCanReady\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderPrescriptionStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanAwaitPrescription\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanCancel\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanCreate\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanMarkForReshipment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanNotifySupplier\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanReturn\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideCanShip\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanAwaitPrescription\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanCancel\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanCreate\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanMarkForReshipment\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanNotifySupplier\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanReturn\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidCanShip\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideInvalidOrderForTestInvalidCanReady\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderShippingStateGuardTest\\:\\:provideValidOrderForCanReady\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderStateGuardTest\\:\\:provideCanCompleteCheckout\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\OrderStateGuardTest\\:\\:provideInvalidCanCompleteCheckout\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/OrderStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\ShippingStateGuardTest\\:\\:provideCanAwaitPrescription\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/ShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\ShippingStateGuardTest\\:\\:provideCanCreateOrder\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/ShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\ShippingStateGuardTest\\:\\:provideInvalidCanAwaitPrescription\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/ShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\ShippingStateGuardTest\\:\\:provideInvalidCanCreateOrders\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/ShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\ShippingStateGuardTest\\:\\:provideInvalidCanReady\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/ShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\Guard\\\\ShippingStateGuardTest\\:\\:provideValidOrderForCanReady\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/Guard/ShippingStateGuardTest.php

		-
			message: "#^Method App\\\\Tests\\\\StateMachine\\\\StateApplier\\\\OrderStateApplierTest\\:\\:provideOrderForApplyNewStateAfterTransitionShippingState\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/StateMachine/StateApplier/OrderStateApplierTest.php

		-
			message: "#^Using nullsafe method call on non\\-nullable type Sylius\\\\Component\\\\Core\\\\Model\\\\AddressInterface\\. Use \\-\\> instead\\.$#"
			count: 1
			path: tests/StateMachine/StateResolver/OrderFraudCheckStateResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Dto\\\\OrderItemTest\\:\\:orderItemProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderItemTest.php

		-
			message: "#^Cannot call method getBillingAddress\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getDateOfBirth\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getEmailAddress\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getFirstName\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getGenderAtBirth\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getLanguage\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getLastName\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getPhoneNumber\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getQuantity\\(\\) on App\\\\Supplier\\\\Dto\\\\OrderItem\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getReference\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getReference\\(\\) on App\\\\Supplier\\\\Dto\\\\OrderItem\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getShippingAddress\\(\\) on App\\\\Supplier\\\\Dto\\\\Customer\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getSupplierProduct\\(\\) on App\\\\Supplier\\\\Dto\\\\OrderItem\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Cannot call method getUsageAdvice\\(\\) on App\\\\Supplier\\\\Dto\\\\OrderItem\\|null\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Class App\\\\Entity\\\\Order\\\\OrderItem constructor invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Dto\\\\OrderTest\\:\\:createSupplierServiceOrderItems\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Dto/OrderTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:provideBlueclinicServiceWithPerferredProducts\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:provideBlueclinicServiceWithoutPerferredProducts\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:provideDokGbServiceWithoutProducts\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:providePrescriptionWithPreferredProductsOrder\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:providePrescriptionWithoutPreferredProductsOrder\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:provideSupplierIsResolved\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:provideSupplierIsResolvedForPrescriptionServiceWithDoctor\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\Resolver\\\\SupplierIdentifierResolverTest\\:\\:resolveForShipmentWithoutSupplierDataProvider\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Supplier/Resolver/SupplierIdentifierResolverTest.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Supplier\\\\StateMachine\\\\StateResolver\\\\ShipmentStateResolverBasedOnSupplierOrderStatusTest\\:\\:\\$resolver\\.$#"
			count: 2
			path: tests/Supplier/StateMachine/StateResolver/ShipmentStateResolverBasedOnSupplierOrderStatusTest.php

		-
			message: "#^Access to an undefined property App\\\\Tests\\\\Supplier\\\\StateMachine\\\\StateResolver\\\\ShipmentStateResolverBasedOnSupplierOrderStatusTest\\:\\:\\$stateMachineMock\\.$#"
			count: 3
			path: tests/Supplier/StateMachine/StateResolver/ShipmentStateResolverBasedOnSupplierOrderStatusTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\StateMachine\\\\StateResolver\\\\ShipmentStateResolverBasedOnSupplierOrderStatusTest\\:\\:provideOrderShipmentStatus\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Supplier/StateMachine/StateResolver/ShipmentStateResolverBasedOnSupplierOrderStatusTest.php

		-
			message: "#^Call to an undefined method Sylius\\\\Component\\\\Order\\\\Model\\\\OrderItemInterface\\:\\:getVariant\\(\\)\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method getIdentifier\\(\\) on App\\\\Entity\\\\Supplier\\\\SupplierInterface\\|null\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method getSupplier\\(\\) on App\\\\Entity\\\\Product\\\\ProductVariantInterface\\|null\\.$#"
			count: 10
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method getSupplierFromUnits\\(\\) on Sylius\\\\Component\\\\Core\\\\Model\\\\ShipmentInterface\\|false\\.$#"
			count: 2
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method getUnits\\(\\) on Sylius\\\\Component\\\\Core\\\\Model\\\\ShipmentInterface\\|false\\.$#"
			count: 3
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method getVariant\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|null\\.$#"
			count: 10
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method isImmutable\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|null\\.$#"
			count: 8
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentItemsUpdaterTest\\:\\:dataProviderWhenAnOrderItemIsInvalid\\(\\) is unused\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentItemsUpdaterTest\\:\\:dataProviderWhenAnOrderItemIsInvalid\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentItemsUpdaterTest\\:\\:dataProviderWhenTheOrderItemShipmentStateIsInvalid\\(\\) is unused\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentItemsUpdaterTest\\:\\:dataProviderWhenTheOrderItemShipmentStateIsInvalid\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentItemsUpdaterTest\\:\\:getOrderItemsToSwitchBySuppliers\\(\\) has parameter \\$switchSuppliers with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#1 \\$func of method Doctrine\\\\Common\\\\Collections\\\\ReadableCollection\\<\\(int\\|string\\),Sylius\\\\Component\\\\Shipping\\\\Model\\\\ShipmentUnitInterface\\>\\:\\:map\\(\\) expects Closure\\(Sylius\\\\Component\\\\Shipping\\\\Model\\\\ShipmentUnitInterface\\)\\: Sylius\\\\Component\\\\Order\\\\Model\\\\OrderItemInterface, Closure\\(App\\\\Entity\\\\Order\\\\OrderItemUnit\\)\\: Sylius\\\\Component\\\\Order\\\\Model\\\\OrderItemInterface given\\.$#"
			count: 2
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#1 \\$orderItem of static method App\\\\Supplier\\\\Exception\\\\InvalidOrderItemException\\:\\:createForMissingShipment\\(\\) expects App\\\\Entity\\\\Order\\\\OrderItem, App\\\\Entity\\\\Order\\\\OrderItem\\|false given\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#1 \\$string of function crc32 expects string, string\\|null given\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#1 \\.\\.\\.\\$orderItems of class App\\\\Supplier\\\\OrderItemCollection constructor expects App\\\\Entity\\\\Order\\\\OrderItem, Sylius\\\\Component\\\\Order\\\\Model\\\\OrderItemInterface\\|false given\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#2 \\$orderItem of static method App\\\\Supplier\\\\Exception\\\\InvalidOrderItemException\\:\\:createForInvalidOrder\\(\\) expects App\\\\Entity\\\\Order\\\\OrderItem, App\\\\Entity\\\\Order\\\\OrderItem\\|false given\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#3 \\$currentLocale of static method App\\\\Tests\\\\Util\\\\Factory\\\\ProductVariantFactory\\:\\:createPrefilled\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, string\\|null given\\.$#"
			count: 2
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Parameter \\#4 \\$fallbackLocale of static method App\\\\Tests\\\\Util\\\\Factory\\\\ProductVariantFactory\\:\\:createPrefilled\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentItemsUpdaterTest.php

		-
			message: "#^Cannot call method getPreferredItems\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentUpdaterTest.php

		-
			message: "#^Cannot call method getVariant\\(\\) on App\\\\Entity\\\\Order\\\\OrderItem\\|false\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentUpdaterTest\\:\\:invalidOrderShippingStateProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\Supplier\\\\SupplierShipmentUpdaterTest\\:\\:validShippingStateProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Supplier/SupplierShipmentUpdaterTest.php

		-
			message: "#^Method App\\\\Tests\\\\TrackAndTrace\\\\TrackAndTraceLinkResolverTest\\:\\:provideInvalidShipments\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/TrackAndTrace/TrackAndTraceLinkResolverTest.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\AddressFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/AddressFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\AddressFactory\\:\\:createWithArguments\\(\\) has parameter \\$pickupPointAddress with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/AddressFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\BusinessUnitFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/BusinessUnitFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ChannelFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ChannelFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ChannelFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ChannelFactory.php

		-
			message: "#^Offset 'id' on non\\-empty\\-array in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: tests/Util/Factory/ChannelFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ChannelPricingFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ChannelPricingFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ChannelPricingFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ChannelPricingFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\CurrencyFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/CurrencyFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\CustomerFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/CustomerFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\CustomerFactory\\:\\:createCustomerPool\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/CustomerFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\CustomerFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/CustomerFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\LocaleFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/LocaleFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\MarketingSubscriptionFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/MarketingSubscriptionFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\MarketingSubscriptionFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/MarketingSubscriptionFactory.php

		-
			message: "#^Call to an undefined method App\\\\Tests\\\\Mocks\\\\Entity\\\\TestOrder\\:\\:addPreviousItem\\(\\)\\.$#"
			count: 1
			path: tests/Util/Factory/OrderFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\OrderFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/OrderFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\OrderFactory\\:\\:createOrderItemUnit\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/OrderFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\OrderFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/OrderFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\OrderFactory\\:\\:createPrefilled\\(\\) has parameter \\$locales with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/OrderFactory.php

		-
			message: "#^Offset 'channel' on array on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: tests/Util/Factory/OrderFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\OrderItemFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/OrderItemFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\OrderItemFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/OrderItemFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\PreferredOrderItemFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/PreferredOrderItemFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\PreferredOrderItemFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/PreferredOrderItemFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProblemDetailsFactory\\:\\:array\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProblemDetailsFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProblemDetailsFactory\\:\\:create\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProblemDetailsFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:addVariant\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:createPrefilled\\(\\) has parameter \\$locales with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:createProductAttribute\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductFactory\\:\\:createProductAttributeValue\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductImageFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductImageFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductImageFactory\\:\\:createPreFilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductImageFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductTaxonFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductTaxonFactory.php

		-
			message: "#^Offset 'id' on non\\-empty\\-array in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: tests/Util/Factory/ProductTaxonFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductVariantFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductVariantFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductVariantFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductVariantFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ProductVariantFactory\\:\\:createPrefilled\\(\\) has parameter \\$locales with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ProductVariantFactory.php

		-
			message: "#^Offset 'currentLocale' on array on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: tests/Util/Factory/ProductVariantFactory.php

		-
			message: "#^Offset 'fallbackLocale' on array on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: tests/Util/Factory/ProductVariantFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\PromotionCouponFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/PromotionCouponFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\PromotionCouponFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/PromotionCouponFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\PromotionFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/PromotionFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\PromotionFactory\\:\\:createPreFilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/PromotionFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ShipmentFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ShipmentFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ShipmentFactory\\:\\:createShippingMethod\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ShipmentFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ShopBillingDataFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ShopBillingDataFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ShopUserFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ShopUserFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\ShopUserFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/ShopUserFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\SupplierDoctorFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/SupplierDoctorFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\TaxonFactory\\:\\:create\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/TaxonFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\TaxonFactory\\:\\:createPrefilled\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/TaxonFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\TaxonFactory\\:\\:createPrefilled\\(\\) has parameter \\$locales with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/TaxonFactory.php

		-
			message: "#^Offset 'id' on non\\-empty\\-array in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: tests/Util/Factory/TaxonFactory.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\Factory\\\\TranslationFactory\\:\\:addTranslation\\(\\) has parameter \\$translations with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/Factory/TranslationFactory.php

		-
			message: "#^Property App\\\\Tests\\\\Util\\\\JsonSchema\\\\Constraints\\\\Factory\\:\\:\\$constraintMap type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Util/JsonSchema/Constraints/Factory.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$required\\.$#"
			count: 1
			path: tests/Util/JsonSchema/Constraints/UndefinedConstraint.php

		-
			message: "#^Method App\\\\Tests\\\\Util\\\\JsonSchema\\\\Constraints\\\\UndefinedConstraint\\:\\:check\\(\\) has parameter \\$fromDefault with no type specified\\.$#"
			count: 1
			path: tests/Util/JsonSchema/Constraints/UndefinedConstraint.php
