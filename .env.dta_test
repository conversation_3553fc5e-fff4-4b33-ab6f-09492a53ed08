APP_DEBUG=0
APP_ENV=dta_test

DATABASE_HOST=checkout-service-test.csv7sodpridp.eu-central-1.rds.amazonaws.com
DATABASE_NAME=checkout_service
DATABASE_HOST_READONLY=checkout-service-test.csv7sodpridp.eu-central-1.rds.amazonaws.com
DATABASE_NAME_READONLY=checkout_service

# Use only in shop config; URL will be fetched from the business unit
# Currently unavailable, but this should not be the API host
FRONTEND_FOLLOW_UP_APP_URL=https://test--blueclinic.netlify.app
# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN_APP_URL=https://test--dokteronline.netlify.app
# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN2_APP_URL=https://test--doctoronline-uk.netlify.app

JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/dta_test/public.pem
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/dta_test/private.pem

# Current Auth0 tenant
AUTH0_DOMAIN=test-ehvg.eu.auth0.com
AUTH0_AUDIENCE='["api://commerce-system.dokteronline"]'

SUPPLIER_SERVICE_API_BASE_URI=https://pharmacies.sbtest.nl/api/
SUPPLIER_SERVICE_AUTH0_AUDIENCE=api://supplier-service

ANAMNESIS_SERVICE_API_BASE_URI=https://anamnesis-service.sbtest.nl/api/
ANAMNESIS_SERVICE_AUTH0_AUDIENCE=api://anamnesis-system.dokteronline

DOKTERONLINE_API_BASE_URI=https://dokteronline.sbaccept.nl
DOKTERONLINE_AUTH0_AUDIENCE=api://dok-beheer

CONSULT_SYSTEM_API_BASE_URI=https://consult.sbtest.nl/api/
CONSULT_SYSTEM_AUTH0_AUDIENCE=api://consult-system

COMMUNICATION_SYSTEM_API_BASE_URI=https://communication.sbtest.nl/api/

CHECKOUT_SERVICE_API_HOST=dokteronline.commerce.sbtest.nl
MESSENGER_TRANSPORT_DSN=doctrine://default
PARTNERIZE_CAMPAIGN_ID=null
REDIS_HOST=checkout-service-test.4oibn8.ng.0001.euc1.cache.amazonaws.com
REDIS_PORT=6379
SEND_ORDERS_TO_SUPPLIER_SEVICE=no
SYMFONY_DEPRECATIONS_HELPER=999999

# See \App\Tests\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/((deploy-preview-([0-9]*)|test)--((anamnesis-admin-)?dokteronline|blueclinic|doctoronline-uk)\.netlify\.app|consult\.sbtest\.nl)$'

SHOP_CONFIGURATION_DIRECTORY=dokteronline
SYLIUS_CHANNEL_PREFIX=dok

PUBLIC_STORAGE_BUCKET=commerce.dokteronline.public.s3.sbtest.nl

PIM_KATANA_API_BASE_URI=https://dokteronline-acc.katanapim.com/api/
PIM_KATANA_BASE_URI=https://dokteronline-acc.katanapim.com/

AUTH0_COMMERCE_SYSTEM_RWA_AUDIENCE=internal://commerce-system.dokteronline
AUTH0_COMMERCE_SYSTEM_RWA_CLIENT_ORGANIZATION=org_CbuNW0Uw6rp7lyOM

KLARNA_BASE_URI=https://api.playground.klarna.com
PAYPAL_BASE_URI=https://api-m.sandbox.paypal.com
CM_BASE_URI=https://testsecure.docdatapayments.com

BUSINESS_UNIT_DOKTERONLINE_API_HOSTNAME=dokteronline.commerce.sbtest.nl
BUSINESS_UNIT_DOCTORONLINE_API_HOSTNAME=doctoronline.commerce.sbtest.nl
BUSINESS_UNIT_BLUECLINIC_API_HOSTNAME=blueclinic.commerce.sbtest.nl

AUTO_CAPTURE_DELAY_PERIOD="15 minutes"

RABBITMQ_HOST=amqps://b-476a0f83-a232-40fb-aadd-aa1f34cde498.mq.eu-central-1.amazonaws.com
RABBITMQ_PORT=5671
