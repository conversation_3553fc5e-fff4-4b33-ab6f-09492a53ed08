secrets:
    composer_auth_json:
        file: '~/.composer/auth.json'
    npmrc:
        file: '~/.npmrc'
    ssh_private_key:
        file: '~/.ssh/id_rsa'

services:
    web-frontend-dokteronline:
        image: 'ghcr.io/superbrave/frontend-dokteronline-checkout:edge-development'
        platform: 'linux/amd64'
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.web-frontend-dokteronline.rule=Host(`dokteronline.commerce.ehvg.dev`)"
            - "traefik.http.routers.web-frontend-dokteronline.entrypoints=https"
            - "traefik.http.routers.web-frontend-dokteronline.tls=true"
            - "traefik.docker.network=public"
        ports:
            - '80'
        networks:
            public: null
            private: null

    web-frontend-doctoronline:
        image: 'ghcr.io/superbrave/frontend-doctoronline-checkout:edge-development'
        platform: 'linux/amd64'
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.web-frontend-doctoronline.rule=Host(`doctoronline.commerce.ehvg.dev`)"
            - "traefik.http.routers.web-frontend-doctoronline.entrypoints=https"
            - "traefik.http.routers.web-frontend-doctoronline.tls=true"
            - "traefik.docker.network=public"
        ports:
            - '80'
        networks:
            public: null
            private: null

    web-frontend-blueclinic:
        image: 'ghcr.io/superbrave/frontend-blueclinic-checkout:edge-development'
        platform: 'linux/amd64'
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.web-frontend-blueclinic.rule=Host(`blueclinic.commerce.ehvg.dev`)"
            - "traefik.http.routers.web-frontend-blueclinic.entrypoints=https"
            - "traefik.http.routers.web-frontend-blueclinic.tls=true"
            - "traefik.docker.network=public"
        ports:
            - '80'
        networks:
            public: null
            private: null

    web-frontend-seeme:
        image: 'ghcr.io/superbrave/frontend-seeme-checkout:edge-development'
        platform: 'linux/amd64'
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.web-frontend-seeme.rule=Host(`seemenopause.commerce.ehvg.dev`)"
            - "traefik.http.routers.web-frontend-seeme.entrypoints=https"
            - "traefik.http.routers.web-frontend-seeme.tls=true"
            - "traefik.docker.network=public"
        ports:
            - '80'
        networks:
            public: null
            private: null

    web-api-and-admin:
        image: 'ghcr.io/superbrave/nginx:********'
        depends_on:
            - php
            - assets
        volumes:
            - '.:/var/www:ro'
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.web-api-and-admin.rule=HostRegexp(`^.+\\.commerce\\.ehvg\\.dev$`) && PathRegexp(`^/(api|payment|feeds|admin|bundles|build|media|_wdt|_profiler|health)(/.*)?`)"
            - "traefik.http.routers.web-api-and-admin.entrypoints=https"
            - "traefik.http.routers.web-api-and-admin.tls=true"
            - "traefik.docker.network=public"
        ports:
            - '80'
        networks:
            public: null
            private: null

    php: &php-container-settings
        image: 'ghcr.io/superbrave/php:8.3-xdebug'
        environment:
            XDEBUG: 'off' # See https://xdebug.org/docs/all_settings#mode for all modes. Defaults to 'off', advised: 'debug,develop'
        volumes:
            - 'filesystem:/var/www'
            - 'symfony_var:/var/www/var'
            - '../coverage:/opt/phpstorm-coverage' # If you run tests in PhpStorm with code coverage reports, this is where they are stored.
        depends_on:
            - database
            - redis
        working_dir: '/var/www'
        secrets:
            - source: 'composer_auth_json'
              target: '/home/<USER>/.composer/auth.json'
              mode: 0400
              uid: '1000'
              gid: '1000'
            - source: 'ssh_private_key'
              target: '/home/<USER>/.ssh/id_rsa'
              mode: 0400
              uid: '1000'
              gid: '1000'
        networks:
            public: null
            private: null

    consumer:
        <<: *php-container-settings
        image: 'ghcr.io/superbrave/php:8.3'
        command: >
            bin/console messenger:consume
            outbox
            external_async_priority
            internal_async_priority
            external_async
            internal_async
            fifo_webhook
            commerce_to_consult_system_transport
            create_supplier_service_shipment
            cancel_supplier_service_shipment
            affiliate_conversion
            check_cart_abandonment
            async_mailer_service
            async_delay
            async
            payments
            commerce_to_communication_system
            product_catalog_import
            --profile
            -vv
        depends_on:
            php:
                condition: service_healthy
            rabbitmq:
                condition: service_healthy
        deploy:
            mode: replicated
            replicas: 2
        restart: unless-stopped

    database: &database
        image: 'mariadb:10.11'
        environment:
            MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
            MYSQL_DATABASE: 'checkout_service'
            MYSQL_USER: 'checkout_service'
            MYSQL_PASSWORD: 'checkout_service'
        ports:
            - '127.0.0.1:3313:3306'
        volumes:
            - 'database:/var/lib/mysql'
            - '.docker/mariadb/init:/docker-entrypoint-initdb.d/'
        healthcheck:
            test: ["CMD", "mysqladmin" ,"ping", "-h", "database"]
            interval: 10s
            timeout: 10s
            retries: 5
        networks:
            private: null

    database-test:
        <<: *database
        ports:
            - '127.0.0.1:3314:3306'
        volumes:
            - 'database-test:/var/lib/mysql'
            - '.docker/mariadb/init:/docker-entrypoint-initdb.d/'
        healthcheck:
            test: ["CMD", "mysqladmin" ,"ping", "-h", "database-test"]
            interval: 10s
            timeout: 10s
            retries: 5

    redis:
        image: 'redis:6.0'
        volumes:
            - 'redis:/data'
        ports:
            - '127.0.0.1:6380:6379'
        command: 'redis-server --appendonly yes'
        networks:
            private: null

    assets:
        image: 'ghcr.io/superbrave/node:20'
        volumes:
            - 'filesystem:/var/www'
        working_dir: '/var/www'
        command: ['node', '/tmp/dummy.js']
        secrets:
            - source: 'npmrc'
              target: '/home/<USER>/.npmrc'
              mode: 0440
              uid: '1000'
              gid: '1000'

    rabbitmq:
        image: 'rabbitmq:3.13.3-management'
        environment:
            RABBITMQ_DEFAULT_USER: 'rabbitmq'
            RABBITMQ_DEFAULT_PASS: 'rabbitmq'
        healthcheck:
            test: 'rabbitmq-diagnostics -q ping'
            interval: '10s'
            timeout: '30s'
            retries: 5
        ports:
            - '127.0.0.1:15672:15672'
        networks:
            private: null

volumes:
    node_modules:
        driver: 'local'

    database:
        driver: 'local'

    database-test:
        driver: 'local'

    redis:
        driver: 'local'

    symfony_var:
        driver: 'local'

    filesystem:

x-mutagen:
    sync:
        defaults:
            permissions:
                defaultFileMode: 0664
                defaultDirectoryMode: 0755
            ignore:
                vcs: true
                paths:
                    - "/var/www/var"

        filesystem:
            alpha: "."
            beta: "volume://filesystem"
            mode: "two-way-resolved"
            configurationBeta:
                permissions:
                    defaultOwner: "id:1000"
                    defaultGroup: "id:1000"

networks:
    public:
        external: true
    private:
        external: false
