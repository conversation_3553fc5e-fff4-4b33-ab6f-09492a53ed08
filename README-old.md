# Checkout service
The microlith service responsible for the checkout process.

## Installation

### Prerequisites
To install this application you require the following tooling on your host machine:
* Git
* [Docker CE](https://docs.docker.com/install/)
* [Docker Compose](https://docs.docker.com/compose/install/)

For ease of use on Linux please see the [Post-installation steps for Linux](https://docs.docker.com/install/linux/linux-postinstall/).
Otherwise, you require to run all the Docker and Docker Compose commands with `sudo`.

Also, make sure your Docker daemon is authenticated with ECR as per [these instructions](https://mv-jira-1.atlassian.net/wiki/spaces/DEV/pages/1522106379/Login+to+Docker+ECR)
to be able to download the base container images.

### 1. Get the source
Clone the following repository to get the source code of the application:
```shell
<NAME_EMAIL>:superbrave/checkout-service.git
```

### 2. Install the development environment
There is a `Makefile` with commands to conveniently set up this project:
```shell
make install
```

This will:
* Build the Docker containers
* Create the `public` Docker network for this application to communicate with other applications within your development environment
* Install the Composer dependencies
* Install front-end dependencies and create compiled front-end files to run app

### 3. Modify hosts file
Add the following line at the bottom of your hosts file, usually located at `/etc/hosts`.

### 4. (Re-)Install PHP dependencies through Composer
Installing of PHP dependencies can be run through a Docker container specifically configured for Composer.

The following command runs the Composer container with the `install` command:
```shell
make install-dependencies
```

To use the same container for requiring a new dependency (eg. symfony/thanks) you can use the following command:
```shell
docker-compose run --rm composer require --dev symfony/thanks
```

## Usage
To run the application execute the following command:
```shell
make start
```
or if you want to run the containers on the foreground with logging:
```shell
make start-foreground
```

This will also start a NodeJS container to (re)build the required assets and watches for changes made
to the source files.

### Accessing Sylius Administration Panel

1. Navigate to https://dokteronline.commerce.ehvg.dev/admin
2. Log in with username: admin, password: admin

### Secrets management & adding a secret
The checkout-service uses the [encrypted secrets management](https://symfony.com/doc/current/configuration/secrets.html)
to store encrypted secrets. The private keys used to decrypt the secrets for the test, acceptance
and production environments will be stored inside a Kubernetes secret separate from the Helm deployment configuration.

To store a secret for a specific server environment use the following command:
```bash
docker-compose run php sh -c 'echo -n "value" | APP_ENV=<server-environment> ./bin/console secrets:set VARIABLE_NAME -'
```

The `<server-environment>` must be one of the following:
* development
* test
* acceptance
* production

### Fixtures

#### Loading fixtures
Execute the following command to load the fixtures into the database container:
```shell
make load-fixtures
```
OR using composer inside the PHP container
```shell
docker-compose run --rm php sh
composer db-reload
```

#### Customizing fixtures
Fixtures are located in `config/packages/dev/fixtures` and are separated by entity name (ie. address, order, etc.).

*Note: fixtures have an optional `priority` parameter (higher === sooner) for customizing the order in which the fixtures are loaded.*

More information: https://docs.sylius.com/en/1.9/book/architecture/fixtures.html

### (Unit) testing
To run automated testing execute the following command:
```shell
make test
```

### Checking and fixing code standards
In order to test if the files match the coding standards use the following commands.

To fix PHP code standards violations you're able to run the PHP CS fixer through the following command:
```shell
make fix-code-style
```

For each language you're able to check for code standards violations by running on of the following commands:
* PHP: `make check-code-style`
* SCSS: `docker-compose run --rm assets yarn check:scss`
* Javascript: `docker-compose run --rm assets yarn check:js`

### Accessing the database
During development, it's always nice to have a look inside the database with database tools like MySQL Workbench.
For this Docker Compose is configured to expose the MySQL port.
This allows you to access the database with user `root` on `127.0.0.1:3313` without a password.

## Documentation
You can find additional documentation on the application inside the `docs/` directory.

Important application architecture choices and decisions are documented [here](/docs/decisions/).

### Front-end
To build the latest front-end (useful for back-enders), just run `make install`.
Find more information on the front-end setup and its rules [here](/docs/frontend.md).
