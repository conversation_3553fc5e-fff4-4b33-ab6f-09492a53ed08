shop:
    title: Dokteronline.com

sylius:
    cart:
        order_item:
            contains_warnings: Er staan product(en) in uw winkelmand die u momenteel niet kunt bestellen.

    ui:
        choose_option: Maak een keuze
        awaiting_prescription: 'Afwachting van recept'
        declined: Afge<PERSON>zen
        mark_for_reshipment: Mark<PERSON> herverzending
        prescription_state: Recept status
        ready_for_consult: <PERSON><PERSON>ar voor consult
        doctor_unavailable: Gee<PERSON> dokt<PERSON> beschik<PERSON>
        waiting_for_response: Afwachting van reactie
        approved: Goedgekeurd
        skipped: Overgeslagen
        partially_returned: Gedeeltelijk geretourneerd
        partially_shipped: Gedeeltelijk verzonden
        doctor_name: Naam dokter
        daily_order_limit: Daglimiet orders
        amount_orders_today: Aantal orders vandaag
        amount_orders_today_updated_at: Aantal orders vandaag bijgewerkt op
        supplier_name: Leverancier(s)
        switch_supplier: Kies leverancier
        refund_order_items: Terugbetaling order regels
        switch_doctor: Kies dokter
        supplier_switch_unavailable: Leverancier kan niet gewijzigd worden
        doctor_switch_unavailable: Geen dokt<PERSON> beschikbaar
        confirm: Bevestigen
        reason: Reden
        dispatched_to_supplier_at: Verstuurd naar leverancier
        expired: Verlopen
        ready_for_fraud_check: Klaar voor fraude check
        postcode: Postcode
        supplier_shipment: Leverancier & verzending
        address_not_editable:
            title: Het adres kan niet bewerkt worden
            text: Het is op dit moment niet mogelijk om een DHL PickupPoint adres te bewerken.
        refund_order_item:
            items: Order regel
            Shipment: Zending
            reason: Reden
        tickets: Tickets

    resource:
        approved: '%resource% is goedgekeurd.'
        cancelled: '%resource% is geannuleerd'
        updated: '%resource% is bijgewerkt.'

    payment:
        cannot_complete: Betaling kan niet worden voltooid omdat de betaling nog niet is gestart.
        failed_to_complete: De betaling kon niet worden voltooid vanwege een error.
app:
    ui:
        product_variant:
            caption: 'Bijschrift'

            medical_properties_segment: 'Medische eigenschappen'
            prescription_required: 'Receptplichtig'
            maximum_quantity_per_order: 'Maximum aantal per order'
            repeat_interval_in_days: 'Herhaal interval in dagen'

            supplier_segment: 'Informatie over de leverancier van dit product variant'
            select_country: 'Selecteer een land wanneer dit product variant alleen beschikbaar is voor één land'
            select_medication_channel: 'Selecteer een medicatie kanaal'
            medication_channel: 'Medicatie kanaal'
            supplier_variant_name: 'Variant naam'
            supplier_variant_code: 'Variant code'

        cart:
            multiple_consults_allowed: 'Mag een winkelwagen meerdere consulten hebben?'
            multiple_shipments_allowed: 'Zijn meerdere verzendingen mogelijk?'
            pickup_points_allowed: 'Zijn pickup points mogelijk?'

        note:
            create:
                success: 'De notitie is toegevoegd'
            author: 'Auteur'
            created_at: 'Geplaatst op'
            message: 'Bericht'
            add: 'Notitie toevoegen'
            title: 'Order notities'
            order: 'Order'

        supplier:
            singular: 'Leverancier'
            plural: 'Leveranciers'
            select: 'Selecteer een leverancier'

        financial:
            export:
                singular: 'Financiële export'

        fraud_check_state: 'Fraude check status'

        fraud_check:
            singular: 'Fraude check'
            orders: 'Fraude check orders'
            approve: 'Goedkeuren'
            hold: 'Zet in de wacht'
            cancel: 'Annuleer'

        refund:
            payment:
                reason: 'Reden'
                singular: 'Terugbetaling'
                plural: 'Terugbetalingen'
                state:
                    new: Nieuw
                    completed: Voltooid
                    declined: Afgewezen
                actions:
                    complete: Afronden
                    decline: Afkeuren
                edit:
                    success: 'Terugbetaling voor bestelling %orderNumber% is %state%.'
                    error: 'Er is een fout opgetreden bij het bijwerken van de terugbetaling voor bestelling %orderNumber%.'
        refund_payments: 'Terugbetalingen'
        edit_refund_payment: 'Terugbetaling bewerken'

        new_supplier: 'Nieuwe leverancier'
        edit_supplier: 'Bewerken van supplier'

        suppliers: 'Leveranciers'

        handlingFee: 'Handling fee'

        addPrescriptionMedicationDirectlyToCart:
            label: 'Toestaan om receptplichtige medicatie direct aan de winkelwagen toe te voegen?'
            warning: 'Pas op! Deze optie heeft grote impact op het checkout proces.'

        payment_method:
            maximum_order_total: 'Maximum order totaalbedrag'
            is_different_address_allowed: 'Sta een verschillend factuur- en afleveradres toe.'
            is_payment_for_reshipment_costs_allowed: 'Sta betalingen toe voor het herverzenden van een order.'

        shipment:
            mark_for_reshipment:
                success: 'De zending is succesvol gemarkeerd voor herverzending.'
                error: 'De zending kon niet worden gemarkeerd voor herverzending door een fout.'

        checkout_completed_at: 'Checkout afgerond op'
order:
    switch_supplier:
        supplier: 'Leverancier'
    switch_doctor:
        doctor: 'Dokter'

sylius_refund:
    ui:
        declined: 'Afgewezen'

sylius_plus:
    ui:
        order_number_prefix: Bestelnummer voorvoegsel
    rbac:
        parent:
            refund_payments: Terugbetalingen
            suppliers: Leveranciers
            financialexport: Financiële export
        permissions:
            app_admin_order_product_names: 'Toon RX en Consult namen'
            app_admin_order_switch_supplier: 'Van leverancier wisselen'
            app_admin_order_notes: 'Toon en toevoegen van order notities'

        app_admin_order_switch_doctor: 'Van dokter wisselen'

feed:
    expected_delivery_period: 3-5 werkdagen
