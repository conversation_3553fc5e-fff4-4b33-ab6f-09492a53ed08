shop:
    title: Dokteronline.com

form:
    promotion_filter:
        help:
            excluded_products: 'Products that are excluded from the promotion.'

sylius:
    cart:
        order_item:
            contains_warnings: Your shopping cart contains one or more items that are currently unavailable for purchase.

    ui:
        choose_option: Choose option
        awaiting_prescription: 'Awaiting prescription'
        awaiting_customer_service: 'Awaiting customer service'
        declined: Declined
        mark_for_reshipment: Mark for reshipment
        prescription_state: Prescription state
        ready_for_consult: Ready for consult
        doctor_unavailable: Doctor unavailable
        waiting_for_response: Waiting for response
        approved: Approved
        skipped: Skipped
        partially_returned: Partially returned
        partially_shipped: Partially shipped
        doctor_name: Doctor name
        daily_order_limit: Daily order limit
        amount_orders_today: Amount orders today
        amount_orders_today_updated_at: Amount orders today updated at
        supplier_name: Supplier(s)
        switch_supplier: Choose supplier
        refund_order_items: Refund order items
        switch_doctor: Choose doctor
        supplier_switch_unavailable: Supplier switch unavailable
        doctor_switch_unavailable: No doctors available
        no_suppliers_found: There are no suppliers found for the selected order lines
        choose_order_lines: Choose order lines
        confirm: Confirm
        reason: Reason
        dispatched_to_supplier_at: Dispatched to supplier at
        expired: Expired
        cart: Cart
        awaiting_shipment: Awaiting shipment
        opened: Opened
        waiting_for_doctor_response: Waiting for doctor response
        doctor_responded: Doctor responded
        closed: Closed
        ready_for_fraud_check: 'Ready for fraud check'
        awaiting_fraud_check: Awaiting fraud check
        allowed: Allowed
        postcode: Postcode
        supplier_shipment: Supplier & shipment
        address_not_editable:
            title: Address is not editable
            text: You are not able to edit addresses that are shipped to a DHL PickupPoint at this moment.
        pause: Pause
        continue: Continue
        refund_order_item:
            items: Order items
            shipment: Shipment
            reason: Reason
        business_unit: Business unit
        business_units: Business units
        representative: 'Representative'
        tax_id: 'Tax ID'
        order_number_prefix: Order number prefix
        manage_business_units: 'Manage business units'
        company_name: Company name
        tickets: Tickets
        prescription_state_updated_at: Prescription state updated at

    resource:
        approved: '%resource% is approved.'
        cancelled: '%resource% is cancelled.'
        updated: '%resource% is updated.'
        paused: '%resource% handling is paused.'
        continued: '%resource% handling has continued.'

        marked: '%resource% is marked as %mark%'

    payment:
        cannot_complete: Payment cannot be completed as it has not yet been started.
        failed_to_complete: Payment could not be completed due to an error.

    oauth:
        login: 'Login with %provider%'
        automatic_redirect: 'You will be automatically redirected to %provider% in a few seconds.'

app:
    ui:
        fraud:
            config:
                menu:
                    postcode: Postcode
                    products: Consult products
                    payment_methods: Payment methods
        access_to_all_channels: 'Access to all channels'
        company_name: 'Company name'
        customer_pool: 'Customer pool'
        customer_pools: 'Customer pools'
        enabled_permission_checker: 'Enabled permission checker'
        from_ip: 'from IP'
        from_order_no: 'From Order no.'
        for_every: 'For every '
        inventory_source: 'Inventory source'
        issued_for_order: 'Issued for order'
        last_login_date: 'Last login date'
        last_login_ip: 'Last login IP'
        manage_customer_pools: 'Manage customer pools'
        manage_roles: 'Manage roles'
        manage_stock: 'Manage stock'
        month: 'Month'
        new_customer_pool: 'New customer pool'
        none: 'None'
        partially_ship: 'Partially ship'
        permission_management: 'Permission management'
        permissions: 'Permissions'
        permissions_tree: 'Permissions tree'
        promotion: 'Promotion'
        purchase: 'Purchase'
        report_for: 'report for'
        represented_by: 'Represented by'
        reserved: 'Reserved'
        roles: 'Roles'
        select: 'Select'
        spent_on_items_the_customer_gets: 'spent on items the customer gets'
        split_shipment: 'Split shipment'
        splitting_shipment_for_order: 'Splitting shipment for Order no. #%number%'
        transaction_date: 'Transaction date'
        returns:
            accept_and_go_to_refund: 'Accept and go to refund'
            accepted: 'Accepted'
            all_items: 'All items'
            cancel_this_return_request: 'Cancel this Return Request'
            cancelled: 'Cancelled'
            change_resolution: 'Change resolution'
            choose_your_desired_resolution: 'Choose your desired resolution:'
            create_replacement_order: 'Create replacement order'
            download: 'Download'
            download_tooltip: 'Download a PDF with the return request confirmation document'
            for_the_order: 'for the Order #%orderNumber%'
            items_partially_returned_to_inventory: 'Items partially returned to inventory'
            items_returned_to_inventory: 'Items returned to inventory'
            make_refund: 'Make a refund'
            new: 'New'
            package_received: 'Package received'
            package_with_part_of_items_received: 'Package with a part of items received'
            processing: 'Processing'
            reason: 'Reason'
            received: 'Received'
            refund: 'Refund'
            rejected: 'Rejected'
            repair: 'Repair'
            repaired_items_sent: 'Repaired items sent'
            replacement: 'Replacement'
            replacement_order_discount: 'Replacement order discount'
            replacement_orders: 'Replacement orders'
            request_return_or_refund: 'Request a Return or a Refund'
            resolution: 'Resolution'
            resolve: 'Resolve'
            resolved: 'Resolved'
            response: 'Response'
            return: 'Return'
            return_all_items: 'Return all items'
            return_items_to_inventory: 'Return items to inventory'
            return_rate: 'Return rate'
            return_request: 'Return request'
            return_request_confirmation: 'Return Request Confirmation'
            return_requests_allowed: 'Are customer return requests allowed?'
            show_replacement_order: 'Show replacement order'
            split: 'Split'
            split_return_request: 'Split return request'
            splitting_return_request: 'Splitting return request %id%'
            submit_a_request: 'Submit a request'
            submitted_at: 'Submitted at'
            to_return: 'To return'
        return_requests: 'Return requests'
        year: 'Year'

        new_fraud_postcode: Add fraud check postcode configuration item
        edit_fraud_postcode: Edit fraud check postcode configuration item
        fraud_postcodes: Fraud check postcode configuration

        new_fraud_product: Add fraud check consult product configuration item
        edit_fraud_product: Edit fraud check consult product configuration item
        fraud_products: Fraud check consult product configuration

        new_fraud_payment_method: Add fraud check payment method configuration item
        edit_fraud_payment_method: Edit fraud check payment method configuration item
        fraud_payment_methods: Fraud check payment method configuration

        edit_business_unit: Edit business unit

        cancellation:
            reason:
                # User reasons
                not_able_to_pay: I wasn't able to pay
                health_has_improved: My health issue has improved
                ordered_wrong_product: I accidentally ordered the wrong product
                found_better_price: I found a better price somewhere else
                # Doctor reasons
                declined_by_doctor: The order has been declined by the doctor
                # System reasons
                payment_expired: Payment is not paid in time
                # Customer Service reasons
                at_customers_request: 'At customers request'
                out_of_stock: 'Out of stock'
                returned_to_pharmacy: 'Returned to pharmacy'
                incorrect_or_damaged_delivery: 'Incorrect or damaged delivery'
                follow_up_order_was_cancelled: 'Follow up order was cancelled'
                too_early_or_too_many: 'Too early or too many'
                fraud: 'Fraud'
                no_reply_from_customer: 'No reply from customer'
                technical_error: 'Technical error'
                other: 'Other'
        note:
            create:
                success: 'The note has been added'
            author: 'Author'
            created_at: 'Created at'
            message: 'Message'
            add: 'Add note'
            title: 'Order notes'
            order: 'Order'

        product_variant:
            caption: 'Caption'

            medical_properties_segment: 'Medical properties'
            prescription_required: 'Prescription required'
            maximum_quantity_per_order: 'Maximum quantity per order'
            repeat_interval_in_days: 'Repeat interval in days'

            supplier_segment: 'Information about the supplier of this product variant'
            select_country: 'Select a country if this product variant is only available for one country'
            select_medication_channel: 'Select a medication channel'
            medication_channel: 'Medication channel'
            supplier_variant_name: 'Variant name'
            supplier_variant_code: 'Variant code'
            preferred_variant_for_minimum_daily_orders: 'Preferred variant for minimum daily orders'
            preferred_variant_for_minimum_daily_orders_help: 'If this value is set, it indicates the minimum number of
                times per day this variant is preferred over the same variant of another supplier while the variant has
                not been ordered more than the number set.'

        cart:
            multiple_consults_allowed: 'Are multiple consults in the cart allowed?'
            multiple_shipments_allowed: 'Are multiple shipments allowed?'
            pickup_points_allowed: 'Are pickup points allowed?'

        supplier:
            singular: 'Supplier'
            plural: 'Suppliers'
            select: 'Select supplier'

        financial:
            export:
                singular: 'Financial Export'

        fraud_check:
            singular: 'Fraud check'
            orders: 'Fraud check orders'
            approve: 'Approve'
            hold: 'Put on hold'
            cancel: 'Cancel'

            trusted: Trusted
            neutral: Neutral
            fraudulent: Fraudulent
        fraud_check_orders: 'Fraud check'

        new_supplier: 'New supplier'
        edit_supplier: 'Edit supplier'
        suppliers: 'Suppliers'

        refund:
            order_line:
                success: 'Successfully refunded the order line(s).'
                error: 'There was an error while refunding the order line(s).'
            reason:
                out_of_stock: 'Out of stock'
                incorrect_delivery: 'Incorrect delivery'
                damaged_delivery: 'Damaged delivery'
                returned: 'Returned'
                prescribed_medication_could_not_be_shipped: 'Prescribed medication could not be shipped'
                lost_during_shipment: 'Lost during shipment'
                refused_by_carrier: 'Refused by carrier'
                held_by_customs: 'Held by customs'
                seized_by_customs: 'Seized by customs'
            placeholder: 'Select a reason'
            cancellation:
                by: Cancelled by
                reason: Cancellation reason
            payment:
                reason: 'Refund reason'
                singular: 'Refund payment'
                plural: 'Refund payments'
                state:
                    new: New
                    completed: Completed
                    declined: Declined
                actions:
                    complete: Complete
                    decline: Decline
                edit:
                    success: 'Refund for order %orderNumber% has been %state%.'
                    error: 'An error occurred updating refund payment for order %orderNumber%.'
        refund_payments: 'Refund payments'
        edit_refund_payment: 'Edit refund payment'

        handlingFee: 'Handling fee'
        country: Country
        businessUnit: Business Unit
        postcode: Postcode
        consult_product: Consult product

        prescription_state: 'Prescription state'
        aftercare_state: 'Aftercare state'
        checkout_completed_at: 'Checkout completed at'
        fraud_check_state: 'Fraud check state'

        addPrescriptionMedicationDirectlyToCart:
            label: 'Allowed to add prescription medication directly to the shopping cart?'
            warning: 'Handle with care! This will alter the checkout flow significantly.'

        payment:
            id:
                singular: 'Payment ID'
                plural: "Payment ID's"
            payment_service_provider:
                plural: 'Payment Service Providers'
            force_pay: 'Force pay'

        payment_method:
            singular: Payment method
            is_different_address_allowed: 'Allow different billing and shipping address.'
            is_payment_for_reshipment_costs_allowed: 'Allow payment for reshipment costs'
            maximum_order_total: 'Maximum order total'
            icon: 'Icon'

        shipment:
            mark_for_reshipment:
                success: 'The shipment has successfully been marked for reshipment.'
                error: 'The shipment could not be marked for reshipment due to an error.'
            switched_supplier:
                success: 'The supplier has successfully been updated'

        customer:
            gender:
                m: Male
                f: Female
                u: Unknown

        business_unit: Business unit
        business_units: Business units
        representative: 'Representative'
        tax_id: 'Tax ID'
        order_number_prefix: Order number prefix
        manage_business_units: 'Manage business units'
        new_business_unit: 'New business unit'

    order:
        cancel:
            reason: 'Reason'
        switch_supplier:
            supplier: 'Supplier'
            note: 'Note'
        switch_doctor:
            doctor: 'Doctor'
        type:
            any: 'Any'
            rx: 'RX'
            otc: 'OTC'
        items:
            remove:
                success: 'Successfully removed the product from the order.'
                failed: 'Failed to remove the product from the order due to an error.'
            update:
                serviceProduct: 'Add a service product to this order'
                success: 'Succesfully added the product to the order.'
                failed:
                    error: 'Failed to add the product to the order due to an error.'
                    max_quantity: 'Failed to add the product to the order, the order already has this product.'
        fraud_check:
            update:
                postcode:
                    comment:
                        success: 'Successfully updated comment for fraud check.'

sylius_refund:
    ui:
        declined: 'Declined'

sylius_plus:
    rbac:
        parent:
            refund_payments: Refund payments
            suppliers: Suppliers
            financialexport: Financial export
            fraud_check_payment_method_config: Fraud check payment method configure filters
            fraud_check_product_config: Fraud check product configure filters
            fraud_check_postcode_config: Fraud check postcode configure filters
            fraudcheck: Fraud check
            fraud_check_postcode_comment: Fraud check postcode comment
        permissions:
            app_admin_order_product_names: Show RX and Consult names
            app_admin_order_switch_supplier: Switch supplier
            app_admin_order_notes: Show and add order notes
            app_admin_order_items_update: Update order items (returned shipment)
            app_fraud_check_approve_orders: Approve orders

        app_admin_fraud_check:
            order_index: View orders

        app_customer_fraud_check_trust_level: Modify customer trust level
        app_admin_order_switch_doctor: Switch doctor
        app_admin_order_pause: Pause handling of order
        app_admin_order_continue: Continue handling of order
feed:
    expected_delivery_period: 3-5 working days
