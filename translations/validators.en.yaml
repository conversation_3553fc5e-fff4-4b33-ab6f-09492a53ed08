app:
    supplier:
        handling_fee:
            min: Handling fee cannot be a negative number.
    register_customer:
            gender: Invalid gender given.
    business_unit:
        code:
            not_blank: 'Code cannot be blank'
            regex: 'Code can only be comprised of letters, numbers, dashes and underscores.'
            unique: 'Code has to be unique'
        company_name:
            not_blank: 'Company name cannot be blank'
        representative:
            not_blank: 'Representative cannot be blank'
        tax_id:
            not_blank: 'Tax ID cannot be blank'
    customer_pool:
        name:
            not_blank: 'Name cannot be blank'
        code:
            not_blank: 'Code cannot be blank'
            regex: 'Code can only be comprised of letters, numbers, dashes and underscores.'
            unique: 'Code has to be unique'
sylius:
    ui:
        invalid_order_number_prefix: Must contain exactly 2 uppercase characters
