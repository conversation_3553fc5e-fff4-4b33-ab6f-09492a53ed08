<?xml version="1.0" encoding="UTF-8"?>
<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         bootstrap="tests/bootstrap.php"
         convertDeprecationsToExceptions="false"
>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <exclude>
            <directory>src/Admin/Form/Extension</directory>
            <directory>src/Admin/Grid</directory>
            <directory>src/Admin/Menu</directory>
            <directory>src/Fixture</directory>
            <directory>src/Repository</directory>
        </exclude>
    </coverage>
    <php>
        <ini name="display_errors" value="1"/>
        <ini name="error_reporting" value="-1"/>
        <ini name="memory_limit" value="-1"/>
        <server name="APP_ENV" value="test" force="true"/>
        <server name="SHELL_VERBOSITY" value="-1"/>
        <server name="SYMFONY_PHPUNIT_REMOVE" value=""/>
        <server name="SYMFONY_PHPUNIT_VERSION" value="9.5"/>
        <server name="KERNEL_CLASS" value="\App\Kernel"/>
        <server name="SYMFONY_DEPRECATIONS_HELPER" value="verbose=0"/>
    </php>
    <testsuites>
        <testsuite name="unit">
            <directory>./tests</directory>
            <exclude>./tests/Behat</exclude>
            <exclude>./tests/Functional</exclude>
        </testsuite>
        <testsuite name="functional">
            <directory>./tests/Functional</directory>
        </testsuite>
    </testsuites>
    <extensions>
        <extension class="DAMA\DoctrineTestBundle\PHPUnit\PHPUnitExtension"/>
    </extensions>
    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener">
            <arguments>
                <array>
                    <element key="debug-class-loader">
                        <integer>0</integer>
                    </element>
                </array>
            </arguments>
        </listener>
    </listeners>
</phpunit>
