// Vendor
const path = require("path");
const Encore = require("@symfony/webpack-encore");
require("dotenv").config();

const syliusBundles = path.resolve(
  __dirname,
  "vendor/sylius/sylius/src/Sylius/Bundle/"
);
const uiBundleScripts = path.resolve(
  syliusBundles,
  "UiBundle/Resources/private/js/"
);
const uiBundleResources = path.resolve(
  syliusBundles,
  "UiBundle/Resources/private/"
);

if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

// Admin config
Encore.setOutputPath("public/build/admin/")
  .setPublicPath("/build/admin")
  .configureCssLoader((options) => {
    options.url = {
      filter: (url) => !url.startsWith("data:application/x-font-ttf"),
    };
  })
  .addEntry("admin", "./assets/admin/entry.js")
  .disableSingleRuntimeChunk()

    // When enabled, Webpack "splits" your files into smaller pieces for greater optimization.
    .splitEntryChunks()

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()

    /*
     * FEATURE CONFIG
     *
     * Enable & configure other features below. For a full
     * list of features, see:
     * https://symfony.com/doc/current/frontend.html#adding-more-features
     */
    .cleanupOutputBeforeBuild()
    .enableBuildNotifications()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())

    // configure Babel
    // .configureBabel((config) => {
    //     config.plugins.push('@babel/a-babel-plugin');
    // })
  .cleanupOutputBeforeBuild()
  .enableSourceMaps(!Encore.isProduction())
  .enableVersioning(Encore.isProduction())
  .enableSassLoader();

Encore.configureDevServerOptions((options) => {
  options.allowedHosts = '.ehvg.dev';
});

const adminConfig = Encore.getWebpackConfig();

adminConfig.resolve.alias["sylius/ui"] = uiBundleScripts;
adminConfig.resolve.alias["sylius/ui-resources"] = uiBundleResources;
adminConfig.resolve.alias["sylius/bundle"] = syliusBundles;
adminConfig.externals = Object.assign({}, adminConfig.externals, {
  window: "window",
  document: "document",
});
adminConfig.name = "admin";

module.exports = [adminConfig];
