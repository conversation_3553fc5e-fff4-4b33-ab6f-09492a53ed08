<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000042 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE sylius_plus_loyalty_rule_action (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule ADD action_id INT DEFAULT NULL, DROP actions');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule ADD CONSTRAINT FK_54C32E8D9D32F035 FOREIGN KEY (action_id) REFERENCES sylius_plus_loyalty_rule_action (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_54C32E8D9D32F035 ON sylius_plus_loyalty_rule (action_id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule DROP FOREIGN KEY FK_54C32E8D9D32F035');
        $this->addSql('DROP TABLE sylius_plus_loyalty_rule_action');
        $this->addSql('DROP INDEX UNIQ_54C32E8D9D32F035 ON sylius_plus_loyalty_rule');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule ADD actions LONGTEXT CHARACTER SET utf8 NOT NULL COLLATE `utf8_unicode_ci` COMMENT \'(DC2Type:array)\', DROP action_id');
    }
}
