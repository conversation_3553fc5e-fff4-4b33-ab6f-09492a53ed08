<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000040 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Initialize sylius_plus_loyalty_purchase table';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE sylius_plus_loyalty_purchase (id INT AUTO_INCREMENT NOT NULL, promotion_id INT DEFAULT NULL, code VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, value INT NOT NULL, enabled TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_FA819B5F77153098 (code), INDEX IDX_FA819B5F139DF194 (promotion_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_purchase ADD CONSTRAINT FK_FA819B5F139DF194 FOREIGN KEY (promotion_id) REFERENCES sylius_promotion (id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE sylius_plus_loyalty_purchase');
    }
}
