<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000049 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Relation between LoyaltyRules and Channels, and enabling channels for all already existing Loyalty Rules';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE sylius_plus_loyalty_rule_channels (loyalty_rule_id INT NOT NULL, channel_id INT NOT NULL, INDEX IDX_CCD8FF5D188FEBE2 (loyalty_rule_id), INDEX IDX_CCD8FF5D72F5A1AA (channel_id), PRIMARY KEY(loyalty_rule_id, channel_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule_channels ADD CONSTRAINT FK_6DC3AD55188FEBE2 FOREIGN KEY (loyalty_rule_id) REFERENCES sylius_plus_loyalty_rule (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule_channels ADD CONSTRAINT FK_6DC3AD5572F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id) ON DELETE CASCADE');

        /** @var array $channels */
        $channels = $this->connection->fetchAllAssociative('SELECT id FROM sylius_channel');

        /** @var array $loyaltyRules */
        $loyaltyRules = $this->connection->fetchAllAssociative('SELECT id FROM sylius_plus_loyalty_rule');

        foreach ($loyaltyRules as $loyaltyRule) {
            foreach ($channels as $channel) {
                $this->addSql('INSERT INTO sylius_plus_loyalty_rule_channels (loyalty_rule_id, channel_id) VALUES (:loyalty_rule_id, :channel_id)', [
                    'loyalty_rule_id' => $loyaltyRule['id'],
                    'channel_id' => $channel['id'],
                ]);
            }
        }
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE sylius_plus_loyalty_rule_channels');
    }
}
