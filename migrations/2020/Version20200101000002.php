<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000002 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE sylius_business_unit (id INT AUTO_INCREMENT NOT NULL, company_name VARCHAR(255) DEFAULT NULL, representative VARCHAR(255) DEFAULT NULL, tax_id VARCHAR(255) DEFAULT NULL, address_street VARCHAR(255) DEFAULT NULL, address_postcode VARCHAR(255) DEFAULT NULL, address_city VARCHAR(255) DEFAULT NULL, address_country_code VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE UTF8_unicode_ci ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE sylius_business_unit');
    }
}
