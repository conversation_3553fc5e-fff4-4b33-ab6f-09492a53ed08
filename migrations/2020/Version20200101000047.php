<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000047 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add transactions history to loyalty points account';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE sylius_plus_loyalty_points_transactions (loyalty_points_account_id INT NOT NULL, transaction_id INT NOT NULL, INDEX IDX_BFBB5A68CDF1749F (loyalty_points_account_id), INDEX IDX_BFBB5A682FC0CB0F (transaction_id), PRIMARY KEY(loyalty_points_account_id, transaction_id)) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_points_transaction (id INT AUTO_INCREMENT NOT NULL, details LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\', transaction_date DATETIME NOT NULL, type VARCHAR(255) NOT NULL, points_value INT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_points_transactions ADD CONSTRAINT FK_BFBB5A68CDF1749F FOREIGN KEY (loyalty_points_account_id) REFERENCES sylius_plus_loyalty_points_account (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_points_transactions ADD CONSTRAINT FK_BFBB5A682FC0CB0F FOREIGN KEY (transaction_id) REFERENCES sylius_plus_loyalty_points_transaction (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE sylius_plus_loyalty_points_transactions DROP FOREIGN KEY FK_BFBB5A682FC0CB0F');
        $this->addSql('DROP TABLE sylius_plus_loyalty_points_transactions');
        $this->addSql('DROP TABLE sylius_plus_loyalty_points_transaction');
    }
}
