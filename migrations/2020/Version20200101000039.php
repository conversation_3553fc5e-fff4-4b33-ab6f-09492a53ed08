<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000039 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE sylius_plus_loyalty_points_account (id INT AUTO_INCREMENT NOT NULL, balance INT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_customer ADD loyalty_points_account_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_customer ADD CONSTRAINT FK_7E82D5E6CDF1749F FOREIGN KEY (loyalty_points_account_id) REFERENCES sylius_plus_loyalty_points_account (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_7E82D5E6CDF1749F ON sylius_customer (loyalty_points_account_id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE sylius_customer DROP FOREIGN KEY FK_7E82D5E6CDF1749F');
        $this->addSql('DROP TABLE sylius_plus_loyalty_points_account');
        $this->addSql('DROP INDEX UNIQ_7E82D5E6CDF1749F ON sylius_customer');
        $this->addSql('ALTER TABLE sylius_customer DROP loyalty_points_account_id');
    }
}
