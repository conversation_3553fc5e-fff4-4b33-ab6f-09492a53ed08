<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20200101000048 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add loyalty purchase name to transaction details without it';
    }

    public function up(Schema $schema): void
    {
        /** @var array $transactions */
        $transactions = $this->connection->fetchAllAssociative('SELECT id, details FROM sylius_plus_loyalty_points_transaction');

        foreach ($transactions as $transaction) {
            /** @var array $unserializedDetails */
            $unserializedDetails = unserialize($transaction['details']);
            if (!array_key_exists('loyaltyPurchaseCode', $unserializedDetails)) {
                continue;
            }

            /** @var array $loyaltyPurchase */
            $loyaltyPurchase = $this->connection->fetchAssociative(
                'SELECT name FROM sylius_plus_loyalty_purchase WHERE code = :code',
                ['code' => $unserializedDetails['loyaltyPurchaseCode']],
            );

            $loyaltyPurchaseName = $loyaltyPurchase['name'];

            $unserializedDetails['loyaltyPurchaseName'] = $loyaltyPurchaseName;
            $details = serialize($unserializedDetails);

            $this->addSql('UPDATE sylius_plus_loyalty_points_transaction SET details = :details WHERE id = :id', [
                'details' => $details,
                'id' => $transaction['id'],
            ]);
        }
    }

    public function down(Schema $schema): void
    {
        // Leaving changes provided by this migration after reverting, does not have any impact to application
    }
}
