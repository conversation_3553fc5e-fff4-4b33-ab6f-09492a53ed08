<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

class Version20241004144342 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add unique constraint on gateway_name column in sylius_gateway_config table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX unique_gateway_name ON sylius_gateway_config (gateway_name)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX unique_gateway_name ON sylius_gateway_config');
    }
}
