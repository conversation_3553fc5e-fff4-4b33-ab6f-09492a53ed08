<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241030085028 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Schema update long text for comment on fraud_postcode';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode CHANGE comment comment LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode CHANGE comment comment MEDIUMTEXT DEFAULT NULL');
    }
}
