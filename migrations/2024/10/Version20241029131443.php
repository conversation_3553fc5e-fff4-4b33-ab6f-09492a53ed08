<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241029131443 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds hash, validated and validation_response to the sylius_address table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address ADD hash VARCHAR(255) DEFAULT NULL, ADD validated TINYINT(1) NOT NULL, ADD validation_response LONGTEXT DEFAULT \'{}\' NOT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address DROP hash, DROP validated, DROP validation_response');
    }
}
