<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241029103023 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Schema updates for json types';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries CHANGE context context JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE marketing_subscription CHANGE opt_in_sms_subscription opt_in_sms_subscription VARCHAR(255) NOT NULL COMMENT \'(DC2Type:subscriptionEnumType)\', CHANGE opt_in_email_subscription opt_in_email_subscription VARCHAR(255) NOT NULL COMMENT \'(DC2Type:subscriptionEnumType)\'');
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE attributes attributes JSON DEFAULT \'{}\' NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_adjustment CHANGE details details JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_gateway_config CHANGE config config JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_order CHANGE checkout_terms_answers checkout_terms_answers JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_payment CHANGE details details JSON NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_product_attribute_value CHANGE json_value json_value JSON DEFAULT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_address CHANGE pickup_point_address pickup_point_address JSON DEFAULT NULL COMMENT \'(DC2Type:json)\', CHANGE service_point_location_ids service_point_location_ids JSON DEFAULT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_shipment CHANGE variant_names variant_names JSON DEFAULT \'{}\' NOT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        // The down is exactly the same as the up, because of the package upgrade and which otherwise should be incompatible.
        $this->up($schema);
    }
}
