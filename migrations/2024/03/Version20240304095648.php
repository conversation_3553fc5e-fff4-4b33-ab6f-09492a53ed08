<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240304095648 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added customer audit entry';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries ADD customer_id INT DEFAULT NULL AFTER order_id');
        $this->addSql('ALTER TABLE audit_log_entries ADD CONSTRAINT FK_384ABAC79395C3F3 FOREIGN KEY (customer_id) REFERENCES sylius_customer (id)');
        $this->addSql('CREATE INDEX IDX_384ABAC79395C3F3 ON audit_log_entries (customer_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries DROP FOREIGN KEY FK_384ABAC79395C3F3');
        $this->addSql('DROP INDEX IDX_384ABAC79395C3F3 ON audit_log_entries');
        $this->addSql('ALTER TABLE audit_log_entries DROP customer_id');
    }
}
