<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240322145443 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds a country_id field to payment method fraud checks';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_payment_method ADD country_id INT NOT NULL');
        $this->addSql('ALTER TABLE fraud_payment_method ADD CONSTRAINT FK_ADB03352F92F3E70 FOREIGN KEY (country_id) REFERENCES sylius_country (id)');
        $this->addSql('CREATE INDEX IDX_ADB03352F92F3E70 ON fraud_payment_method (country_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_payment_method DROP FOREIGN KEY FK_ADB03352F92F3E70');
        $this->addSql('DROP INDEX IDX_ADB03352F92F3E70 ON fraud_payment_method');
        $this->addSql('ALTER TABLE fraud_payment_method DROP country_id');
    }
}
