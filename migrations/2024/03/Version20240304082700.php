<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240304082700 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added the customer trust level.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_customer ADD trust_level VARCHAR(20) NOT NULL COMMENT \'(DC2Type:TrustLevelEnumType)\'');
        $this->addSql("UPDATE sylius_customer SET trust_level = 'neutral';");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_customer DROP trust_level');
    }
}
