<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240308134412 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added canonical postcode for better performance';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode ADD canonical_postcode VARCHAR(255) NOT NULL');
        $this->addSql('CREATE INDEX IDX_3072FB94F1FD575E ON fraud_postcode (canonical_postcode)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_3072FB94F1FD575E ON fraud_postcode');
        $this->addSql('ALTER TABLE fraud_postcode DROP canonical_postcode');
    }
}
