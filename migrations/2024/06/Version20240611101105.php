<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240611101105 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds unique index to Auth0 id for an AdminUser.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX unique_auth0_id ON sylius_admin_user (auth0_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX unique_auth0_id ON sylius_admin_user');
    }
}
