<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240604150632 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds auth0_id to sylius_admin_user table to pair an Auth0 user to a Sylius admin user.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_admin_user ADD auth0_id VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_admin_user DROP auth0_id');
    }
}
