<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241114103916 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds tax_rate_in_percentage column to supplier_country_shipping table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier_country_shipping ADD tax_rate_in_percentage NUMERIC(10, 5) DEFAULT \'0\' NOT NULL AFTER shipping_cost');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier_country_shipping DROP tax_rate_in_percentage');
    }
}
