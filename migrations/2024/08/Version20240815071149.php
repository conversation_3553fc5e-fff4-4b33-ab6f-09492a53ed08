<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240815071149 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds table for OrderItemRefund.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE order_item_refund (id INT AUTO_INCREMENT NOT NULL, order_item_id INT NOT NULL, refund_payment_id INT NOT NULL, reason VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_C6671E11E415FB15 (order_item_id), INDEX IDX_C6671E11E739D017 (refund_payment_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE order_item_refund ADD CONSTRAINT FK_C6671E11E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('ALTER TABLE order_item_refund ADD CONSTRAINT FK_C6671E11E739D017 FOREIGN KEY (refund_payment_id) REFERENCES sylius_refund_refund_payment (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE order_item_refund');
    }
}
