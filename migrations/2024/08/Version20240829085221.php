<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240829085221 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds unique constraint to fraud_payment_method at business_unit_id, payment_method_id and country_id.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX unique_item ON fraud_payment_method');
        $this->addSql('CREATE UNIQUE INDEX unique_item ON fraud_payment_method (business_unit_id, payment_method_id, country_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX unique_item ON fraud_payment_method');
        $this->addSql('CREATE UNIQUE INDEX unique_item ON fraud_payment_method (business_unit_id, payment_method_id)');
    }
}
