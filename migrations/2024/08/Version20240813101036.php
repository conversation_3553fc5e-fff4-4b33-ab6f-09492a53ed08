<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240813101036 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds sylius_business_unit.order_number_prefix.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD order_number_prefix VARCHAR(255) NOT NULL');
        $this->addSql('
            UPDATE sylius_business_unit
            INNER JOIN sylius_order_sequence ON sylius_business_unit.id = sylius_order_sequence.business_unit_id
            SET sylius_business_unit.order_number_prefix = sylius_order_sequence.prefix
            WHERE sylius_business_unit.id = sylius_order_sequence.business_unit_id
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP order_number_prefix');
    }
}
