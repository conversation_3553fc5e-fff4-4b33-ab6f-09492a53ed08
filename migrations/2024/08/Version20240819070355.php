<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240819070355 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds order item refunds for all partially fulfilled orders and removes this state afterwards.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO order_item_refund (order_item_id, refund_payment_id, reason)
            SELECT oi.id, rp.id, "out_of_stock"
            FROM sylius_order_item oi
            INNER JOIN sylius_order o ON o.id = oi.order_id
            INNER JOIN sylius_refund_refund_payment rp ON rp.order_id = o.id
            INNER JOIN sylius_order_item_unit oiu ON oiu.order_item_id = oi.id
            INNER JOIN sylius_shipment s ON s.id = oiu.shipment_id
            WHERE o.state = "partially_fulfilled" AND s.state = "cancelled"
        ');

        $this->addSql('
            INSERT INTO order_item_refund (order_item_id, refund_payment_id, reason)
            SELECT oi.id, rp.id, "returned"
            FROM sylius_order_item oi
            INNER JOIN sylius_order o ON o.id = oi.order_id
            INNER JOIN sylius_refund_refund_payment rp ON rp.order_id = o.id
            INNER JOIN sylius_order_item_unit oiu ON oiu.order_item_id = oi.id
            INNER JOIN sylius_shipment s ON s.id = oiu.shipment_id
            WHERE o.state = "partially_fulfilled" AND s.state = "returned"
        ');

        $this->addSql('
            UPDATE sylius_order
            SET state = "fulfilled"
            WHERE state = "partially_fulfilled"
        ');
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException('This (data manipulation) migration is irreversible.');
    }
}
