<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240812135745 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds an unique constraint to payment_method_gateway_config on payment_method_id and gateway_config_id columns.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX payment_method_id_gateway_config_id_unique ON payment_method_gateway_config (payment_method_id, gateway_config_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX payment_method_id_gateway_config_id_unique ON payment_method_gateway_config');
    }
}
