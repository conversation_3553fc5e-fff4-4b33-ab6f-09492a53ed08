<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240528070823 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removes unused sylius_gateway_config entries.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `sylius_gateway_config`
            WHERE id NOT IN (
                SELECT gateway_config_id
                FROM `sylius_payment_method`
            );
        ');
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException('This migration is irreversible.');
    }
}
