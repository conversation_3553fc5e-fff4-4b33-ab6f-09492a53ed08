<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240528135334 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add additional address information and pickup point address to address entity';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address ADD additional_address_information VARCHAR(255) DEFAULT NULL, ADD pickup_point_address LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address DROP additional_address_information, DROP pickup_point_address');
    }
}
