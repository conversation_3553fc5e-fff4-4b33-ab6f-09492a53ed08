<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240517122331 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Creates product_variant_note table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE product_variant_note (id INT AUTO_INCREMENT NOT NULL, message LONGTEXT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_product_variant ADD note_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_product_variant ADD CONSTRAINT FK_A29B52326ED0855 FOREIGN KEY (note_id) REFERENCES product_variant_note (id)');
        $this->addSql('CREATE INDEX IDX_A29B52326ED0855 ON sylius_product_variant (note_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant DROP FOREIGN KEY FK_A29B52326ED0855');
        $this->addSql('DROP TABLE product_variant_note');
        $this->addSql('DROP INDEX IDX_A29B52326ED0855 ON sylius_product_variant');
        $this->addSql('ALTER TABLE sylius_product_variant DROP note_id');
    }
}
