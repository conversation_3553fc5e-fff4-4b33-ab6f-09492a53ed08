<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240524124444 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds items to the shipment entity.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment ADD variant_names LONGTEXT DEFAULT \'{}\' NOT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment DROP variant_names');
    }
}
