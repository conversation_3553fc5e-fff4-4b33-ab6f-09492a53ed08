<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240916085747 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds `canopy_deploy_product_back_in_stock_webhook_url` to `sylius_business_unit`.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD canopy_deploy_product_back_in_stock_webhook_url VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP canopy_deploy_product_back_in_stock_webhook_url');
    }
}
