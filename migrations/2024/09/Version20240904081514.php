<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240904081514 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removes business unit from order sequence table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_sequence DROP FOREIGN KEY FK_6F449F61A58ECB40');
        $this->addSql('DROP INDEX unique_business_unit ON sylius_order_sequence');
        $this->addSql('ALTER TABLE sylius_order_sequence DROP business_unit_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_sequence ADD business_unit_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_sequence ADD CONSTRAINT FK_6F449F61A58ECB40 FOREIGN KEY (business_unit_id) REFERENCES sylius_business_unit (id)');
        $this->addSql('CREATE UNIQUE INDEX unique_business_unit ON sylius_order_sequence (business_unit_id)');
    }
}
