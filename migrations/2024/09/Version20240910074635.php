<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240910074635 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds apiHostname to BusinessUnit';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD api_hostname VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP api_hostname');
    }
}
