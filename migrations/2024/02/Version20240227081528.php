<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240227081528 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added the fraud check config tables.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE fraud_payment_method (id INT AUTO_INCREMENT NOT NULL, payment_method_id INT NOT NULL, business_unit_id INT NOT NULL, INDEX IDX_ADB033525AA1164F (payment_method_id), INDEX IDX_ADB03352A58ECB40 (business_unit_id), UNIQUE INDEX unique_item (business_unit_id, payment_method_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fraud_postcode (id INT AUTO_INCREMENT NOT NULL, country_id INT NOT NULL, business_unit_id INT NOT NULL, postcode VARCHAR(255) NOT NULL, INDEX IDX_3072FB94F92F3E70 (country_id), INDEX IDX_3072FB94A58ECB40 (business_unit_id), UNIQUE INDEX unique_item (business_unit_id, postcode, country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fraud_product (id INT AUTO_INCREMENT NOT NULL, product_id INT NOT NULL, business_unit_id INT NOT NULL, INDEX IDX_2B86B15F4584665A (product_id), INDEX IDX_2B86B15FA58ECB40 (business_unit_id), UNIQUE INDEX unique_item (business_unit_id, product_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');

        $this->addSql('ALTER TABLE fraud_payment_method ADD CONSTRAINT FK_ADB033525AA1164F FOREIGN KEY (payment_method_id) REFERENCES sylius_payment_method (id)');
        $this->addSql('ALTER TABLE fraud_payment_method ADD CONSTRAINT FK_ADB03352A58ECB40 FOREIGN KEY (business_unit_id) REFERENCES sylius_business_unit (id)');

        $this->addSql('ALTER TABLE fraud_postcode ADD CONSTRAINT FK_3072FB94F92F3E70 FOREIGN KEY (country_id) REFERENCES sylius_country (id)');
        $this->addSql('ALTER TABLE fraud_postcode ADD CONSTRAINT FK_3072FB94A58ECB40 FOREIGN KEY (business_unit_id) REFERENCES sylius_business_unit (id)');

        $this->addSql('ALTER TABLE fraud_product ADD CONSTRAINT FK_2B86B15F4584665A FOREIGN KEY (product_id) REFERENCES sylius_product (id)');
        $this->addSql('ALTER TABLE fraud_product ADD CONSTRAINT FK_2B86B15FA58ECB40 FOREIGN KEY (business_unit_id) REFERENCES sylius_business_unit (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE fraud_payment_method');
        $this->addSql('DROP TABLE fraud_postcode');
        $this->addSql('DROP TABLE fraud_product');
    }
}
