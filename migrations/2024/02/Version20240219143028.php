<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240219143028 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index on type (locale) column of sylius_product_image';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX type_idx ON sylius_product_image (type)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX type_idx ON sylius_product_image');
    }
}
