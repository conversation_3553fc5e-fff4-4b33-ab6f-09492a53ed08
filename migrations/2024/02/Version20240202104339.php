<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240202104339 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added payment_state_updated_at and checkout_state_updated_at to sylius_order.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD payment_state_updated_at DATETIME DEFAULT NULL, ADD checkout_state_updated_at DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP payment_state_updated_at, DROP checkout_state_updated_at');
    }
}
