<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240227133900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the fraud_check_state column to the sylius_order table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_order ADD fraud_check_state VARCHAR(255) DEFAULT \'cart\' NOT NULL AFTER prescription_state,
            ADD fraud_check_state_updated_at DATETIME DEFAULT NULL AFTER prescription_state_updated_at
        ');
        $this->addSql('
            UPDATE sylius_order
            SET fraud_check_state = CASE
                WHEN state = "cart" THEN "cart"
                WHEN state = "cancelled" THEN "cancelled"
                WHEN payment_state = "awaiting_payment" THEN "awaiting_payment"
                ELSE "skipped"
            END
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP fraud_check_state, DROP fraud_check_state_updated_at');
    }
}
