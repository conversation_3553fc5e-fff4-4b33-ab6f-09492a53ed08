<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241219125652 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added indexes to sylius_customer table for faster search in Sylius admin.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX email_idx ON sylius_customer (email)');
        $this->addSql('CREATE INDEX first_name_idx ON sylius_customer (first_name)');
        $this->addSql('CREATE INDEX last_name_idx ON sylius_customer (last_name)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX email_idx ON sylius_customer');
        $this->addSql('DROP INDEX first_name_idx ON sylius_customer');
        $this->addSql('DROP INDEX last_name_idx ON sylius_customer');
    }
}
