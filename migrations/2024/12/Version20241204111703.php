<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241204111703 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Schema updates for json types';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address CHANGE validation_response validation_response JSON DEFAULT \'{}\' NOT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address CHANGE validation_response validation_response JSON DEFAULT \'{}\' NOT NULL COMMENT \'(DC2Type:json)\'');
    }
}
