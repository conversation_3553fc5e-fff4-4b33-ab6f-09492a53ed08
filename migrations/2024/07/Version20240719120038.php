<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240719120038 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds payment_method_gateway_config table and relationship to payment table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE payment_method_gateway_config (id INT AUTO_INCREMENT NOT NULL, payment_method_id INT DEFAULT NULL, gateway_config_id INT DEFAULT NULL, payment_service_provider_identifier VARCHAR(255) NOT NULL, daily_limit INT DEFAULT NULL, enabled TINYINT(1) NOT NULL, daily_usage INT NOT NULL, daily_usage_updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_52BBBA3F5AA1164F (payment_method_id), INDEX IDX_52BBBA3FF23D6140 (gateway_config_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE payment_method_gateway_config ADD CONSTRAINT FK_52BBBA3F5AA1164F FOREIGN KEY (payment_method_id) REFERENCES sylius_payment_method (id)');
        $this->addSql('ALTER TABLE payment_method_gateway_config ADD CONSTRAINT FK_52BBBA3FF23D6140 FOREIGN KEY (gateway_config_id) REFERENCES sylius_gateway_config (id)');
        $this->addSql('ALTER TABLE sylius_payment ADD payment_method_gateway_config_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_payment ADD CONSTRAINT FK_D9191BD4BEF4BF9 FOREIGN KEY (payment_method_gateway_config_id) REFERENCES payment_method_gateway_config (id)');
        $this->addSql('CREATE INDEX IDX_D9191BD4BEF4BF9 ON sylius_payment (payment_method_gateway_config_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment DROP FOREIGN KEY FK_D9191BD4BEF4BF9');
        $this->addSql('DROP TABLE payment_method_gateway_config');
        $this->addSql('DROP INDEX IDX_D9191BD4BEF4BF9 ON sylius_payment');
        $this->addSql('ALTER TABLE sylius_payment DROP payment_method_gateway_config_id');
    }
}
