<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240422095144 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'The canonical_postcode has been renamed for a more consistent naming convention across the platform.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode ADD postcode_canonical VARCHAR(255) NOT NULL AFTER canonical_postcode;');
        $this->addSql('CREATE INDEX IDX_3072FB94B3B67691 ON fraud_postcode (postcode_canonical)');

        $this->addSql(
            'UPDATE fraud_postcode as fp1
            INNER JOIN fraud_postcode as fp2 ON fp1.id = fp2.id
            SET fp1.`postcode_canonical` = fp2.canonical_postcode WHERE fp1.id = fp2.id;'
        );

        // This is a workaround for safe migrations.
        $this->addSql('ALTER TABLE fraud_postcode CHANGE canonical_postcode canonical_postcode VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode CHANGE canonical_postcode canonical_postcode VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`');
        $this->addSql('DROP INDEX IDX_3072FB94B3B67691 ON fraud_postcode');
        $this->addSql('ALTER TABLE fraud_postcode DROP postcode_canonical');
    }
}
