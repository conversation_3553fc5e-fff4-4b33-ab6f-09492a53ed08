<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240422121452 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop the canonical_postcode and cleanup the database.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_3072FB94F1FD575E ON fraud_postcode');
        $this->addSql('ALTER TABLE fraud_postcode DROP canonical_postcode');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode ADD canonical_postcode VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
        $this->addSql('CREATE INDEX IDX_3072FB94F1FD575E ON fraud_postcode (canonical_postcode)');
        $this->addSql(
            'UPDATE fraud_postcode as fp1
            INNER JOIN fraud_postcode as fp2 ON fp1.id = fp2.id
            SET fp1.canonical_postcode = fp2.postcode_canonical WHERE fp1.id = fp2.id;'
        );
    }
}
