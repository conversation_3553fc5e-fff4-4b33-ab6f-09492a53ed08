<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240403153501 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add comment to fraud_postcode table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode ADD comment mediumtext DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE fraud_postcode DROP comment');
    }
}
