<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240415132457 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add postcode canonical to address table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_address ADD postcode_canonical VARCHAR(255) DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_B97FF058B3B67691 ON sylius_address (postcode_canonical)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_B97FF058B3B67691 ON sylius_address');
        $this->addSql('ALTER TABLE sylius_address DROP postcode_canonical');
    }
}
