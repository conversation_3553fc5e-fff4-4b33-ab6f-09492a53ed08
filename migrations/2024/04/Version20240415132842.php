<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240415132842 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set all postcode canonical to lowercase and remove spaces';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'UPDATE sylius_address a
             JOIN (
                    SELECT id, REPLACE(LOWER(postcode), " ", "") as postcode_canonical
                    FROM sylius_address
             ) tmp ON a.id = tmp.id
             SET a.postcode_canonical = tmp.postcode_canonical'
        );
    }
}
