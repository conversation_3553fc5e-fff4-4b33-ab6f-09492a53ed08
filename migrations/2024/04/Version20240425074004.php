<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240425074004 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'LONGTEXT for usage_advice';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE original_preferred_order_item CHANGE usage_advice usage_advice LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item CHANGE usage_advice usage_advice LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE usage_advice usage_advice LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE original_preferred_order_item CHANGE usage_advice usage_advice VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
        $this->addSql('ALTER TABLE preferred_order_item CHANGE usage_advice usage_advice VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE usage_advice usage_advice VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
