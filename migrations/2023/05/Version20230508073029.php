<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230508073029 extends AbstractMigration
{
    private const string UPDATE_ORDER_STATE_UPDATED_AT = "
        UPDATE sylius_order as o
        LEFT JOIN sylius_shipment s on o.id = s.order_id AND s.shipped_at IS NOT NULL
        SET order_state_updated_at = CASE
            WHEN o.state = 'cart' THEN o.created_at
            WHEN o.state = 'new' THEN o.checkout_completed_at
            WHEN o.state = 'cancelled' THEN o.updated_at
            WHEN o.state = 'fulfilled' AND s.shipped_at IS NOT NULL THEN s.shipped_at
            ELSE o.updated_at
            END
        WHERE o.order_state_updated_at IS NULL;
    ";

    public function getDescription(): string
    {
        return 'Set updated field for changes on the order state.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD order_state_updated_at DATETIME DEFAULT NULL AFTER prescription_state_updated_at');
        $this->addSql(self::UPDATE_ORDER_STATE_UPDATED_AT);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP order_state_updated_at');
    }
}
