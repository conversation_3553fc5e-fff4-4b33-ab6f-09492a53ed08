<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230502124551 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update deprecated refund payment reasons.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE sylius_refund_refund_payment SET reason = 'Customer paid too much' WHERE reason = 'System note: Refund created due to difference with paid amount'");
        $this->addSql("UPDATE sylius_refund_refund_payment SET reason = 'Order was cancelled' WHERE reason NOT IN ('Order was cancelled', 'Customer paid too much') AND reason IS NOT NULL");
    }

    public function down(Schema $schema): void
    {
    }
}
