<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230511103243 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create the audit log entries table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE audit_log_entries (uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\', user_id INT DEFAULT NULL, order_id INT DEFAULT NULL, user_type VARCHAR(255) NOT NULL COMMENT \'(DC2Type:userTypeEnumType)\', username VARCHAR(255) DEFAULT NULL, message VARCHAR(255) NOT NULL, action_icon VARCHAR(255) NOT NULL, context LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', entry_type VARCHAR(255) NOT NULL, INDEX IDX_384ABAC7A76ED395 (user_id), INDEX IDX_384ABAC78D9F6D38 (order_id), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE audit_log_entries ADD CONSTRAINT FK_384ABAC7A76ED395 FOREIGN KEY (user_id) REFERENCES sylius_admin_user (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE audit_log_entries ADD CONSTRAINT FK_384ABAC78D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE audit_log_entries');
    }
}
