<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230509102202 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the aftercare_state column to the sylius_order table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("ALTER TABLE sylius_order ADD aftercare_state VARCHAR(255) DEFAULT 'cart' NOT NULL AFTER prescription_state");
        $this->addSql("
            UPDATE sylius_order o
            LEFT JOIN sylius_shipment s ON (o.id = s.order_id AND s.shipped_at IS NOT NULL)
            SET aftercare_state = CASE
                WHEN o.state <> 'cart' AND o.prescription_state = 'skipped' THEN 'skipped'
                WHEN o.state <> 'cart' AND o.follow_up_order_id IS NOT NULL THEN 'skipped'
                WHEN o.state = 'new' AND s.shipped_at IS NULL THEN 'awaiting_shipment'
                WHEN o.state = 'new' AND s.shipped_at IS NOT NULL THEN 'opened'
                WHEN o.state = 'fulfilled' AND s.shipped_at IS NOT NULL THEN 'opened'
                WHEN o.state = 'cancelled' THEN 'closed'
                ELSE 'cart'
            END
            WHERE o.aftercare_state = 'cart'
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP aftercare_state');
    }
}
