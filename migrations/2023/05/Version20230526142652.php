<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230526142652 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Increased length of cancellation_reason because of DataMigration Orders with long cancellation reasons.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order CHANGE cancellation_reason cancellation_reason LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order CHANGE cancellation_reason cancellation_reason VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
