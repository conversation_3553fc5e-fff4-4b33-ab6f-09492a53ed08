<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230824140352 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the consult system reference to the order.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD consult_system_reference CHAR(36) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F9E8D51BD0 ON sylius_order (consult_system_reference)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_6196A1F9E8D51BD0 ON sylius_order');
        $this->addSql('ALTER TABLE sylius_order DROP consult_system');
    }
}
