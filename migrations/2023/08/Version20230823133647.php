<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230823133647 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add order_sync table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE order_sync (third_party VARCHAR(255) NOT NULL, order_id INT NOT NULL, INDEX IDX_61DACF6D8D9F6D38 (order_id), PRIMARY KEY(order_id, third_party)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE order_sync ADD CONSTRAINT FK_61DACF6D8D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE order_sync');
    }
}
