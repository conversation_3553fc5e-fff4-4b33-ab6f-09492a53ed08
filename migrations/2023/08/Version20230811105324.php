<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230811105324 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds order history webhook url for the business units.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD canopy_deploy_order_history_webhook_url VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP canopy_deploy_order_history_webhook_url');
    }
}
