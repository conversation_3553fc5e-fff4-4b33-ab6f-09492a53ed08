<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230821122536 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removed Canopy webhook url';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP canopy_deploy_order_history_webhook_url');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD canopy_deploy_order_history_webhook_url VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
