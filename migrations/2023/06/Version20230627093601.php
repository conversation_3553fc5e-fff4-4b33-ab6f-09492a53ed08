<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230627093601 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added shop_user.username_canonical index';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX username_canonical_idx ON sylius_shop_user (username_canonical)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX username_canonical_idx ON sylius_shop_user');
    }
}
