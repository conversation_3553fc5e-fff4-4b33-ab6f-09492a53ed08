<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230626112701 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removes `previous_preferred_order_item` and `sylius_order_item_previous`.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE previous_preferred_order_item DROP FOREIGN KEY FK_34A867F7E415FB15');
        $this->addSql('DROP TABLE previous_preferred_order_item');
        $this->addSql('DROP TABLE sylius_order_item_previous');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE previous_preferred_order_item (order_item_id INT NOT NULL, preferred_variant_id INT NOT NULL, number_of_preferred_variants INT DEFAULT 1 NOT NULL, usage_advice VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_34A867F7F663B486 (preferred_variant_id), INDEX IDX_34A867F7E415FB15 (order_item_id), PRIMARY KEY(order_item_id, preferred_variant_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_order_item_previous (id INT AUTO_INCREMENT NOT NULL, order_id INT NOT NULL, variant_id INT DEFAULT NULL, quantity INT NOT NULL, unit_price INT NOT NULL, units_total INT NOT NULL, adjustments_total INT NOT NULL, total INT NOT NULL, is_immutable TINYINT(1) NOT NULL, product_name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, variant_name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, version INT DEFAULT 1 NOT NULL, unit_cost_price INT DEFAULT 0 NOT NULL, cost_price_total INT DEFAULT 0 NOT NULL, warnings LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci` COMMENT \'(DC2Type:json)\', usage_advice VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, original_unit_price INT DEFAULT NULL, INDEX IDX_DAE26FBD8D9F6D38 (order_id), INDEX IDX_DAE26FBD3B69A9AF (variant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD CONSTRAINT FK_34A867F7E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item_previous (id)');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD CONSTRAINT FK_34A867F7F663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD3B69A9AF FOREIGN KEY (variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD8D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id) ON DELETE CASCADE');
    }
}
