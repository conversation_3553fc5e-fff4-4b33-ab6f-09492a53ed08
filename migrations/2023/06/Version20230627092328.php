<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230627092328 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index on the order.state';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX state_idx ON sylius_order (state)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX state_idx ON sylius_order');
    }
}
