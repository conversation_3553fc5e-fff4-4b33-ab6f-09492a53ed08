<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230612141319 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removes the obsolete columns `order_item`.`original_order_item_id` and `preferred_order_item`.`original_preferred_order_item_id`';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE002201A9F6655');
        $this->addSql('DROP INDEX UNIQ_9FE002201A9F6655 ON preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item DROP original_preferred_order_item_id');
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED2B74224A');
        $this->addSql('DROP INDEX UNIQ_77B587ED2B74224A ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item DROP original_order_item_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE preferred_order_item ADD original_preferred_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE002201A9F6655 FOREIGN KEY (original_preferred_order_item_id) REFERENCES original_preferred_order_item (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_9FE002201A9F6655 ON preferred_order_item (original_preferred_order_item_id)');
        $this->addSql('ALTER TABLE sylius_order_item ADD original_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED2B74224A FOREIGN KEY (original_order_item_id) REFERENCES original_order_item (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_77B587ED2B74224A ON sylius_order_item (original_order_item_id)');
    }
}
