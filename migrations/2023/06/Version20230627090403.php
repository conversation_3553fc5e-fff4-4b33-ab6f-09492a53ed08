<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230627090403 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index to the createdAt for the audit_log_entries.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX created_at_idx ON audit_log_entries (created_at)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX created_at_idx ON audit_log_entries');
    }
}
