<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230608113124 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'When an original order item of a parent is removed, set the relation to null';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE original_order_item DROP FOREIGN KEY FK_3F5D689D727ACA70');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689D727ACA70 FOREIGN KEY (parent_id) REFERENCES original_order_item (id) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE original_order_item DROP FOREIGN KEY FK_3F5D689D727ACA70');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689D727ACA70 FOREIGN KEY (parent_id) REFERENCES original_order_item (id)');
    }
}
