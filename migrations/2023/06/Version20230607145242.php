<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230607145242 extends AbstractMigration
{
    private const string PRODUCT_ATTRIBUTE_CODE = 'addiction_sensitive';

    public function getDescription(): string
    {
        return sprintf('Removes the %s product attribute which was created by the shop configuration', self::PRODUCT_ATTRIBUTE_CODE);
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DELETE spat
            FROM sylius_product_attribute_translation spat
            INNER JOIN sylius_product_attribute spa ON (spat.translatable_id = spa.id)
            WHERE spa.code = :code
        ', [
            'code' => self::PRODUCT_ATTRIBUTE_CODE,
        ]);

        $this->addSql('
            DELETE FROM sylius_product_attribute WHERE code = :code
        ', [
            'code' => self::PRODUCT_ATTRIBUTE_CODE,
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->connection->executeQuery("
            INSERT INTO `sylius_product_attribute` (`code`, `type`, `storage_type`, `configuration`, `created_at`, `updated_at`, `position`, `translatable`)
            VALUES
                (:code, 'checkbox', 'boolean', 'a:0:{}', NOW(), NOW(), 1, 1);
        ", [
            'code' => self::PRODUCT_ATTRIBUTE_CODE,
        ]);

        $translatableId = $this->connection->lastInsertId();

        $this->addSql("
            INSERT INTO `sylius_product_attribute_translation` (`translatable_id`, `name`, `locale`)
            VALUES
                (:translatableId, 'Afhængighedsfølsomme', 'da'),
                (:translatableId, 'Suchtsensitiv', 'de'),
                (:translatableId, 'Addiction Sensitive', 'en'),
                (:translatableId, 'Sensible a la adicción', 'es'),
                (:translatableId, 'Sensible à la dépendance', 'fr'),
                (:translatableId, 'Verslavingsgevoelig', 'nl'),
                (:translatableId, 'Wrażliwe na uzależnienia', 'pl'),
                (:translatableId, 'Sensível ao vício', 'pt'),
                (:translatableId, 'Beroendekänslig', 'sv'),
                (:translatableId, 'Riippuvuus herkkä', 'fi'),
                (:translatableId, 'Dependență sensibil', 'ro');
        ", [
            'translatableId' => $translatableId,
        ]);
    }
}
