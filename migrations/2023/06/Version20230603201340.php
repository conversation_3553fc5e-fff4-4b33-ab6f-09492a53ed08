<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230603201340 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds original_order_item, original_preferred_order_item. Adds primary key to children of AbstractPreferredOrderItem and renames columns.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE original_order_item (id INT AUTO_INCREMENT NOT NULL, order_id INT NOT NULL, variant_id INT NOT NULL, parent_id INT DEFAULT NULL, order_item_id INT DEFAULT NULL, parent_order_item_id INT DEFAULT NULL, product_name VARCHAR(255) NOT NULL, variant_name VARCHAR(255) NOT NULL, quantity INT NOT NULL, unit_price INT NOT NULL, units_total INT NOT NULL, adjustments_total INT NOT NULL, total INT NOT NULL, INDEX IDX_3F5D689D8D9F6D38 (order_id), INDEX IDX_3F5D689D3B69A9AF (variant_id), INDEX IDX_3F5D689D727ACA70 (parent_id), UNIQUE INDEX UNIQ_3F5D689DE415FB15 (order_item_id), INDEX IDX_3F5D689D38939887 (parent_order_item_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE original_preferred_order_item (id INT AUTO_INCREMENT NOT NULL, order_item_id INT DEFAULT NULL, original_order_item_id INT DEFAULT NULL, preferred_variant_id INT DEFAULT NULL, quantity INT DEFAULT 1 NOT NULL, usage_advice VARCHAR(255) DEFAULT NULL, INDEX IDX_F5307634E415FB15 (order_item_id), INDEX IDX_F53076342B74224A (original_order_item_id), INDEX IDX_F5307634F663B486 (preferred_variant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689D8D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id)');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689D3B69A9AF FOREIGN KEY (variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689D727ACA70 FOREIGN KEY (parent_id) REFERENCES original_order_item (id)');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689DE415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE original_order_item ADD CONSTRAINT FK_3F5D689D38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F5307634E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F53076342B74224A FOREIGN KEY (original_order_item_id) REFERENCES original_order_item (id)');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F5307634F663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220B54B11B7');
        $this->addSql('ALTER TABLE preferred_order_item ADD id INT AUTO_INCREMENT NOT NULL, CHANGE preferred_for_order_item_id preferred_for_order_item_id INT DEFAULT NULL, CHANGE preferred_variant_id preferred_variant_id INT DEFAULT NULL, CHANGE number_of_preferred_variants quantity INT DEFAULT 1 NOT NULL, DROP PRIMARY KEY, ADD PRIMARY KEY (id)');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220B54B11B7 FOREIGN KEY (preferred_for_order_item_id) REFERENCES sylius_order_item (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE previous_preferred_order_item DROP FOREIGN KEY FK_34A867F780AFC901');
        $this->addSql('DROP INDEX IDX_34A867F780AFC901 ON previous_preferred_order_item');
        $this->addSql('ALTER TABLE previous_preferred_order_item DROP PRIMARY KEY');
        $this->addSql('ALTER TABLE previous_preferred_order_item CHANGE preferred_for_previous_order_item_id order_item_id INT NOT NULL');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD CONSTRAINT FK_34A867F7E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item_previous (id)');
        $this->addSql('CREATE INDEX IDX_34A867F7E415FB15 ON previous_preferred_order_item (order_item_id)');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD PRIMARY KEY (order_item_id, preferred_variant_id)');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP FOREIGN KEY FK_F5307634F663B486');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP FOREIGN KEY FK_F5307634E415FB15');
        $this->addSql('DROP INDEX IDX_F5307634F663B486 ON original_preferred_order_item');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD variant_id INT DEFAULT NULL, CHANGE preferred_variant_id preferred_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F5307634CC3003B5 FOREIGN KEY (preferred_order_item_id) REFERENCES preferred_order_item (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F53076343B69A9AF FOREIGN KEY (variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F5307634E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_F5307634CC3003B5 ON original_preferred_order_item (preferred_order_item_id)');
        $this->addSql('CREATE INDEX IDX_F53076343B69A9AF ON original_preferred_order_item (variant_id)');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220F663B486');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220B54B11B7');
        $this->addSql('DROP INDEX IDX_9FE00220F663B486 ON preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item CHANGE preferred_variant_id variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE002203B69A9AF FOREIGN KEY (variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220B54B11B7 FOREIGN KEY (preferred_for_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_9FE002203B69A9AF ON preferred_order_item (variant_id)');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220B54B11B7');
        $this->addSql('DROP INDEX IDX_9FE00220B54B11B7 ON preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item CHANGE preferred_for_order_item_id order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_9FE00220E415FB15 ON preferred_order_item (order_item_id)');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP INDEX IDX_F5307634CC3003B5, ADD UNIQUE INDEX UNIQ_F5307634CC3003B5 (preferred_order_item_id)');
        $this->addSql('ALTER TABLE preferred_order_item ADD original_preferred_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE002201A9F6655 FOREIGN KEY (original_preferred_order_item_id) REFERENCES original_preferred_order_item (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_9FE002201A9F6655 ON preferred_order_item (original_preferred_order_item_id)');
        $this->addSql('ALTER TABLE sylius_order_item ADD original_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED2B74224A FOREIGN KEY (original_order_item_id) REFERENCES original_order_item (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_77B587ED2B74224A ON sylius_order_item (original_order_item_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE original_preferred_order_item DROP INDEX UNIQ_F5307634CC3003B5, ADD INDEX IDX_F5307634CC3003B5 (preferred_order_item_id)');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE002201A9F6655');
        $this->addSql('DROP INDEX UNIQ_9FE002201A9F6655 ON preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item DROP original_preferred_order_item_id');
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED2B74224A');
        $this->addSql('DROP INDEX UNIQ_77B587ED2B74224A ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item DROP original_order_item_id');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220E415FB15');
        $this->addSql('DROP INDEX IDX_9FE00220E415FB15 ON preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item CHANGE order_item_id preferred_for_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220B54B11B7 FOREIGN KEY (preferred_for_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_9FE00220B54B11B7 ON preferred_order_item (preferred_for_order_item_id)');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP FOREIGN KEY FK_F5307634CC3003B5');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP FOREIGN KEY FK_F53076343B69A9AF');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP FOREIGN KEY FK_F5307634E415FB15');
        $this->addSql('DROP INDEX IDX_F5307634CC3003B5 ON original_preferred_order_item');
        $this->addSql('DROP INDEX IDX_F53076343B69A9AF ON original_preferred_order_item');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD preferred_variant_id INT DEFAULT NULL, DROP preferred_order_item_id, DROP variant_id');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F5307634F663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE original_preferred_order_item ADD CONSTRAINT FK_F5307634E415FB15 FOREIGN KEY (order_item_id) REFERENCES sylius_order_item (id) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_F5307634F663B486 ON original_preferred_order_item (preferred_variant_id)');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE002203B69A9AF');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220B54B11B7');
        $this->addSql('DROP INDEX IDX_9FE002203B69A9AF ON preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item CHANGE variant_id preferred_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220F663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220B54B11B7 FOREIGN KEY (preferred_for_order_item_id) REFERENCES sylius_order_item (id) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_9FE00220F663B486 ON preferred_order_item (preferred_variant_id)');
        $this->addSql('ALTER TABLE original_order_item DROP FOREIGN KEY FK_3F5D689D727ACA70');
        $this->addSql('ALTER TABLE original_preferred_order_item DROP FOREIGN KEY FK_F53076342B74224A');
        $this->addSql('DROP TABLE original_order_item');
        $this->addSql('DROP TABLE original_preferred_order_item');
        $this->addSql('ALTER TABLE preferred_order_item MODIFY id INT NOT NULL');
        $this->addSql('ALTER TABLE preferred_order_item DROP FOREIGN KEY FK_9FE00220B54B11B7');
        $this->addSql('ALTER TABLE preferred_order_item DROP PRIMARY KEY');
        $this->addSql('ALTER TABLE preferred_order_item DROP id, CHANGE preferred_for_order_item_id preferred_for_order_item_id INT NOT NULL, CHANGE preferred_variant_id preferred_variant_id INT NOT NULL, CHANGE quantity number_of_preferred_variants INT DEFAULT 1 NOT NULL');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220B54B11B7 FOREIGN KEY (preferred_for_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('ALTER TABLE preferred_order_item ADD PRIMARY KEY (preferred_for_order_item_id, preferred_variant_id)');
        $this->addSql('ALTER TABLE previous_preferred_order_item DROP FOREIGN KEY FK_34A867F7E415FB15');
        $this->addSql('DROP INDEX IDX_34A867F7E415FB15 ON previous_preferred_order_item');
        $this->addSql('ALTER TABLE previous_preferred_order_item DROP PRIMARY KEY');
        $this->addSql('ALTER TABLE previous_preferred_order_item CHANGE order_item_id preferred_for_previous_order_item_id INT NOT NULL');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD CONSTRAINT FK_34A867F780AFC901 FOREIGN KEY (preferred_for_previous_order_item_id) REFERENCES sylius_order_item_previous (id)');
        $this->addSql('CREATE INDEX IDX_34A867F780AFC901 ON previous_preferred_order_item (preferred_for_previous_order_item_id)');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD PRIMARY KEY (preferred_for_previous_order_item_id, preferred_variant_id)');
    }
}
