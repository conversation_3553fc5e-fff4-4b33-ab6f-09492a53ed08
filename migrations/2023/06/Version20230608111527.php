<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230608111527 extends AbstractMigration
{
    private const array SUPPLIER_IDENTIFIERS = [
        'roskilde-dom-apotek',
        'prescription-signing-and-shipping',
        'cloudrx',
        'blueclinic',
        'allegra-service-apotheek',
    ];

    public function getDescription(): string
    {
        return 'Remove old suppliers';
    }

    public function up(Schema $schema): void
    {
        foreach (self::SUPPLIER_IDENTIFIERS as $identifier) {
            $supplierId = $this->connection->executeQuery(
                'SELECT id FROM supplier WHERE identifier = :identifier LIMIT 1',
                ['identifier' => $identifier]
            )->fetchOne();

            if (empty($supplierId)) {
                continue;
            }

            $whereParams = ['supplierId' => $supplierId];

            // Remove or update the basics
            $this->addSql('DELETE FROM supplier_country_shipping WHERE supplier_id = :supplierId', $whereParams);
            $this->addSql('DELETE FROM supplier_doctor WHERE supplier_id = :supplierId', $whereParams);
            $this->addSql('UPDATE sylius_shipment SET supplier_id = null WHERE supplier_id = :supplierId', $whereParams);

            // Remove or update everything related to the product variants
            $this->addSql(
                'DELETE FROM preferred_order_item
                WHERE variant_id in (SELECT pv.id FROM sylius_product_variant AS pv WHERE pv.supplier_id = :supplierId);',
                $whereParams
            );

            $this->addSql(
                'DELETE FROM previous_preferred_order_item
                WHERE preferred_variant_id in (SELECT pv.id FROM sylius_product_variant AS pv WHERE pv.supplier_id = :supplierId);',
                $whereParams
            );

            $this->addSql(
                'UPDATE sylius_order_item AS oi
                INNER JOIN sylius_product_variant pv on oi.variant_id = pv.id
                SET oi.variant_id = null
                WHERE pv.supplier_id = :supplierId
            ',
                $whereParams
            );

            $this->addSql(
                'UPDATE sylius_order_item_previous AS oip
                INNER JOIN sylius_product_variant pv on oip.variant_id = pv.id
                SET oip.variant_id = null
                WHERE pv.supplier_id = :supplierId;',
                $whereParams
            );

            $this->addSql('DELETE FROM sylius_product_variant WHERE supplier_id = :supplierId', $whereParams);

            // Remove the term questions
            $this->addSql('DELETE FROM term_question_translation WHERE translatable_id IN (SELECT tq.id FROM term_question as tq WHERE tq.supplier_id = :supplierId)', $whereParams);
            $this->addSql('DELETE FROM term_question WHERE supplier_id = :supplierId', $whereParams);

            // Finally remove the supplier
            $this->addSql('DELETE FROM supplier WHERE id = :supplierId', $whereParams);
        }
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException('Cannot un-delete the old suppliers.');
    }
}
