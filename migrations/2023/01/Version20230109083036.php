<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230109083036 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add affiliate conversion id to order.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD affiliate_conversion_id VARCHAR(255) DEFAULT NULL AFTER affiliate_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP affiliate_conversion_id');
    }
}
