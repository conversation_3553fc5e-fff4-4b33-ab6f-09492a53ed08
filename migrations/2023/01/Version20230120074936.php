<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230120074936 extends AbstractMigration
{
    private const string UPDATE_ORDER_NUMBER_PREFIX = '
        UPDATE sylius_order as o
        SET o.number = CONCAT("%s", number)
        WHERE channel_id in (
            SELECT c.id
            FROM sylius_channel as c
            INNER JOIN sylius_business_unit sbu on c.business_unit_id = sbu.id
            WHERE sbu.code = "%s"
        )
        AND substr(o.number, 1, 2) = "00";
        ';

    public function getDescription(): string
    {
        return 'Add the order number prefix on existing orders.';
    }

    public function up(Schema $schema): void
    {
        // Set the blueclinic prefix for the bc business unit orders.
        $this->addSql(sprintf(self::UPDATE_ORDER_NUMBER_PREFIX, 'BC', 'blueclinic'));

        // Set the dokteronline prefix for the dk business unit orders.
        $this->addSql(sprintf(self::UPDATE_ORDER_NUMBER_PREFIX, 'DK', 'dokteronline'));
    }

    public function down(Schema $schema): void
    {
    }
}
