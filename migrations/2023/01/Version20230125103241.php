<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230125103241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add affiliate_conversion_status to sylius_order table to be able to track affiliate conversion status.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD affiliate_conversion_status VARCHAR(255) NOT NULL DEFAULT \'none\' COMMENT \'(DC2Type:affiliateConversionStatusEnumType)\' AFTER affiliate_conversion_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP affiliate_conversion_status');
    }
}
