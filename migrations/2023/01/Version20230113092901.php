<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230113092901 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Made marketing subscription more consistent with openapi spec.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription CHANGE opt_in_email_subscription_customer_survey opt_in_email_subscription_promotions TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE marketing_subscription DROP opt_in_sms');
        $this->addSql('ALTER TABLE marketing_subscription ADD opt_in_sms_subscription_service TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription CHANGE opt_in_email_subscription_promotions opt_in_email_subscription_customer_survey TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE marketing_subscription ADD opt_in_sms TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE marketing_subscription DROP opt_in_sms_subscription_service');
    }
}
