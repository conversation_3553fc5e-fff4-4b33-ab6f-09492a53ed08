<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230110190926 extends AbstractMigration
{
    private const string FETCH_BUSINESS_UNIT = 'SELECT sbu.id FROM sylius_business_unit as sbu WHERE sbu.code = "%s"';
    private const string INSERT_ORDER_SEQUENCE = '
        INSERT INTO sylius_order_sequence (idx, version, business_unit_id, prefix)
        VALUES (
            IFNULL((
                SELECT CAST(so.`number` as int)
                FROM sylius_order as so
                WHERE channel_id in (
                    SELECT sc.id
                    FROM sylius_channel as sc
                    WHERE sc.business_unit_id = "%1$s"
                )
                AND so.number IS NOT NULL
                ORDER BY so.number DESC
                LIMIT 1
            ), 0),
            1,
            "%1$s",
            "%2$s"
       );';

    public function getDescription(): string
    {
        return 'Update the order sequence table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_sequence ADD business_unit_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_sequence ADD CONSTRAINT FK_6F449F61A58ECB40 FOREIGN KEY (business_unit_id) REFERENCES sylius_business_unit (id)');
        $this->addSql('CREATE UNIQUE INDEX unique_business_unit ON sylius_order_sequence (business_unit_id)');

        $this->addSql('ALTER TABLE sylius_order_sequence ADD prefix VARCHAR(10) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6F449F6193B1868E ON sylius_order_sequence (prefix)');

        $this->createOrderSequences();

        $this->addSql('alter table sylius_order_sequence modify prefix varchar(10) not null;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_sequence DROP FOREIGN KEY FK_6F449F61A58ECB40');
        $this->addSql('DROP INDEX unique_business_unit ON sylius_order_sequence');
        $this->addSql('ALTER TABLE sylius_order_sequence DROP business_unit_id');

        $this->addSql('DROP INDEX UNIQ_6F449F6193B1868E ON sylius_order_sequence');
        $this->addSql('ALTER TABLE sylius_order_sequence DROP prefix');
    }

    private function createOrderSequences(): void
    {
        $this->addSql('DELETE FROM sylius_order_sequence WHERE business_unit_id is null;');

        // Only create new order sequences when the business unit already exists.
        $businessUnit = $this->connection->fetchOne(sprintf(self::FETCH_BUSINESS_UNIT, 'DK'));
        if ($businessUnit) {
            $this->addSql(sprintf(self::INSERT_ORDER_SEQUENCE, $businessUnit, 'DK'));
        }

        $bCBusinessUnit = $this->connection->fetchOne(sprintf(self::FETCH_BUSINESS_UNIT, 'BC'));
        if ($bCBusinessUnit) {
            $this->addSql(sprintf(self::INSERT_ORDER_SEQUENCE, $bCBusinessUnit, 'BC'));
        }
    }
}
