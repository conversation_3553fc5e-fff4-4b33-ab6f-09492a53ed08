<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230208144234 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Disable old creditcard payment method used for blueclinic.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE sylius_payment_method SET is_enabled = 0 WHERE code = \'credit_card\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE sylius_payment_method SET is_enabled = 1 WHERE code = \'credit_card\'');
    }
}
