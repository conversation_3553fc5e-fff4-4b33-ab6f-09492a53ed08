<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230209094234 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added column index on frequently used columns.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX name_idx ON sylius_product_translation (name)');
        $this->addSql('CREATE INDEX code_idx ON sylius_channel (code)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX name_idx ON sylius_product_translation');
        $this->addSql('DROP INDEX code_idx ON sylius_channel');
    }
}
