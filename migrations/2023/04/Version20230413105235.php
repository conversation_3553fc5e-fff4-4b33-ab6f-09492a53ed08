<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\Product\ProductVariant;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230413105235 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Adds %s::$preferredSupplier.', ProductVariant::class);
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant ADD preferred_supplier INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant DROP preferred_supplier');
    }
}
