<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230419153835 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds amountOrdersToday & amountOrdersTodayUpdatedAt to Supplier.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier ADD amount_orders_today INT DEFAULT 0 NOT NULL, ADD amount_orders_today_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier DROP amount_orders_today, DROP amount_orders_today_updated_at');
    }
}
