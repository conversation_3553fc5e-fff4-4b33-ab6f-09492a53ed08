<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230405155308 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds a cancelledBy to Order so we know who cancelled the Order & changed type of reason from RefundPayment.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD cancelled_by VARCHAR(255) DEFAULT NULL COMMENT \'(DC2Type:cancelledByEnumType)\'');
        $this->addSql('ALTER TABLE sylius_refund_refund_payment CHANGE reason reason VARCHAR(255) DEFAULT NULL COMMENT \'(DC2Type:refundPaymentReasonEnumType)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP cancelled_by');
        $this->addSql('ALTER TABLE sylius_refund_refund_payment CHANGE reason reason LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
