<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230404123232 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds an exchangeRate to a Payment.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment ADD exchange_rate NUMERIC(10, 5) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment DROP exchange_rate');
    }
}
