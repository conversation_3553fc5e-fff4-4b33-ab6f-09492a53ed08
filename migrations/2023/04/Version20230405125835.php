<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230405125835 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds PaymentServiceProviderName to a Payment.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment ADD payment_service_provider_name VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment DROP payment_service_provider_name');
    }
}
