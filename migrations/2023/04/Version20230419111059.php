<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230419111059 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create indexes on order table for better list view performance.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX checkout_completed_at_idx ON sylius_order (checkout_completed_at)');
        $this->addSql('CREATE INDEX doctor_name_idx ON sylius_order (doctor_name)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX doctor_name_idx ON sylius_order');
        $this->addSql('DROP INDEX checkout_completed_at_idx ON sylius_order');
    }
}
