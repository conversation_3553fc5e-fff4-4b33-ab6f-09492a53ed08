<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230424152813 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Alters App\Entity\Product\ProductVariant::$preferredSupplier to boolean instead of integer.');
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE preferred_supplier preferred_supplier TINYINT(1) DEFAULT \'0\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE preferred_supplier preferred_supplier INT DEFAULT 0 NOT NULL');
    }
}
