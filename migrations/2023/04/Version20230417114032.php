<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230417114032 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds supplier id to sylius_shipment table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment ADD supplier_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_shipment ADD CONSTRAINT FK_FD707B332ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
        $this->addSql('CREATE INDEX IDX_FD707B332ADD6D8C ON sylius_shipment (supplier_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment DROP FOREIGN KEY FK_FD707B332ADD6D8C');
        $this->addSql('DROP INDEX IDX_FD707B332ADD6D8C ON sylius_shipment');
        $this->addSql('ALTER TABLE sylius_shipment DROP supplier_id');
    }
}
