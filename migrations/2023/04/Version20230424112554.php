<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230424112554 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename the column in marketing_subscription opt_in_email_at to opt_in_email_subscribed_at';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription RENAME COLUMN opt_in_email_at TO opt_in_email_subscribed_at');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription RENAME COLUMN opt_in_email_subscribed_at TO opt_in_email_at');
    }
}
