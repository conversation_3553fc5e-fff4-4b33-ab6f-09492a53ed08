<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230414144834 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds a daily_order_limit to supplier.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier ADD daily_order_limit INT UNSIGNED DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier DROP daily_order_limit');
    }
}
