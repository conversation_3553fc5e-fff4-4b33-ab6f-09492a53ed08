<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230417120650 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename cancelled_by column to cancellation_by on sylius_order.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order CHANGE cancelled_by cancellation_by VARCHAR(255) DEFAULT NULL COMMENT \'(DC2Type:CancellationByEnumType)\' AFTER doctor_registration_number');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order CHANGE cancellation_by cancelled_by VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
