<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230912131307 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds defaultUsageAdvice to ProductVariantTranslation.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation ADD default_usage_advice VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation DROP default_usage_advice');
    }
}
