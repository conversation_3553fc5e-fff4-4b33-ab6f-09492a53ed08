<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230915093928 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds column to check if payment method is allowed to use for reshipment costs.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment_method ADD reshipment_costs_allowed TINYINT(1) DEFAULT \'1\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment_method DROP reshipment_costs_allowed');
    }
}
