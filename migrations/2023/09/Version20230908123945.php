<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230908123945 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds an immutable property to Shipment.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment ADD immutable TINYINT(1) DEFAULT \'0\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment DROP immutable');
    }
}
