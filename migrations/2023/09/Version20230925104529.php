<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230925104529 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removes unknown from cancellation by and reason for orders.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE sylius_order SET cancellation_by = NULL, cancellation_reason = NULL WHERE cancellation_by = 'unknown';");
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException('Time moves forward, not backwards. -BO');
    }
}
