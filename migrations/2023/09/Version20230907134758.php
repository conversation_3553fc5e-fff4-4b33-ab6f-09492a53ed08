<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230907134758 extends AbstractMigration
{
    public function getDescription(): string
    {
        return "Removes the unique index for the supplier shipment reference on the shipment because it's not unique anymore due to reshipments.";
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_FD707B33DF791B62 ON sylius_shipment');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX UNIQ_FD707B33DF791B62 ON sylius_shipment (supplier_shipment_reference)');
    }
}
