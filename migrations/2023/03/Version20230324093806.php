<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230324093806 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove parent_order_item_id from sylius_order_item_previous.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD38939887');
        $this->addSql('DROP INDEX IDX_DAE26FBD38939887 ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP parent_order_item_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD parent_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item_previous (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_DAE26FBD38939887 ON sylius_order_item_previous (parent_order_item_id)');
    }
}
