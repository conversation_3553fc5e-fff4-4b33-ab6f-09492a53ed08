<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230315093751 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove the customer locale, it is already stored in the MarketingSubscription.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_customer DROP locale_code');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_customer ADD locale_code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
