<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\Product\ProductVariantTranslation;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230327204320 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Adds leafletUrl to %s.', ProductVariantTranslation::class);
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation ADD leaflet_url VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation DROP leaflet_url');
    }
}
