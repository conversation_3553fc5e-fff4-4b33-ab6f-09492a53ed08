<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230320132641 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fix parent relation on "order_item_previous" and add cascade delete on parent column.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED38939887');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD38939887');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item_previous (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED38939887');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD38939887');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id)');
    }
}
