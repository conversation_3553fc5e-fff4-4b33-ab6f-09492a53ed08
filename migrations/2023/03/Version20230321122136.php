<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230321122136 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add supplier_doctor table for linking doctor registration numbers to suppliers.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE supplier_doctor (registration_number VARCHAR(20) NOT NULL, supplier_id INT NOT NULL, INDEX IDX_C4A221BE2ADD6D8C (supplier_id), UNIQUE INDEX registration_number_unique (registration_number), PRIMARY KEY(supplier_id, registration_number)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE supplier_doctor ADD CONSTRAINT FK_C4A221BE2ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE supplier_doctor');
    }
}
