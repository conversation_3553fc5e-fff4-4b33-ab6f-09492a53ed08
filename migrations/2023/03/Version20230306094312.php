<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230306094312 extends AbstractMigration
{
    /**
     * @see https://mv-jira-1.atlassian.net/browse/DV-5056.
     * TL;DR: Translations for prescription bin product are always provided by DOK through the catalog loader.
     */
    public function getDescription(): string
    {
        return '@see https://mv-jira-1.atlassian.net/browse/DV-5056';
    }

    public function up(Schema $schema): void
    {
    }

    public function down(Schema $schema): void
    {
    }
}
