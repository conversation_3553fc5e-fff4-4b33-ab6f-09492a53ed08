<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\Product\ProductVariantTranslationInterface;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230331070614 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Changes the property from leafletUrl to leaflet in %s.', ProductVariantTranslationInterface::class);
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation CHANGE leaflet_url leaflet VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation CHANGE leaflet leaflet_url VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
