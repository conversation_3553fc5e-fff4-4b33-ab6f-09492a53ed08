<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\TermQuestion\TermQuestion;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210921142518 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Adds the position column to %s.', TermQuestion::class);
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question ADD position INT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question DROP position');
    }
}
