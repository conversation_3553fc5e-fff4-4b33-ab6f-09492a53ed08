<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210528092653 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add initial medical properties to the product variant.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant ADD prescription_required TINYINT(1) DEFAULT \'0\' NOT NULL AFTER enabled, ADD maximum_quantity_per_order INT DEFAULT 1 NOT NULL AFTER prescription_required');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant DROP prescription_required, DROP maximum_quantity_per_order');
    }
}
