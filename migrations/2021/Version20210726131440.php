<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210726131440 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds cost price related columns to `sylius_adjustment`, `sylius_order` and `sylius_order_item` tables.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_adjustment ADD cost_price_amount INT NOT NULL DEFAULT 0 AFTER label');
        $this->addSql('ALTER TABLE sylius_order ADD items_cost_price_total INT NOT NULL DEFAULT 0 AFTER checkout_completed_at, ADD adjustments_cost_price_total INT NOT NULL DEFAULT 0 AFTER items_total, ADD cost_price_total INT NOT NULL DEFAULT 0 AFTER adjustments_total');
        $this->addSql('ALTER TABLE sylius_order_item ADD unit_cost_price INT NOT NULL DEFAULT 0 AFTER quantity, ADD cost_price_total INT NOT NULL DEFAULT 0 AFTER adjustments_total');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_adjustment DROP cost_price_amount');
        $this->addSql('ALTER TABLE sylius_order DROP items_cost_price_total, DROP adjustments_cost_price_total, DROP cost_price_total');
        $this->addSql('ALTER TABLE sylius_order_item DROP unit_cost_price, DROP cost_price_total');
    }
}
