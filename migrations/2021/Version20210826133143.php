<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210826133143 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add JSON column checkout_terms_answers to order table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD checkout_terms_answers JSON NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP checkout_terms_answers');
    }
}
