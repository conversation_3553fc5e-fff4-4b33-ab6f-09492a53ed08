<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210601124823 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add unique index to supplier.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier CHANGE identifier identifier VARCHAR(50) NOT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_9B2A6C7E772E836A ON supplier (identifier)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_9B2A6C7E772E836A ON supplier');
        $this->addSql('ALTER TABLE supplier CHANGE identifier identifier VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`');
    }
}
