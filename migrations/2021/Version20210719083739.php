<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210719083739 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adding handling fee for supplier';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier ADD handlingFee INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier DROP handlingFee');
    }
}
