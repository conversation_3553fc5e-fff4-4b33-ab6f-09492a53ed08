<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210719173030 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Introduce supplier shipping cost table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE supplier_shipping_cost (id INT AUTO_INCREMENT NOT NULL, supplier_id INT NOT NULL, country_id INT NOT NULL, shippingCost INT DEFAULT 0 NOT NULL, INDEX IDX_9B4782392ADD6D8C (supplier_id), INDEX IDX_9B478239F92F3E70 (country_id), UNIQUE INDEX UNIQ_9B4782392ADD6D8CF92F3E70 (supplier_id, country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE supplier_shipping_cost ADD CONSTRAINT FK_9B4782392ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
        $this->addSql('ALTER TABLE supplier_shipping_cost ADD CONSTRAINT FK_9B478239F92F3E70 FOREIGN KEY (country_id) REFERENCES sylius_country (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE supplier_shipping_cost');
    }
}
