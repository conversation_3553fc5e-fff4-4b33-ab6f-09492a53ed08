<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210825140827 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create term question and term question translation table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE term_question (id INT AUTO_INCREMENT NOT NULL, supplier_id INT DEFAULT NULL, channel_id INT DEFAULT NULL, code VARCHAR(255) NOT NULL, enabled TINYINT(1) DEFAULT \'1\' NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_B6F7494E2ADD6D8C (supplier_id), INDEX IDX_B6F7494E72F5A1AA (channel_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE term_question_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT NOT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(255) NOT NULL, INDEX IDX_576D9AE22C2AC5D3 (translatable_id), UNIQUE INDEX UNIQ_576D9AE22C2AC5D34180C698 (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE term_question ADD CONSTRAINT FK_B6F7494E2ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
        $this->addSql('ALTER TABLE term_question ADD CONSTRAINT FK_B6F7494E72F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id)');
        $this->addSql('ALTER TABLE term_question_translation ADD CONSTRAINT FK_576D9AE22C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES term_question (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question_translation DROP FOREIGN KEY FK_576D9AE22C2AC5D3');
        $this->addSql('DROP TABLE term_question');
        $this->addSql('DROP TABLE term_question_translation');
    }
}
