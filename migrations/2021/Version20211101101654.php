<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\Order\Order;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20211101101654 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Add prescription_state column to %s.', Order::class);
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD prescription_state VARCHAR(255) DEFAULT \'cart\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP prescription_state');
    }
}
