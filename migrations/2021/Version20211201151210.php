<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20211201151210 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add table for ProductTaxonChannel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE product_taxon_channel (id INT AUTO_INCREMENT NOT NULL, channel_id INT DEFAULT NULL, relevance INT DEFAULT 0 NOT NULL, label VARCHAR(255) DEFAULT NULL, productTaxon_id INT DEFAULT NULL, INDEX IDX_806A787282F46CEB (productTaxon_id), INDEX IDX_806A787272F5A1AA (channel_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE product_taxon_channel ADD CONSTRAINT FK_806A787282F46CEB FOREIGN KEY (productTaxon_id) REFERENCES sylius_product_taxon (id)');
        $this->addSql('ALTER TABLE product_taxon_channel ADD CONSTRAINT FK_806A787272F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE product_taxon_channel');
    }
}
