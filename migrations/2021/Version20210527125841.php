<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210527125841 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the supplier. Including references from the product variant to the supplier and optionally the country.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE supplier (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, identifier VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_product_variant ADD supplier_id INT NOT NULL AFTER shipping_category_id, ADD country_id INT DEFAULT NULL AFTER supplier_id, ADD supplier_variant_name VA<PERSON>HA<PERSON>(255) DEFAULT NULL, ADD supplier_variant_code VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_product_variant ADD CONSTRAINT FK_A29B5232ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
        $this->addSql('ALTER TABLE sylius_product_variant ADD CONSTRAINT FK_A29B523F92F3E70 FOREIGN KEY (country_id) REFERENCES sylius_country (id)');
        $this->addSql('CREATE INDEX IDX_A29B5232ADD6D8C ON sylius_product_variant (supplier_id)');
        $this->addSql('CREATE INDEX IDX_A29B523F92F3E70 ON sylius_product_variant (country_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant DROP FOREIGN KEY FK_A29B5232ADD6D8C');
        $this->addSql('DROP TABLE supplier');
        $this->addSql('ALTER TABLE sylius_product_variant DROP FOREIGN KEY FK_A29B523F92F3E70');
        $this->addSql('DROP INDEX IDX_A29B5232ADD6D8C ON sylius_product_variant');
        $this->addSql('DROP INDEX IDX_A29B523F92F3E70 ON sylius_product_variant');
        $this->addSql('ALTER TABLE sylius_product_variant DROP supplier_id, DROP country_id, DROP supplier_variant_name, DROP supplier_variant_code');
    }
}
