<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210623083437 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Make variant id nullable for a order item.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item CHANGE variant_id variant_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item CHANGE variant_id variant_id INT NOT NULL');
    }
}
