<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210831083630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fixup incorrect column definitions by migration diff.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question RENAME INDEX idx_b6f7494e2add6d8c TO IDX_39A137FA2ADD6D8C');
        $this->addSql('ALTER TABLE term_question RENAME INDEX idx_b6f7494e72f5a1aa TO IDX_39A137FA72F5A1AA');
        $this->addSql('ALTER TABLE term_question_translation RENAME INDEX idx_576d9ae22c2ac5d3 TO IDX_8137B81D2C2AC5D3');
        $this->addSql('ALTER TABLE term_question_translation RENAME INDEX uniq_576d9ae22c2ac5d34180c698 TO UNIQ_8137B81D2C2AC5D34180C698');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question RENAME INDEX idx_39a137fa2add6d8c TO IDX_B6F7494E2ADD6D8C');
        $this->addSql('ALTER TABLE term_question RENAME INDEX idx_39a137fa72f5a1aa TO IDX_B6F7494E72F5A1AA');
        $this->addSql('ALTER TABLE term_question_translation RENAME INDEX idx_8137b81d2c2ac5d3 TO IDX_576D9AE22C2AC5D3');
        $this->addSql('ALTER TABLE term_question_translation RENAME INDEX uniq_8137b81d2c2ac5d34180c698 TO UNIQ_576D9AE22C2AC5D34180C698');
    }
}
