<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210921115634 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Changed the name column for the term question translation to text so it can fill the whole question.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question_translation CHANGE name name LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question_translation CHANGE name name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');
    }
}
