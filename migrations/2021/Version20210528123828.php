<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210528123828 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add enabled to supplier.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier ADD enabled TINYINT(1) DEFAULT \'1\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier DROP enabled');
    }
}
