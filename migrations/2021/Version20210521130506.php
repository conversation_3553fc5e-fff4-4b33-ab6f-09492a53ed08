<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210521130506 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Improve the default value of the sylius_order_item.warnings column.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings JSON DEFAULT (JSON_ARRAY()) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // No need for a rollback.
    }
}
