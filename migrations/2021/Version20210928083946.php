<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210928083946 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Make position property for term_question nullable.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question CHANGE position position INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question CHANGE position position INT NOT NULL');
    }
}
