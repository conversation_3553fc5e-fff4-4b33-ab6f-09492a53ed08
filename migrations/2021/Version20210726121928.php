<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210726121928 extends AbstractMigration
{
    public function getDescription(): string
    {
        return "Fixes supplier and supplier_shipping_cost tables which didn't follow the underscore naming strategy convention in the database.";
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier CHANGE handlingFee handling_fee INT DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE supplier_shipping_cost CHANGE shippingCost shipping_cost INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier CHANGE handling_fee handlingFee INT DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE supplier_shipping_cost CHANGE shipping_cost shippingCost INT DEFAULT 0 NOT NULL');
    }
}
