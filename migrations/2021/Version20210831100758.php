<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210831100758 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update term question and term question translation tables.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question CHANGE code code VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE term_question_translation CHANGE name name VARCHAR(255) DEFAULT NULL, CHANGE locale locale VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE term_question CHANGE code code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`');
        $this->addSql('ALTER TABLE term_question_translation CHANGE name name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, CHANGE locale locale VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`');
    }
}
