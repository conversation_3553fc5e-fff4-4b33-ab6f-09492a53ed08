<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210521132528 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add caption to a product variant translation.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation ADD caption VARCHAR(255) DEFAULT NULL AFTER `name`');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant_translation DROP caption');
    }
}
