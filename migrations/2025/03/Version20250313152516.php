<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250313152516 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'After Sylius update to 1.13 schema does not validate without these changes.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries CHANGE context context JSON NOT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries CHANGE context context LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\'');
    }
}
