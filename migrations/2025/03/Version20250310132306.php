<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250310132306 extends AbstractMigration
{
    private const array ALLOWED_ENVS = [
        'dev',
        'test',
        'accept',
    ];

    public function getDescription(): string
    {
        return 'Update number prefix from DK to DO for the configuration tables. Update order number prefix in sylius_order table for non-production environments.';
    }

    public function up(Schema $schema): void
    {
        if (!$this->isAllowedEnvironment()) {
            $this->warnIf(!$this->isAllowedEnvironment(), 'Skipped: Only execute for Dokteronline/Doctoronline environments, environment: '.$this->getEnvironment());

            return;
        }

        $this->addSql("UPDATE sylius_order_sequence SET prefix = 'DO' WHERE prefix = 'DK'");
        $this->addSql("UPDATE sylius_business_unit SET order_number_prefix = 'DO' WHERE order_number_prefix = 'DK'");

        $this->updateOrderTableToDo();
    }

    public function down(Schema $schema): void
    {
        if (!$this->isAllowedEnvironment()) {
            $this->warnIf(!$this->isAllowedEnvironment(), 'Skipped: Only execute for Dokteronline/Doctoronline environments, environment: '.$this->getEnvironment());

            return;
        }

        $this->addSql("UPDATE sylius_order_sequence SET prefix = 'DK' WHERE prefix = 'DO'");
        $this->addSql("UPDATE sylius_business_unit SET order_number_prefix = 'DK' WHERE order_number_prefix = 'DO'");

        $this->updateOrderTableToDK();
    }

    /**
     * Prevent running this migration on production environment
     * The sylius_order table will be updated manually on production.
     */
    private function updateOrderTableToDo(): void
    {

        if (!in_array($this->getEnvironment(), self::ALLOWED_ENVS, true)) {
            $this->warnIf(true, 'Only update the order number prefixes on dev,test and accept Environment: '.$this->getEnvironment());

            return;
        }

        $this->addSql("
            UPDATE sylius_order as ordr
            SET number = REPLACE(ordr.number,  'DK', 'DO')
            WHERE number LIKE 'DK%'
        ");
    }

    /**
     * Prevent running this migration on production environment
     * The sylius_order table will be updated manually on production.
     */
    private function updateOrderTableToDK(): void
    {
        if (!in_array($this->getEnvironment(), self::ALLOWED_ENVS, true)) {
            $this->warnIf(true, 'Only update the order number prefixes on dev,test and accept Environment: '.$this->getEnvironment());

            return;
        }

        $this->addSql("
            UPDATE sylius_order as ordr
            SET number = REPLACE(ordr.number,  'DO', 'DK')
            WHERE number LIKE 'DO%'
        ");
    }

    private function isAllowedEnvironment(): bool
    {
        return in_array($this->getEnvironment(), [...self::ALLOWED_ENVS, 'prod'], true);
    }

    private function getEnvironment(): string
    {
        return getenv('APP_ENV') ?: $_SERVER['APP_ENV'] ?? 'unknown';
    }
}
