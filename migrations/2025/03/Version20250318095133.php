<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250318095133 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index to `created_at` on sylius_order table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX created_at_idx ON sylius_order (created_at)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX created_at_idx ON sylius_order');
    }
}
