<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250701061910 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds sylius_product_variant.repeat_interval_in_days';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE sylius_product_variant ADD repeat_interval_in_days INT DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE sylius_product_variant DROP repeat_interval_in_days
        SQL);
    }
}
