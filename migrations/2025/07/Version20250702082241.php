<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250702082241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds OptInShareProductData embeddable to the marketing_subscription table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE marketing_subscription
            ADD opt_in_share_product_data_subscribed TINYINT(1) DEFAULT 0 NOT NULL,
            ADD opt_in_share_product_data_subscribed_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
            ADD opt_in_share_product_data_unsubscribed_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)'
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE marketing_subscription
            DROP opt_in_share_product_data_subscribed,
            DROP opt_in_share_product_data_subscribed_at,
            DROP opt_in_share_product_data_unsubscribed_at
        SQL);
    }
}
