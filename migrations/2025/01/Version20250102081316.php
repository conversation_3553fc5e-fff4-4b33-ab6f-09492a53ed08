<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250102081316 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'transition_log_entry table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE transition_log_entry (id INT AUTO_INCREMENT NOT NULL, order_id INT NOT NULL, transitioned_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', object_table_name VARCHAR(255) NOT NULL, object_id INT NOT NULL, state_machine_graph VARCHAR(255) NOT NULL, state_from VARCHAR(255) NOT NULL, state_to VARCHAR(255) NOT NULL, transition VARCHAR(255) NOT NULL, INDEX IDX_FDB2158C8D9F6D38 (order_id), INDEX transitioned_at_idx (transitioned_at), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE transition_log_entry ADD CONSTRAINT FK_FDB2158C8D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE transition_log_entry');
    }
}
