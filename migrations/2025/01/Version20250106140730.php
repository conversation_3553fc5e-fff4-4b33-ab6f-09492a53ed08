<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250106140730 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Required company_name in sylius_business_unit';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit CHANGE company_name company_name VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit CHANGE company_name company_name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
    }
}
