<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250120160219 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove Sylius Plus tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel DROP return_requests_allowed');
        $this->addSql('ALTER TABLE sylius_customer DROP FOREIGN KEY FK_7E82D5E6CDF1749F');
        $this->addSql('ALTER TABLE sylius_customer DROP loyalty_points_account_id');
        $this->addSql('ALTER TABLE sylius_order DROP FOREIGN KEY FK_6196A1F989EA1297');
        $this->addSql('ALTER TABLE sylius_order DROP return_request_id');
        $this->addSql('ALTER TABLE sylius_shipment DROP FOREIGN KEY FK_FD707B331280F509');
        $this->addSql('ALTER TABLE sylius_shipment DROP inventory_source_id');

        $this->addSql('SET FOREIGN_KEY_CHECKS = 0;');

        $this->addSql('DROP TABLE sylius_plus_inventory_source');
        $this->addSql('DROP TABLE sylius_plus_inventory_source_address');
        $this->addSql('DROP TABLE sylius_plus_inventory_source_channels');
        $this->addSql('DROP TABLE sylius_plus_inventory_source_stock');
        $this->addSql('DROP TABLE sylius_plus_loyalty_points_account');
        $this->addSql('DROP TABLE sylius_plus_loyalty_points_transaction');
        $this->addSql('DROP TABLE sylius_plus_loyalty_points_transactions');
        $this->addSql('DROP TABLE sylius_plus_loyalty_purchase');
        $this->addSql('DROP TABLE sylius_plus_loyalty_rule');
        $this->addSql('DROP TABLE sylius_plus_loyalty_rule_action');
        $this->addSql('DROP TABLE sylius_plus_loyalty_rule_channels');
        $this->addSql('DROP TABLE sylius_plus_rbac_admin_user_roles');
        $this->addSql('DROP TABLE sylius_plus_rbac_role');
        $this->addSql('DROP TABLE sylius_plus_rbac_role_translation');
        $this->addSql('DROP TABLE sylius_plus_return_request');
        $this->addSql('DROP TABLE sylius_plus_return_request_credit_memos');
        $this->addSql('DROP TABLE sylius_plus_return_request_image');
        $this->addSql('DROP TABLE sylius_plus_return_request_items');
        $this->addSql('DROP TABLE sylius_plus_return_request_unit');

        $this->addSql('SET FOREIGN_KEY_CHECKS = 1;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE sylius_plus_inventory_source (id INT AUTO_INCREMENT NOT NULL, address_id INT DEFAULT NULL, code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, priority INT DEFAULT 0 NOT NULL, UNIQUE INDEX UNIQ_96C48A62F5B7AF75 (address_id), UNIQUE INDEX UNIQ_96C48A6277153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_inventory_source_address (id INT AUTO_INCREMENT NOT NULL, country_code VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, street VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, city VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, postcode VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_inventory_source_channels (inventory_source_id INT NOT NULL, channel_id INT NOT NULL, INDEX IDX_9ED7D9201280F509 (inventory_source_id), INDEX IDX_9ED7D92072F5A1AA (channel_id), PRIMARY KEY(inventory_source_id, channel_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_inventory_source_stock (id INT AUTO_INCREMENT NOT NULL, product_variant_id INT NOT NULL, inventory_source_id INT DEFAULT NULL, on_hand INT NOT NULL, on_hold INT NOT NULL, inventory_source_code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_7FD5018DA80EF684 (product_variant_id), INDEX IDX_7FD5018D1280F509 (inventory_source_id), UNIQUE INDEX UNIQ_7FD5018DA80EF6841280F509 (product_variant_id, inventory_source_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_points_account (id INT AUTO_INCREMENT NOT NULL, balance INT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_points_transaction (id INT AUTO_INCREMENT NOT NULL, details LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci` COMMENT \'(DC2Type:array)\', transaction_date DATETIME NOT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, points_value INT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_points_transactions (loyalty_points_account_id INT NOT NULL, transaction_id INT NOT NULL, INDEX IDX_BFBB5A68CDF1749F (loyalty_points_account_id), INDEX IDX_BFBB5A682FC0CB0F (transaction_id), PRIMARY KEY(loyalty_points_account_id, transaction_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_purchase (id INT AUTO_INCREMENT NOT NULL, promotion_id INT DEFAULT NULL, code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, loyalty_points INT NOT NULL, enabled TINYINT(1) NOT NULL, description VARCHAR(180) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_FA819B5F139DF194 (promotion_id), UNIQUE INDEX UNIQ_FA819B5F77153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_rule (id INT AUTO_INCREMENT NOT NULL, action_id INT DEFAULT NULL, name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, enabled TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_54C32E8D9D32F035 (action_id), UNIQUE INDEX UNIQ_54C32E8D77153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_rule_action (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, configuration LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci` COMMENT \'(DC2Type:object)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_loyalty_rule_channels (loyalty_rule_id INT NOT NULL, channel_id INT NOT NULL, INDEX IDX_CCD8FF5D188FEBE2 (loyalty_rule_id), INDEX IDX_CCD8FF5D72F5A1AA (channel_id), PRIMARY KEY(loyalty_rule_id, channel_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_rbac_admin_user_roles (admin_id INT NOT NULL, role_id INT NOT NULL, INDEX IDX_DDFE6842642B8210 (admin_id), INDEX IDX_DDFE6842D60322AC (role_id), PRIMARY KEY(admin_id, role_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_rbac_role (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, permissions LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci` COMMENT \'(DC2Type:array)\', UNIQUE INDEX UNIQ_C3C5675377153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_rbac_role_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT NOT NULL, name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, locale VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_C70206BE2C2AC5D3 (translatable_id), UNIQUE INDEX sylius_plus_rbac_role_translation_uniq_trans (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_return_request (id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, order_id INT DEFAULT NULL, reason VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, submitted_at DATETIME NOT NULL, state VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, response LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, currency_code VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, resolution VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, number VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_164D478D9F6D38 (order_id), UNIQUE INDEX number_order_id_idx (number, order_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_return_request_credit_memos (return_request_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, credit_memo_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_EA5B8F2F89EA1297 (return_request_id), INDEX IDX_EA5B8F2F8E574316 (credit_memo_id), PRIMARY KEY(return_request_id, credit_memo_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_return_request_image (id INT AUTO_INCREMENT NOT NULL, owner_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, type VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`, path VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_884946AC7E3C61F9 (owner_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_return_request_items (return_request_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, return_request_unit_id INT NOT NULL, INDEX IDX_AC6AABBE89EA1297 (return_request_id), INDEX IDX_AC6AABBEF9BA0E74 (return_request_unit_id), PRIMARY KEY(return_request_id, return_request_unit_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE sylius_plus_return_request_unit (id INT AUTO_INCREMENT NOT NULL, order_item_unit_id INT NOT NULL, product_unit_name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, product_unit_price INT NOT NULL, state VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_520_ci`, INDEX IDX_1A98A733F720C233 (order_item_unit_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE sylius_plus_inventory_source ADD CONSTRAINT FK_96C48A62F5B7AF75 FOREIGN KEY (address_id) REFERENCES sylius_plus_inventory_source_address (id)');
        $this->addSql('ALTER TABLE sylius_plus_inventory_source_channels ADD CONSTRAINT FK_9ED7D9201280F509 FOREIGN KEY (inventory_source_id) REFERENCES sylius_plus_inventory_source (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_inventory_source_channels ADD CONSTRAINT FK_9ED7D92072F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_inventory_source_stock ADD CONSTRAINT FK_7FD5018D1280F509 FOREIGN KEY (inventory_source_id) REFERENCES sylius_plus_inventory_source (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_inventory_source_stock ADD CONSTRAINT FK_7FD5018DA80EF684 FOREIGN KEY (product_variant_id) REFERENCES sylius_product_variant (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_points_transactions ADD CONSTRAINT FK_BFBB5A682FC0CB0F FOREIGN KEY (transaction_id) REFERENCES sylius_plus_loyalty_points_transaction (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_points_transactions ADD CONSTRAINT FK_BFBB5A68CDF1749F FOREIGN KEY (loyalty_points_account_id) REFERENCES sylius_plus_loyalty_points_account (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_purchase ADD CONSTRAINT FK_FA819B5F139DF194 FOREIGN KEY (promotion_id) REFERENCES sylius_promotion (id)');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule ADD CONSTRAINT FK_54C32E8D9D32F035 FOREIGN KEY (action_id) REFERENCES sylius_plus_loyalty_rule_action (id)');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule_channels ADD CONSTRAINT FK_6DC3AD55188FEBE2 FOREIGN KEY (loyalty_rule_id) REFERENCES sylius_plus_loyalty_rule (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_loyalty_rule_channels ADD CONSTRAINT FK_6DC3AD5572F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_rbac_admin_user_roles ADD CONSTRAINT FK_DDFE6842642B8210 FOREIGN KEY (admin_id) REFERENCES sylius_admin_user (id)');
        $this->addSql('ALTER TABLE sylius_plus_rbac_admin_user_roles ADD CONSTRAINT FK_DDFE6842D60322AC FOREIGN KEY (role_id) REFERENCES sylius_plus_rbac_role (id)');
        $this->addSql('ALTER TABLE sylius_plus_rbac_role_translation ADD CONSTRAINT FK_C70206BE2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES sylius_plus_rbac_role (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request ADD CONSTRAINT FK_164D478D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request_credit_memos ADD CONSTRAINT FK_EA5B8F2F89EA1297 FOREIGN KEY (return_request_id) REFERENCES sylius_plus_return_request (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request_credit_memos ADD CONSTRAINT FK_EA5B8F2F8E574316 FOREIGN KEY (credit_memo_id) REFERENCES sylius_refund_credit_memo (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request_image ADD CONSTRAINT FK_884946AC7E3C61F9 FOREIGN KEY (owner_id) REFERENCES sylius_plus_return_request (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request_items ADD CONSTRAINT FK_AC6AABBE89EA1297 FOREIGN KEY (return_request_id) REFERENCES sylius_plus_return_request (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request_items ADD CONSTRAINT FK_AC6AABBEF9BA0E74 FOREIGN KEY (return_request_unit_id) REFERENCES sylius_plus_return_request_unit (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_plus_return_request_unit ADD CONSTRAINT FK_1A98A733F720C233 FOREIGN KEY (order_item_unit_id) REFERENCES sylius_order_item_unit (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_channel ADD return_requests_allowed TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE sylius_customer ADD loyalty_points_account_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_customer ADD CONSTRAINT FK_7E82D5E6CDF1749F FOREIGN KEY (loyalty_points_account_id) REFERENCES sylius_plus_loyalty_points_account (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_7E82D5E6CDF1749F ON sylius_customer (loyalty_points_account_id)');
        $this->addSql('ALTER TABLE sylius_order ADD return_request_id VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci`');
        $this->addSql('ALTER TABLE sylius_order ADD CONSTRAINT FK_6196A1F989EA1297 FOREIGN KEY (return_request_id) REFERENCES sylius_plus_return_request (id) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_6196A1F989EA1297 ON sylius_order (return_request_id)');
        $this->addSql('ALTER TABLE sylius_shipment ADD inventory_source_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_shipment ADD CONSTRAINT FK_FD707B331280F509 FOREIGN KEY (inventory_source_id) REFERENCES sylius_plus_inventory_source (id)');
        $this->addSql('CREATE INDEX IDX_FD707B331280F509 ON sylius_shipment (inventory_source_id)');
    }
}
