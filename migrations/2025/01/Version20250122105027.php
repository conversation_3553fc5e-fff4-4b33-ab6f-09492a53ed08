<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250122105027 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index on doctor_uuid and doctor_registration_number columns of sylius_order';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX doctor_registration_number_idx ON sylius_order (doctor_registration_number)');
        $this->addSql('CREATE INDEX doctor_uuid_idx ON sylius_order (doctor_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX doctor_registration_number_idx ON sylius_order');
        $this->addSql('DROP INDEX doctor_uuid_idx ON sylius_order');
    }
}
