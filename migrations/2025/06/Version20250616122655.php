<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250616122655 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Copy doctor information (name, registration number, UUID, and consult system reference) from their base orders to their corresponding follow-up orders';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            UPDATE sylius_order AS bc_order
                INNER JOIN sylius_order AS dok_order ON dok_order.follow_up_order_id = bc_order.id
            SET bc_order.doctor_name                = dok_order.doctor_name,
                bc_order.doctor_registration_number = dok_order.doctor_registration_number,
                bc_order.doctor_uuid                = dok_order.doctor_uuid,
                bc_order.consult_system_reference   = dok_order.consult_system_reference
            WHERE bc_order.id = dok_order.follow_up_order_id
        SQL);

    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            UPDATE sylius_order AS bc_order
                INNER JOIN sylius_order AS dok_order ON dok_order.follow_up_order_id = bc_order.id
            SET bc_order.doctor_name                = null,
                bc_order.doctor_registration_number = null,
                bc_order.doctor_uuid                = null,
                bc_order.consult_system_reference   = null
            WHERE bc_order.id = dok_order.follow_up_order_id AND dok_order.follow_up_order_id IS NOT NULL
        SQL);
    }
}
