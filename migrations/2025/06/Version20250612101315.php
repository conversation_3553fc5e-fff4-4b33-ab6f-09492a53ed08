<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250612101315 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Modify the unique index on sylius_order table to include follow_up_order_id, so we can set the consult_system_reference to null for follow-up orders without breaking the uniqueness constraint.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_6196A1F9E8D51BD0 ON sylius_order
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX consult_system_reference_idx ON sylius_order (consult_system_reference, follow_up_order_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX consult_system_reference_idx ON sylius_order
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_6196A1F9E8D51BD0 ON sylius_order (consult_system_reference)
        SQL);
    }
}
