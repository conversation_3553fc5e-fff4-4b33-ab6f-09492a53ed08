<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250401144552 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Convert serialized object attributes to codes in sylius_promotion_rule.configuration table';
    }

    public function up(Schema $schema): void
    {
        foreach ($this->tablesAndColumnsToBeUpdated() as [$table, $column]) {
            $this->convert($table, $column);
        }
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException('This migration is irreversible.');
    }

    /**
     * @return iterable<array{string, string}>
     */
    private function tablesAndColumnsToBeUpdated(): iterable
    {
        yield ['sylius_promotion_action', 'configuration'];
        yield ['sylius_promotion_rule', 'configuration'];
    }

    private function convert(string $table, string $column): void
    {
        $rows = $this->connection->fetchAllAssociative(
            sprintf('SELECT %s, %s, %s FROM %s', 'id', 'type', $column, $table)
        );

        foreach ($rows as $row) {
            switch ($row['type']) {
                case 'product_contains_attribute':
                    $this->convertAttributeObjectsToCode($table, $column, $row);
                    break;
                case 'n_items_of_type_x':
                case 'order_fixed_discount':
                case 'order_fixed_discount_for_each_item_after_nth_item_of_type':
                case 'order_percentage_discount_with_excluded_products':
                case 'order_fixed_discount_with_excluded_products':
                    $this->convertEligibleTypeToCode($table, $column, $row);
                    break;
                case 'contains_product':
                    $this->convertAttributeObjectsToCode($table, $column, $row);
                    $this->convertEligibleTypeToCode($table, $column, $row);
                    break;
                default:
            }
        }
    }

    private function convertAttributeObjectsToCode(string $table, string $column, array $row): void
    {
        $configuration = $row[$column] ?? null;

        /**
         * Skip if we cannot unserialize the data because this would mean the @see \Sylius\Bundle\CoreBundle\Migrations\Version20240315112656 was run before this migration.
         */
        $this->skipIf(@unserialize($configuration) === false, 'Data is not serialized');
        $configuration = unserialize($configuration);
        $serializedAttributes = $configuration['attributes'] ?? [];
        $codes = [];

        foreach ($serializedAttributes as $attribute) {
            $attributeValue = (array) $attribute;
            $code = $attributeValue["\0*\0code"] ?? null;

            if ($code === null) {
                continue;
            }

            $codes[] = $code;
        }

        $attributes['attributes'] = $codes;
        $encodedData = serialize($attributes);

        $this->updateRecord($table, $column, $encodedData, $row['id']);
    }

    private function convertEligibleTypeToCode(string $table, string $column, array $row): void
    {
        $configuration = $row[$column] ?? null;

        /**
         * Skip if we cannot unserialize the data because this would mean the @see \Sylius\Bundle\CoreBundle\Migrations\Version20240315112656 was run before this migration.
         */
        $this->skipIf(@unserialize($configuration) === false, 'Data is not serialized');
        $configuration = unserialize($configuration);
        $eligibleType = $configuration['eligibleType'] ?? $configuration['filters']['product_type_filter'] ?? null;

        if ($eligibleType === null) {
            return;
        }

        $eligibleType = (array) $eligibleType;
        if (!isset($eligibleType['name'])) {
            return;
        }

        $code = match ($eligibleType['name']) {
            'ANY' => 'any',
            'RX' => 'rx',
            'OTC' => 'otc',
            default => null,
        };

        if ($code === null) {
            return;
        }

        if (isset($configuration['eligibleType'])) {
            $configuration['eligibleType'] = $code;
        }
        if (isset($configuration['filters']['product_type_filter'])) {
            $configuration['filters']['product_type_filter'] = $code;
        }

        $encodedData = serialize($configuration);

        $this->updateRecord($table, $column, $encodedData, $row['id']);
    }

    private function updateRecord(string $table, string $column, string $encodedData, int $id): void
    {
        $this->connection->update(
            $table,
            [$column => $encodedData],
            ['id' => $id]
        );
    }
}
