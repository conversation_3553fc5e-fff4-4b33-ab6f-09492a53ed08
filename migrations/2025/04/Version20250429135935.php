<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250429135935 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add marketing_subscription_uuid to audit_log_entries table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries ADD marketing_subscription_uuid CHAR(36) NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE audit_log_entries ADD CONSTRAINT FK_384ABAC75C337ABB FOREIGN KEY (marketing_subscription_uuid) REFERENCES marketing_subscription (uuid) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_384ABAC75C337ABB ON audit_log_entries (marketing_subscription_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE audit_log_entries DROP FOREIGN KEY FK_384ABAC75C337ABB');
        $this->addSql('DROP INDEX IDX_384ABAC75C337ABB ON audit_log_entries');
        $this->addSql('ALTER TABLE audit_log_entries DROP marketing_subscription_uuid');
    }
}
