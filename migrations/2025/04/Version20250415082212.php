<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250415082212 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Replace DC2TYPE:array with JSON part 2';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DELETE FROM sylius_address_log_entries WHERE 1=1');
        $this->addSql('UPDATE sylius_shop_user SET roles = \'["ROLE_USER"]\' WHERE 1=1');

        foreach ($this->tablesAndColumnsToBeUpdated() as [$table, $column]) {
            $this->changeColumnTypeToJson($table, $column);
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM sylius_address_log_entries WHERE 1=1');
        $this->addSql('UPDATE sylius_shop_user SET roles = \'a:1:{i:0;s:9:"ROLE_USER";}\' WHERE 1=1');

        foreach ($this->tablesAndColumnsToBeUpdated() as [$table, $column]) {
            $this->changeColumnTypeToLongText($table, $column);
        }
    }

    private function changeColumnTypeToJson(string $table, string $column): void
    {
        if ($table === 'sylius_address_log_entries') {
            $this->addSql(sprintf('ALTER TABLE %s CHANGE %s %s JSON DEFAULT NULL', $table, $column, $column));
        } else {
            $this->addSql(sprintf('ALTER TABLE %s CHANGE %s %s JSON NOT NULL', $table, $column, $column));
        }
    }

    private function changeColumnTypeToLongText(string $table, string $column): void
    {
        $this->connection->executeQuery(sprintf(
            'ALTER TABLE %s CHANGE %s %s LONGTEXT NOT NULL COMMENT \'(DC2Type:array)\'',
            $table,
            $column,
            $column,
        ));
    }

    /**
     * @return iterable<array{string, string}>
     */
    private function tablesAndColumnsToBeUpdated(): iterable
    {
        yield ['sylius_address_log_entries', 'data'];
        yield ['sylius_shop_user', 'roles'];
    }
}
