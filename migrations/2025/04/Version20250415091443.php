<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250415091443 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Insert migration (Sylius\\Bundle\\CoreBundle\\Migrations\\Version20240315112656) version so we can skip it.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('REPLACE INTO sylius_migrations (version, executed_at, execution_time) VALUES ("Sylius\\\\Bundle\\\\CoreBundle\\\\Migrations\\\\Version20240315112656", NOW(), 1)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM sylius_migrations WHERE version = "Sylius\\\\Bundle\\\\CoreBundle\\\\Migrations\\\\Version20240315112656"');
    }
}
