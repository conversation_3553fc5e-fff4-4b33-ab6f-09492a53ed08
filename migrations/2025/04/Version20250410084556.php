<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use PDO;

final class Version20250410084556 extends AbstractMigration
{
    private const int BATCH_SIZE = 5000;

    public function getDescription(): string
    {
        return 'Replace DC2TYPE:array with JSON part 1';
    }

    public function up(Schema $schema): void
    {
        foreach ($this->tablesAndColumnsToBeUpdated() as [$table, $column]) {
            $this->changeTypesFromLongtextToJsonAndEncodeSerializedData($table, $column);
        }
    }

    public function down(Schema $schema): void
    {
        foreach ($this->tablesAndColumnsToBeUpdated() as [$table, $column]) {
            $this->changeTypesFromJsonToLongtext($table, $column);
        }
    }

    public function postDown(Schema $schema): void
    {
        foreach ($this->tablesAndColumnsToBeUpdated() as [$table, $column]) {
            $this->changeColumnTypeToLongText($table, $column);
        }
    }

    private function changeTypesFromLongtextToJsonAndEncodeSerializedData(string $table, string $dataColumn): void
    {
        $this->changeColumnTypeToJson($table, $dataColumn);

        $this->processInBatchesNative($table, $dataColumn, function (PDO $pdo, array $row) use ($table, $dataColumn): void {
            $this->encodeSerializedData($pdo, $table, $dataColumn, $row);
        });
    }

    private function changeTypesFromJsonToLongtext(string $table, string $dataColumn): void
    {
        $this->changeColumnTypeToLongText($table, $dataColumn, false);

        $this->processInBatchesNative($table, $dataColumn, function (PDO $pdo, array $row) use ($table, $dataColumn): void {
            $this->decodeJsonAndSerialize($pdo, $table, $dataColumn, $row);
        });
    }

    private function encodeSerializedData(PDO $pdo, string $table, string $dataColumn, array $row): void
    {
        $id = (int) $row['id'];
        $data = $row[$dataColumn];

        $unserialized = @unserialize($data, ['allowed_classes' => false]);
        if ($unserialized === false) {
            return;
        }

        $json = json_encode($unserialized, JSON_THROW_ON_ERROR);

        $update = $pdo->prepare("UPDATE `$table` SET `$dataColumn` = :value WHERE id = :id");
        $update->bindValue(':value', $json);
        $update->bindValue(':id', $id, PDO::PARAM_INT);
        $update->execute();
        $update->closeCursor();
    }

    private function decodeJsonAndSerialize(PDO $pdo, string $table, string $dataColumn, array $row): void
    {
        $id = (int) $row['id'];
        $data = $row[$dataColumn];

        $decoded = json_decode($data, true, 512, JSON_THROW_ON_ERROR);
        $serialized = serialize($decoded);

        $update = $pdo->prepare("UPDATE `$table` SET `$dataColumn` = :value WHERE id = :id");
        $update->bindValue(':value', $serialized);
        $update->bindValue(':id', $id, PDO::PARAM_INT);
        $update->execute();
        $update->closeCursor();
    }

    /**
     * @param callable(PDO $pdo, array $row): void $callback
     */
    private function processInBatchesNative(string $table, string $column, callable $callback): void
    {
        /** @var PDO $pdo */
        $pdo = $this->connection->getNativeConnection();
        $lastId = 0;

        while (true) {
            $select = $pdo->prepare(
                sprintf(
                    'SELECT id, %s FROM %s WHERE id > :lastId ORDER BY id ASC LIMIT :limit',
                    $column,
                    $table
                )
            );
            $select->bindValue(':lastId', $lastId, PDO::PARAM_INT);
            $select->bindValue(':limit', self::BATCH_SIZE, PDO::PARAM_INT);
            $select->execute();

            $rows = $select->fetchAll(PDO::FETCH_ASSOC);
            $select->closeCursor();
            $select = null;

            if (count($rows) === 0) {
                break;
            }

            foreach ($rows as $row) {
                $callback($pdo, $row);
                $lastId = max($lastId, (int) $row['id']);
            }

            unset($rows);
            gc_collect_cycles();
        }
    }

    private function changeColumnTypeToJson(string $table, string $column): void
    {
        $this->addSql(sprintf('ALTER TABLE %s CHANGE %s %s JSON NOT NULL', $table, $column, $column));
    }

    private function changeColumnTypeToLongText(string $table, string $column, bool $withComment = true): void
    {
        $this->connection->executeQuery(sprintf(
            'ALTER TABLE %s CHANGE %s %s LONGTEXT NOT NULL %s',
            $table,
            $column,
            $column,
            $withComment ? 'COMMENT \'(DC2Type:array)\'' : ''
        ));
    }

    /**
     * @return iterable<array{string, string}>
     */
    private function tablesAndColumnsToBeUpdated(): iterable
    {
        yield ['sylius_admin_user', 'roles'];
        yield ['sylius_catalog_promotion_action', 'configuration'];
        yield ['sylius_catalog_promotion_scope', 'configuration'];
        yield ['sylius_product_attribute', 'configuration'];
        yield ['sylius_promotion_action', 'configuration'];
        yield ['sylius_promotion_rule', 'configuration'];
        yield ['sylius_shipping_method', 'configuration'];
        yield ['sylius_shipping_method_rule', 'configuration'];
    }
}
