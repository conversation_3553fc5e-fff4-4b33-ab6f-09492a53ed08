<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221101073020 extends AbstractMigration
{
    private const array TABLES = [
        'messenger_messages',
        'product_taxon_channel',
        'supplier',
        'supplier_country_shipping',
        'sylius_address',
        'sylius_address_log_entries',
        'sylius_adjustment',
        'sylius_admin_user',
        'sylius_avatar_image',
        'sylius_business_unit',
        'sylius_catalog_promotion',
        'sylius_catalog_promotion_action',
        'sylius_catalog_promotion_channels',
        'sylius_catalog_promotion_scope',
        'sylius_catalog_promotion_translation',
        'sylius_channel',
        'sylius_channel_countries',
        'sylius_channel_currencies',
        'sylius_channel_locales',
        'sylius_channel_pricing',
        'sylius_channel_pricing_catalog_promotions',
        'sylius_country',
        'sylius_currency',
        'sylius_customer',
        'sylius_customer_group',
        'sylius_exchange_rate',
        'sylius_gateway_config',
        'sylius_invoicing_plugin_billing_data',
        'sylius_invoicing_plugin_invoice',
        'sylius_invoicing_plugin_line_item',
        'sylius_invoicing_plugin_sequence',
        'sylius_invoicing_plugin_shop_billing_data',
        'sylius_invoicing_plugin_tax_item',
        'sylius_locale',
        'sylius_migrations',
        'sylius_order',
        'sylius_order_item',
        'sylius_order_item_previous',
        'sylius_order_item_unit',
        'sylius_order_sequence',
        'sylius_payment',
        'sylius_payment_method',
        'sylius_payment_method_channels',
        'sylius_payment_method_translation',
        'sylius_payment_security_token',
        'sylius_plus_customer_pool',
        'sylius_plus_inventory_source',
        'sylius_plus_inventory_source_address',
        'sylius_plus_inventory_source_channels',
        'sylius_plus_inventory_source_stock',
        'sylius_plus_loyalty_points_account',
        'sylius_plus_loyalty_points_transaction',
        'sylius_plus_loyalty_points_transactions',
        'sylius_plus_loyalty_purchase',
        'sylius_plus_loyalty_rule',
        'sylius_plus_loyalty_rule_action',
        'sylius_plus_loyalty_rule_channels',
        'sylius_plus_rbac_admin_user_roles',
        'sylius_plus_rbac_role',
        'sylius_plus_rbac_role_translation',
        'sylius_plus_return_request',
        'sylius_plus_return_request_credit_memos',
        'sylius_plus_return_request_image',
        'sylius_plus_return_request_items',
        'sylius_plus_return_request_unit',
        'sylius_product',
        'sylius_product_association',
        'sylius_product_association_product',
        'sylius_product_association_type',
        'sylius_product_association_type_translation',
        'sylius_product_attribute',
        'sylius_product_attribute_translation',
        'sylius_product_attribute_value',
        'sylius_product_channels',
        'sylius_product_image',
        'sylius_product_image_product_variants',
        'sylius_product_option',
        'sylius_product_option_translation',
        'sylius_product_option_value',
        'sylius_product_option_value_translation',
        'sylius_product_options',
        'sylius_product_review',
        'sylius_product_taxon',
        'sylius_product_translation',
        'sylius_product_variant',
        'sylius_product_variant_option_value',
        'sylius_product_variant_translation',
        'sylius_promotion',
        'sylius_promotion_action',
        'sylius_promotion_channels',
        'sylius_promotion_coupon',
        'sylius_promotion_order',
        'sylius_promotion_rule',
        'sylius_province',
        'sylius_refund_credit_memo',
        'sylius_refund_credit_memo_line_items',
        'sylius_refund_credit_memo_sequence',
        'sylius_refund_credit_memo_tax_items',
        'sylius_refund_customer_billing_data',
        'sylius_refund_line_item',
        'sylius_refund_refund',
        'sylius_refund_refund_payment',
        'sylius_refund_shop_billing_data',
        'sylius_refund_tax_item',
        'sylius_shipment',
        'sylius_shipping_category',
        'sylius_shipping_method',
        'sylius_shipping_method_channels',
        'sylius_shipping_method_rule',
        'sylius_shipping_method_translation',
        'sylius_shop_billing_data',
        'sylius_shop_user',
        'sylius_tax_category',
        'sylius_tax_rate',
        'sylius_taxon',
        'sylius_taxon_image',
        'sylius_taxon_translation',
        'sylius_user_oauth',
        'sylius_zone',
        'sylius_zone_member',
        'term_question',
        'term_question_translation',
    ];

    public function getDescription(): string
    {
        return 'Updates character set of all tables to utf8mb4 and collation to utf8mb4_unicode_520_ci.';
    }

    public function up(Schema $schema): void
    {
        foreach (self::TABLES as $tableName) {
            $this->addSql(sprintf('SET FOREIGN_KEY_CHECKS=0; ALTER TABLE %s CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci; SET FOREIGN_KEY_CHECKS=1;', $tableName));
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::TABLES as $tableName) {
            $this->addSql(sprintf('SET FOREIGN_KEY_CHECKS = 0;ALTER TABLE %s CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci; SET FOREIGN_KEY_CHECKS=1;', $tableName));
        }
    }
}
