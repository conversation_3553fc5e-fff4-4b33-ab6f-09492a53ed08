<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221118102115 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename sylius_order_item_previous.related_order_item_as_parent_id to parent_order_item_id';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD92B22002');
        $this->addSql('DROP INDEX IDX_DAE26FBD92B22002 ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE related_order_item_as_parent_id parent_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_DAE26FBD38939887 ON sylius_order_item_previous (parent_order_item_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD38939887');
        $this->addSql('DROP INDEX IDX_DAE26FBD38939887 ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE parent_order_item_id related_order_item_as_parent_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD92B22002 FOREIGN KEY (related_order_item_as_parent_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_DAE26FBD92B22002 ON sylius_order_item_previous (related_order_item_as_parent_id)');
    }
}
