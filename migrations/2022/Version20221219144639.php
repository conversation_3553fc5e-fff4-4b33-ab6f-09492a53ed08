<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221219144639 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add preferred order item and previous preferred order item tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE preferred_order_item (preferred_for_order_item_id INT NOT NULL, preferred_variant_id INT NOT NULL, number_of_preferred_variants INT DEFAULT 1 NOT NULL, usage_advice VARCHAR(255) DEFAULT NULL, INDEX IDX_9FE00220B54B11B7 (preferred_for_order_item_id), INDEX IDX_9FE00220F663B486 (preferred_variant_id), PRIMARY KEY(preferred_for_order_item_id, preferred_variant_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE previous_preferred_order_item (preferred_for_previous_order_item_id INT NOT NULL, preferred_variant_id INT NOT NULL, number_of_preferred_variants INT DEFAULT 1 NOT NULL, usage_advice VARCHAR(255) DEFAULT NULL, INDEX IDX_34A867F780AFC901 (preferred_for_previous_order_item_id), INDEX IDX_34A867F7F663B486 (preferred_variant_id), PRIMARY KEY(preferred_for_previous_order_item_id, preferred_variant_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220B54B11B7 FOREIGN KEY (preferred_for_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('ALTER TABLE preferred_order_item ADD CONSTRAINT FK_9FE00220F663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD CONSTRAINT FK_34A867F780AFC901 FOREIGN KEY (preferred_for_previous_order_item_id) REFERENCES sylius_order_item_previous (id)');
        $this->addSql('ALTER TABLE previous_preferred_order_item ADD CONSTRAINT FK_34A867F7F663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('DROP INDEX uniq_6196a1f940c4413e ON sylius_order');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F9DA8ECC4D ON sylius_order (medical_questionnaire)');
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587EDF663B486');
        $this->addSql('DROP INDEX IDX_77B587EDF663B486 ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item DROP preferred_variant_id');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBDF663B486');
        $this->addSql('DROP INDEX IDX_DAE26FBDF663B486 ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP preferred_variant_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE preferred_order_item');
        $this->addSql('DROP TABLE previous_preferred_order_item');
        $this->addSql('DROP INDEX uniq_6196a1f9da8ecc4d ON sylius_order');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F940C4413E ON sylius_order (medical_questionnaire)');
        $this->addSql('ALTER TABLE sylius_order_item ADD preferred_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587EDF663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('CREATE INDEX IDX_77B587EDF663B486 ON sylius_order_item (preferred_variant_id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD preferred_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBDF663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('CREATE INDEX IDX_DAE26FBDF663B486 ON sylius_order_item_previous (preferred_variant_id)');
    }
}
