<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221115080237 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the businessUnit to the marketingSubscriptions';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription ADD business_unit_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE marketing_subscription ADD CONSTRAINT FK_60B5EDE2A58ECB40 FOREIGN KEY (business_unit_id) REFERENCES sylius_business_unit (id)');
        $this->addSql('CREATE INDEX IDX_60B5EDE2A58ECB40 ON marketing_subscription (business_unit_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription DROP FOREIGN KEY FK_60B5EDE2A58ECB40');
        $this->addSql('DROP INDEX IDX_60B5EDE2A58ECB40 ON marketing_subscription');
        $this->addSql('ALTER TABLE marketing_subscription DROP business_unit_id');
    }
}
