<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221101091929 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds table for MarketingSubscription and adds relationship to Customer.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE marketing_subscription (uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\', first_name VARCHAR(255) DEFAULT NULL, last_name VARCHAR(255) DEFAULT NULL, email_address VARCHAR(255) NOT NULL, locale_code VARCHAR(255) NOT NULL, country_code VARCHAR(255) NOT NULL, opt_in_sms TINYINT(1) NOT NULL, opt_in_sms_subscription VARCHAR(150) NOT NULL COMMENT \'(DC2Type:subscriptionEnumType)\', opt_in_sms_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', opt_in_direct_mail TINYINT(1) NOT NULL, opt_in_direct_mail_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', opt_in_email_source VARCHAR(255) DEFAULT NULL, opt_in_email_subscription VARCHAR(150) NOT NULL COMMENT \'(DC2Type:subscriptionEnumType)\', opt_in_email_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', opt_in_email_subscription_service TINYINT(1) NOT NULL, opt_in_email_subscription_product_information TINYINT(1) NOT NULL, opt_in_email_subscription_customer_survey TINYINT(1) NOT NULL, opt_in_email_subscription_timeout_until DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', opt_in_email_unsubscribed_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_520_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_customer ADD marketing_subscription_uuid CHAR(36) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE sylius_customer ADD CONSTRAINT FK_7E82D5E65C337ABB FOREIGN KEY (marketing_subscription_uuid) REFERENCES marketing_subscription (uuid)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_7E82D5E65C337ABB ON sylius_customer (marketing_subscription_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_customer DROP FOREIGN KEY FK_7E82D5E65C337ABB');
        $this->addSql('DROP TABLE marketing_subscription');
        $this->addSql('DROP INDEX UNIQ_7E82D5E65C337ABB ON sylius_customer');
        $this->addSql('ALTER TABLE sylius_customer DROP marketing_subscription_uuid');
    }
}
