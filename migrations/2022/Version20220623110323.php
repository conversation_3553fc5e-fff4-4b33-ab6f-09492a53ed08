<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220623110323 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added addPrescriptionMedicationDirectlyToCart property to Channel entity';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel ADD addPrescriptionMedicationDirectlyToCart TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel DROP addPrescriptionMedicationDirectlyToCart');
    }
}
