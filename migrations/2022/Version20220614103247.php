<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220614103247 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removed required supplier because consults as products do not have suppliers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE supplier_id supplier_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE supplier_id supplier_id INT NOT NULL');
    }
}
