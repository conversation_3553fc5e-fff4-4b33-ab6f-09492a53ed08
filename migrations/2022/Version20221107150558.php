<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221107150558 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Updates pivot table `sylius_payment_method_channels` for linking payment methods to channels with extra information.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment_method_channels DROP FOREIGN KEY FK_543AC0CC5AA1164F');
        $this->addSql('ALTER TABLE sylius_payment_method_channels DROP FOREIGN KEY FK_543AC0CC72F5A1AA');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD maximum_order_total INT DEFAULT NULL, DROP PRIMARY KEY');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD CONSTRAINT FK_543AC0CC5AA1164F FOREIGN KEY (payment_method_id) REFERENCES sylius_payment_method (id)');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD CONSTRAINT FK_543AC0CC72F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id)');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD PRIMARY KEY (payment_method_id, channel_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment_method_channels DROP FOREIGN KEY FK_543AC0CC5AA1164F');
        $this->addSql('ALTER TABLE sylius_payment_method_channels DROP FOREIGN KEY FK_543AC0CC72F5A1AA');
        $this->addSql('ALTER TABLE sylius_payment_method_channels DROP PRIMARY KEY');
        $this->addSql('ALTER TABLE sylius_payment_method_channels DROP maximum_order_total');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD CONSTRAINT FK_543AC0CC5AA1164F FOREIGN KEY (payment_method_id) REFERENCES sylius_payment_method (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD CONSTRAINT FK_543AC0CC72F5A1AA FOREIGN KEY (channel_id) REFERENCES sylius_channel (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_payment_method_channels ADD PRIMARY KEY (payment_method_id, channel_id)');
    }
}
