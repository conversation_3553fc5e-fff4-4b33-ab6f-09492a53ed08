<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220608135946 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add enabled column to `supplier_country_shipping` and use `supplier_id` and `country_id` as composite primary key. Also make `shipping_cost` nullable.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier_country_shipping MODIFY id INT NOT NULL');
        $this->addSql('ALTER TABLE supplier_country_shipping DROP PRIMARY KEY');
        $this->addSql('ALTER TABLE supplier_country_shipping ADD enabled TINYINT(1) DEFAULT \'0\' NOT NULL, DROP id, CHANGE shipping_cost shipping_cost INT DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier_country_shipping ADD PRIMARY KEY (supplier_id, country_id)');
        $this->addSql('ALTER TABLE supplier_country_shipping RENAME INDEX idx_36dabff52add6d8c TO IDX_49D7F06C2ADD6D8C');
        $this->addSql('ALTER TABLE supplier_country_shipping RENAME INDEX idx_36dabff5f92f3e70 TO IDX_49D7F06CF92F3E70');
        $this->addSql('ALTER TABLE supplier_country_shipping RENAME INDEX uniq_36dabff52add6d8cf92f3e70 TO UNIQ_49D7F06C2ADD6D8CF92F3E70');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE supplier_country_shipping ADD id INT AUTO_INCREMENT NOT NULL, DROP enabled, CHANGE shipping_cost shipping_cost INT DEFAULT 0 NOT NULL, DROP PRIMARY KEY, ADD PRIMARY KEY (id)');
        $this->addSql('ALTER TABLE supplier_country_shipping RENAME INDEX idx_49d7f06c2add6d8c TO IDX_36DABFF52ADD6D8C');
        $this->addSql('ALTER TABLE supplier_country_shipping RENAME INDEX idx_49d7f06cf92f3e70 TO IDX_36DABFF5F92F3E70');
        $this->addSql('ALTER TABLE supplier_country_shipping RENAME INDEX uniq_49d7f06c2add6d8cf92f3e70 TO UNIQ_36DABFF52ADD6D8CF92F3E70');
    }
}
