<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220331222300 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add table for PreviousOrder<PERSON>tem to move the orderitems into when they are replaced on the order.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE sylius_order_item_previous (id INT AUTO_INCREMENT NOT NULL, order_id INT NOT NULL, variant_id INT DEFAULT NULL, quantity INT NOT NULL, unit_price INT NOT NULL, units_total INT NOT NULL, adjustments_total INT NOT NULL, total INT NOT NULL, is_immutable TINYINT(1) NOT NULL, product_name VARCHAR(255) DEFAULT NULL, variant_name VARCHAR(255) DEFAULT NULL, version INT DEFAULT 1 NOT NULL, unit_cost_price INT DEFAULT 0 NOT NULL, cost_price_total INT DEFAULT 0 NOT NULL, warnings JSON DEFAULT (JSON_ARRAY()) NOT NULL, usageAdvice VARCHAR(255) DEFAULT NULL, INDEX IDX_DAE26FBD8D9F6D38 (order_id), INDEX IDX_DAE26FBD3B69A9AF (variant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD8D9F6D38 FOREIGN KEY (order_id) REFERENCES sylius_order (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD3B69A9AF FOREIGN KEY (variant_id) REFERENCES sylius_product_variant (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE sylius_order_item_previous');
    }
}
