<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221123094221 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Business unit is required for marketing subscription. Setting NULL values to 1 (dokteronline) as default';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE `marketing_subscription` SET `business_unit_id` = '1' WHERE `business_unit_id` IS NULL");
        $this->addSql('ALTER TABLE marketing_subscription CHANGE business_unit_id business_unit_id INT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE marketing_subscription CHANGE business_unit_id business_unit_id INT DEFAULT NULL');
    }
}
