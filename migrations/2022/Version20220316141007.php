<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220316141007 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds usageAdvice to OrderItem';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_order_item
                ADD usageAdvice VARCHAR(255) DEFAULT NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_order_item
                DROP usageAdvice;
        ');
    }
}
