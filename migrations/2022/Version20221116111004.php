<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\MarketingSubscription\MarketingSubscription;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221116111004 extends AbstractMigration
{
    public function getDescription(): string
    {
        return sprintf('Adds (or removes) composite unique index on %1$s::$emailAddress, %1$s::$locale, %1$s::$country and %1$s::$businessUnit.', MarketingSubscription::class);
    }

    public function up(Schema $schema): void
    {
        // remove duplicate marketing subscriptions by email with no customer attached
        $this->addSql('
            DELETE FROM marketing_subscription
            WHERE uuid IN (
                SELECT
                    m.uuid
                FROM
                    marketing_subscription m
                LEFT JOIN
                    sylius_customer c ON m.uuid = c.marketing_subscription_uuid
                WHERE
                    c.marketing_subscription_uuid IS NULL
                AND m.email_address IN (
                    SELECT
                        m.email_address
                    FROM
                        marketing_subscription m
                    GROUP BY
                        m.email_address,
                        m.locale_code,
                        m.country_code,
                        m.business_unit_id
                    HAVING
                        COUNT(m.country_code) > 1 OR
                        COUNT(m.locale_code) > 1
                )
            )
        ');

        // set marketing subscription email same as customer email
        $this->addSql('
            UPDATE marketing_subscription m
            INNER JOIN sylius_customer c ON m.uuid = c.marketing_subscription_uuid
            SET m.email_address = c.email
            WHERE c.email <> m.email_address
        ');

        // update customers with customer pool 'default'
        $this->addSql("
            UPDATE marketing_subscription
            SET
                email_address = CONCAT('default-customer-pool-', marketing_subscription.email_address),
                country_code = 'XX'
            WHERE `uuid` IN (
                SELECT sylius_customer.marketing_subscription_uuid
                FROM sylius_customer
                WHERE customer_pool_id = (SELECT id FROM sylius_plus_customer_pool WHERE code = 'default')
            );
        ");

        // disable customers with customer pool 'default'
        $this->addSql("
            UPDATE sylius_shop_user
            SET enabled = 0
            WHERE customer_id IN (
                SELECT id
                FROM sylius_customer
                WHERE customer_pool_id = (SELECT id FROM sylius_plus_customer_pool WHERE code = 'default')
            );
        ");

        $this->addSql('CREATE UNIQUE INDEX emailaddress_locale_country_business_unit_unique ON marketing_subscription (email_address, locale_code, country_code, business_unit_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX emailaddress_locale_country_business_unit_unique ON marketing_subscription');
    }
}
