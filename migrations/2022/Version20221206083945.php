<?php

declare(strict_types=1);

namespace App\Migrations;

use App\Entity\MarketingSubscription\Subscription;
use DateTime;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Ramsey\Uuid\Uuid;

final class Version20221206083945 extends AbstractMigration
{
    private const string LOCALE = 'nl';
    private const int FALLBACK_BUSINESS_UNIT_ID = 3; // Dokteronline
    private const string FALLBACK_COUNTRY_CODE = 'nl';

    public function getDescription(): string
    {
        return 'Marketing subscription and locale code are now a required for a customer.';
    }

    public function up(Schema $schema): void
    {
        if (!$schema->getTable('sylius_customer')->hasColumn('locale_code')) {
            $this->addSql('ALTER TABLE sylius_customer ADD locale_code VARCHAR(255) NOT NULL');
        }

        // Make sure all customers have a locale code (default is nl).
        $this->addSql('UPDATE sylius_customer SET locale_code = "nl" WHERE locale_code = ""');

        // Marketing subscriptions are now required
        $this->addSql('ALTER TABLE sylius_customer CHANGE marketing_subscription_uuid marketing_subscription_uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\'');
    }

    /**
     * Create marketing subscriptions for customers that don't have one.
     */
    public function preUp(Schema $schema): void
    {
        // Clear existing marketing subscriptions.
        $this->connection->executeStatement('UPDATE sylius_customer SET marketing_subscription_uuid = null WHERE 1=1');
        $this->connection->executeStatement('DELETE FROM marketing_subscription WHERE 1=1');

        $customersWithoutMarketingSubscriptionQuery = '
            SELECT
                sc.id AS customer_id,
                sc.first_name,
                sc.last_name,
                sc.email_canonical,
                sbu.id AS business_unit_id,
                sbu.address_country_code AS country_code
            FROM sylius_customer sc
            LEFT JOIN sylius_plus_customer_pool spcp on sc.customer_pool_id = spcp.id
            LEFT JOIN sylius_business_unit sbu on spcp.code = sbu.code';

        $statement = $this->connection->executeQuery($customersWithoutMarketingSubscriptionQuery);

        // Insert marketing subscriptions for customer that don't have one.
        while (($row = $statement->fetchAssociative()) !== false) {
            $marketingSubscriptionId = Uuid::uuid4()->toString();

            $this->connection->insert(
                'marketing_subscription',
                [
                    'uuid' => $marketingSubscriptionId,
                    'first_name' => $row['first_name'],
                    'last_name' => $row['last_name'],
                    'email_address' => $row['email_canonical'],
                    'locale_code' => self::LOCALE,
                    'country_code' => $row['country_code'] ?? self::FALLBACK_COUNTRY_CODE,
                    'opt_in_sms' => 0,
                    'opt_in_sms_subscription' => Subscription::NONE->value,
                    'opt_in_direct_mail' => 0,
                    'opt_in_email_subscription' => Subscription::NONE->value,
                    'opt_in_email_subscription_service' => 0,
                    'opt_in_email_subscription_product_information' => 0,
                    'opt_in_email_subscription_customer_survey' => 0,
                    'created_at' => (new DateTime())->format('Y-m-d H:i:s'),
                    'business_unit_id' => $row['business_unit_id'] ?? self::FALLBACK_BUSINESS_UNIT_ID,
                ]
            );

            // Set marketing subscription UUID on customer
            $this->connection->update(
                'sylius_customer',
                [
                    'marketing_subscription_uuid' => $marketingSubscriptionId,
                ],
                [
                    'id' => $row['customer_id'],
                ]
            );
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_customer DROP locale_code');
        $this->addSql('ALTER TABLE sylius_customer CHANGE marketing_subscription_uuid marketing_subscription_uuid CHAR(36) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_520_ci` COMMENT \'(DC2Type:uuid)\'');
    }
}
