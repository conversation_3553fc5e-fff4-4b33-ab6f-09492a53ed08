<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221031102608 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fixed columnDefinition for JSON columns for doctrine diff.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order CHANGE checkout_terms_answers checkout_terms_answers LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE warnings warnings LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order CHANGE checkout_terms_answers checkout_terms_answers LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_bin`');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_bin`');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE warnings warnings LONGTEXT CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_bin`');
    }
}
