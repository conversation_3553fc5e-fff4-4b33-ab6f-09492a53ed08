<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221010101805 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings JSON DEFAULT (JSON_ARRAY()) NOT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD92B22002');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE warnings warnings JSON DEFAULT (JSON_ARRAY()) NOT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD92B22002 FOREIGN KEY (related_order_item_as_parent_id) REFERENCES sylius_order_item (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings JSON DEFAULT \'json_array()\' NOT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBD92B22002');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE warnings warnings JSON DEFAULT \'json_array()\' NOT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBD92B22002 FOREIGN KEY (related_order_item_as_parent_id) REFERENCES sylius_order_item_previous (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
    }
}
