<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220328160316 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add preferred_variant_for_minimum_daily_orders to sylius_product_variant';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_product_variant
                ADD preferred_variant_for_minimum_daily_orders INT DEFAULT NULL
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_product_variant
                DROP preferred_variant_for_minimum_daily_orders
        ');
    }
}
