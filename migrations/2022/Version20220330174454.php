<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220330174454 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fixed doctrine migrations issues with doctrine:migrations:diff so we can use the command again to generate migrations for entities without this SQL being added every time.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE product_taxon_channel RENAME INDEX idx_806a787282f46ceb TO IDX_5F1C6A2B82F46CEB');
        $this->addSql('ALTER TABLE product_taxon_channel RENAME INDEX idx_806a787272f5a1aa TO IDX_5F1C6A2B72F5A1AA');
        $this->addSql('ALTER TABLE supplier_country RENAME INDEX fk_supplier TO IDX_D216BD792ADD6D8C');
        $this->addSql('ALTER TABLE supplier_country RENAME INDEX fk_country TO IDX_D216BD79F92F3E70');
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE preferred_variant_for_minimum_daily_orders preferred_variant_for_minimum_daily_orders INT DEFAULT NULL COMMENT \'If this value is set, it indicates the minimum number of times per day this variant is preferred over the same variant of another supplier while the variant hasn\'\'t been ordered more than the number set. Whether this variant is preferred is calculated by this value and the amount ordered today value.\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE product_taxon_channel RENAME INDEX idx_5f1c6a2b72f5a1aa TO IDX_806A787272F5A1AA');
        $this->addSql('ALTER TABLE product_taxon_channel RENAME INDEX idx_5f1c6a2b82f46ceb TO IDX_806A787282F46CEB');
        $this->addSql('ALTER TABLE supplier_country RENAME INDEX idx_d216bd79f92f3e70 TO FK_COUNTRY');
        $this->addSql('ALTER TABLE supplier_country RENAME INDEX idx_d216bd792add6d8c TO FK_SUPPLIER');
        $this->addSql('ALTER TABLE sylius_product_variant CHANGE preferred_variant_for_minimum_daily_orders preferred_variant_for_minimum_daily_orders INT DEFAULT NULL');
    }
}
