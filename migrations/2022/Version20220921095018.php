<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220921095018 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Renamed relatedOrder to followUpOrder.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP FOREIGN KEY FK_6196A1F92B1C2395');
        $this->addSql('DROP INDEX UNIQ_6196A1F92B1C2395 ON sylius_order');
        $this->addSql('ALTER TABLE sylius_order CHANGE related_order_id follow_up_order_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order ADD CONSTRAINT FK_6196A1F9CD28AEAA FOREIGN KEY (follow_up_order_id) REFERENCES sylius_order (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F9CD28AEAA ON sylius_order (follow_up_order_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP FOREIGN KEY FK_6196A1F9CD28AEAA');
        $this->addSql('DROP INDEX UNIQ_6196A1F9CD28AEAA ON sylius_order');
        $this->addSql('ALTER TABLE sylius_order CHANGE follow_up_order_id related_order_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order ADD CONSTRAINT FK_6196A1F92B1C2395 FOREIGN KEY (related_order_id) REFERENCES sylius_order (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F92B1C2395 ON sylius_order (related_order_id)');
    }
}
