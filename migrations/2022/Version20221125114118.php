<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221125114118 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds medical_questionnaire column to store reference to Anamnesis Service questionnaire.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD medical_questionnaire CHAR(36) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F940C4413E ON sylius_order (medical_questionnaire)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_6196A1F940C4413E ON sylius_order');
        $this->addSql('ALTER TABLE sylius_order DROP medical_questionnaire');
    }
}
