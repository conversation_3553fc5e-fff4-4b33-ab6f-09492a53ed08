<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220708091544 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Change naming strategy to doctrine.orm.naming_strategy.underscore_number_aware
        // @see config/packages/doctrine.yaml
        $this->addSql('ALTER TABLE product_taxon_channel DROP FOREIGN KEY FK_806A787282F46CEB');
        $this->addSql('DROP INDEX IDX_5F1C6A2B82F46CEB ON product_taxon_channel');
        $this->addSql('ALTER TABLE product_taxon_channel CHANGE producttaxon_id product_taxon_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE product_taxon_channel ADD CONSTRAINT FK_5F1C6A2B15A39EE7 FOREIGN KEY (product_taxon_id) REFERENCES sylius_product_taxon (id)');
        $this->addSql('CREATE INDEX IDX_5F1C6A2B15A39EE7 ON product_taxon_channel (product_taxon_id)');
        $this->addSql('ALTER TABLE sylius_channel CHANGE addprescriptionmedicationdirectlytocart add_prescription_medication_directly_to_cart TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE sylius_plus_return_request CHANGE currencycode currency_code VARCHAR(255) NOT NULL');

        // Add related variant id for relating medication to consult product variant
        $this->addSql('ALTER TABLE sylius_order_item ADD related_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587EDB1A4A11F FOREIGN KEY (related_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('CREATE INDEX IDX_77B587EDB1A4A11F ON sylius_order_item (related_variant_id)');

        // Also for previous order item
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD related_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBDB1A4A11F FOREIGN KEY (related_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('CREATE INDEX IDX_DAE26FBDB1A4A11F ON sylius_order_item_previous (related_variant_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE product_taxon_channel DROP FOREIGN KEY FK_5F1C6A2B15A39EE7');
        $this->addSql('DROP INDEX IDX_5F1C6A2B15A39EE7 ON product_taxon_channel');
        $this->addSql('ALTER TABLE product_taxon_channel CHANGE product_taxon_id productTaxon_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE product_taxon_channel ADD CONSTRAINT FK_806A787282F46CEB FOREIGN KEY (productTaxon_id) REFERENCES sylius_product_taxon (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX IDX_5F1C6A2B82F46CEB ON product_taxon_channel (productTaxon_id)');
        $this->addSql('ALTER TABLE sylius_channel CHANGE add_prescription_medication_directly_to_cart addPrescriptionMedicationDirectlyToCart TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE sylius_plus_return_request CHANGE currency_code currencyCode VARCHAR(255) CHARACTER SET utf8mb3 NOT NULL COLLATE `utf8_unicode_ci`');

        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587EDB1A4A11F');
        $this->addSql('DROP INDEX IDX_77B587EDB1A4A11F ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item DROP related_variant_id');

        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBDB1A4A11F');
        $this->addSql('DROP INDEX IDX_DAE26FBDB1A4A11F ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP related_variant_id');
    }
}
