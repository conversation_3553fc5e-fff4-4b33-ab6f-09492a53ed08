<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220315140023 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds prescriptionFilename, doctorName and doctorRegistrationNumber to Order';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_order
                ADD prescriptionFilename VARCHAR(255) DEFAULT NULL,
                ADD doctorName VARCHAR(255) DEFAULT NULL,
                ADD doctorRegistrationNumber VARCHAR(255) DEFAULT NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE sylius_order
                DROP prescriptionFilename,
                DROP doctorName,
                DROP doctorRegistrationNumber;
        ');
    }
}
