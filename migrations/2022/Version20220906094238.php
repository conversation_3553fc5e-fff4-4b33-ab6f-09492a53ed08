<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220906094238 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add relations between order items and fix name related to preferred variant';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel DROP INDEX FK_16C8119E987ACBE9, ADD UNIQUE INDEX UNIQ_16C8119E987ACBE9 (medication_channel_id)');
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587EDB1A4A11F');
        $this->addSql('DROP INDEX IDX_77B587EDB1A4A11F ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item ADD parent_order_item_id INT DEFAULT NULL, CHANGE related_variant_id preferred_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587EDF663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_77B587EDF663B486 ON sylius_order_item (preferred_variant_id)');
        $this->addSql('CREATE INDEX IDX_77B587ED38939887 ON sylius_order_item (parent_order_item_id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBDB1A4A11F');
        $this->addSql('DROP INDEX IDX_DAE26FBDB1A4A11F ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD related_order_item_id INT DEFAULT NULL, CHANGE related_variant_id preferred_variant_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBDF663B486 FOREIGN KEY (preferred_variant_id) REFERENCES sylius_product_variant (id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBDC47318A FOREIGN KEY (related_order_item_id) REFERENCES sylius_order_item_previous (id)');
        $this->addSql('CREATE INDEX IDX_DAE26FBDF663B486 ON sylius_order_item_previous (preferred_variant_id)');
        $this->addSql('CREATE INDEX IDX_DAE26FBDC47318A ON sylius_order_item_previous (related_order_item_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel DROP INDEX UNIQ_16C8119E987ACBE9, ADD INDEX FK_16C8119E987ACBE9 (medication_channel_id)');
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587EDF663B486');
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED38939887');
        $this->addSql('DROP INDEX IDX_77B587EDF663B486 ON sylius_order_item');
        $this->addSql('DROP INDEX IDX_77B587ED38939887 ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item ADD related_variant_id INT DEFAULT NULL, DROP preferred_variant_id, DROP parent_order_item_id');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587EDB1A4A11F FOREIGN KEY (related_variant_id) REFERENCES sylius_product_variant (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX IDX_77B587EDB1A4A11F ON sylius_order_item (related_variant_id)');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBDF663B486');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP FOREIGN KEY FK_DAE26FBDC47318A');
        $this->addSql('DROP INDEX IDX_DAE26FBDF663B486 ON sylius_order_item_previous');
        $this->addSql('DROP INDEX IDX_DAE26FBDC47318A ON sylius_order_item_previous');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD related_variant_id INT DEFAULT NULL, DROP preferred_variant_id, DROP related_order_item_id');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD CONSTRAINT FK_DAE26FBDB1A4A11F FOREIGN KEY (related_variant_id) REFERENCES sylius_product_variant (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX IDX_DAE26FBDB1A4A11F ON sylius_order_item_previous (related_variant_id)');
    }
}
