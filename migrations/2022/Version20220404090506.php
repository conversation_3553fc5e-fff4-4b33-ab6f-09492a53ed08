<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220404090506 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds the amount_ordered_today and amount_ordered_today_updated_at column to sylius_product_variant table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant ADD amount_ordered_today INT NOT NULL, ADD amount_ordered_today_updated_at DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_product_variant DROP amount_ordered_today, DROP amount_ordered_today_updated_at');
    }
}
