<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220329211652 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add a table to link suppliers to countries.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE supplier_country (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supplier_id INT NOT NULL,
                country_id INT NOT NULL,
                CONSTRAINT FK_SUPPLIER FOREIGN KEY (supplier_id) REFERENCES supplier(id),
                CONSTRAINT FK_COUNTRY FOREIGN KEY (country_id) REFERENCES sylius_country(id)
            )
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE supplier_country');
    }
}
