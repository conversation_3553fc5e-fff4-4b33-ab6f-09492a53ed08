<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220804082834 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds a medication channel to consult channel relation to the database.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel ADD medication_channel_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_channel ADD CONSTRAINT FK_16C8119E987ACBE9 FOREIGN KEY (medication_channel_id) REFERENCES sylius_channel (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_channel DROP FOREIGN KEY FK_16C8119E987ACBE9');
        $this->addSql('ALTER TABLE sylius_channel DROP medication_channel_id');
    }
}
