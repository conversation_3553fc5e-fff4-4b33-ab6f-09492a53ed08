<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220309092028 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds the supplier_shipment_reference column to the sylius_shipment table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shipment ADD supplier_shipment_reference VARCHAR(255) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_FD707B33DF791B62 ON sylius_shipment (supplier_shipment_reference)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_FD707B33DF791B62 ON sylius_shipment');
        $this->addSql('ALTER TABLE sylius_shipment DROP supplier_shipment_reference');
    }
}
