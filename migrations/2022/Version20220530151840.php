<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220530151840 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove supplier_country table and rename supplier_shipping_cost table to supplier_country_shipping.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE supplier_country_shipping (id INT AUTO_INCREMENT NOT NULL, supplier_id INT NOT NULL, country_id INT NOT NULL, shipping_cost INT DEFAULT 0 NOT NULL, INDEX IDX_36DABFF52ADD6D8C (supplier_id), INDEX IDX_36DABFF5F92F3E70 (country_id), UNIQUE INDEX UNIQ_36DABFF52ADD6D8CF92F3E70 (supplier_id, country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE supplier_country_shipping ADD CONSTRAINT FK_36DABFF52ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
        $this->addSql('ALTER TABLE supplier_country_shipping ADD CONSTRAINT FK_36DABFF5F92F3E70 FOREIGN KEY (country_id) REFERENCES sylius_country (id)');
        $this->addSql('DROP TABLE supplier_country');
        $this->addSql('DROP TABLE supplier_shipping_cost');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE supplier_country (id INT AUTO_INCREMENT NOT NULL, supplier_id INT NOT NULL, country_id INT NOT NULL, INDEX IDX_D216BD792ADD6D8C (supplier_id), INDEX IDX_D216BD79F92F3E70 (country_id), UNIQUE INDEX UNIQ_D216BD792ADD6D8CF92F3E70 (supplier_id, country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE supplier_shipping_cost (id INT AUTO_INCREMENT NOT NULL, supplier_id INT NOT NULL, country_id INT NOT NULL, shipping_cost INT DEFAULT 0 NOT NULL, INDEX IDX_9B4782392ADD6D8C (supplier_id), INDEX IDX_9B478239F92F3E70 (country_id), UNIQUE INDEX UNIQ_9B4782392ADD6D8CF92F3E70 (supplier_id, country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE supplier_country ADD CONSTRAINT FK_COUNTRY FOREIGN KEY (country_id) REFERENCES sylius_country (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE supplier_country ADD CONSTRAINT FK_SUPPLIER FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE supplier_shipping_cost ADD CONSTRAINT FK_9B4782392ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE supplier_shipping_cost ADD CONSTRAINT FK_9B478239F92F3E70 FOREIGN KEY (country_id) REFERENCES sylius_country (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('DROP TABLE supplier_country_shipping');
    }
}
