<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220405194623 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added unique constraint on the supplier_country table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX UNIQ_D216BD792ADD6D8CF92F3E70 ON supplier_country (supplier_id, country_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_D216BD792ADD6D8CF92F3E70 ON supplier_country');
    }
}
