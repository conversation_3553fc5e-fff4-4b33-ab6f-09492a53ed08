<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220126074338 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds password_changed_at to the sylius_shop_user';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shop_user ADD password_changed_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL AFTER password_requested_at');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_shop_user DROP password_changed_at');
    }
}
