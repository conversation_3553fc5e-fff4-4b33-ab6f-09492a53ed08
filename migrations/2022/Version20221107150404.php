<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221107150404 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds differentAddressAllowed to PaymentMethod.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment_method ADD different_address_allowed TINYINT(1) NOT NULL');
        $this->addSql('UPDATE sylius_payment_method SET different_address_allowed = 1');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_payment_method DROP different_address_allowed');
    }
}
