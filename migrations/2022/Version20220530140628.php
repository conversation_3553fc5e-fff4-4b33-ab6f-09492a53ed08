<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220530140628 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings JSON DEFAULT (JSON_ARRAY()) NOT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous ADD original_unit_price INT DEFAULT NULL, CHANGE warnings warnings JSON DEFAULT (JSON_ARRAY()) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE sylius_order_item CHANGE warnings warnings JSON DEFAULT \'json_array()\' NOT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous DROP original_unit_price, CHANGE warnings warnings JSON DEFAULT \'json_array()\' NOT NULL');
    }
}
