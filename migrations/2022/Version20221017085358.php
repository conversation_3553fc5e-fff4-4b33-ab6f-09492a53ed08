<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221017085358 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds canopyDeployRemoveOrderItemWebhookUrl to BusinessUnit.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD canopy_deploy_remove_order_item_webhook_url VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP canopy_deploy_remove_order_item_webhook_url');
    }
}
