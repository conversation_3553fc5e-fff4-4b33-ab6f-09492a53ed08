<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220805141441 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add self referencing `related_order_id` column to link orders';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD related_order_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order ADD CONSTRAINT FK_6196A1F92B1C2395 FOREIGN KEY (related_order_id) REFERENCES sylius_order (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6196A1F92B1C2395 ON sylius_order (related_order_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order DROP FOREIGN KEY FK_6196A1F92B1C2395');
        $this->addSql('DROP INDEX UNIQ_6196A1F92B1C2395 ON sylius_order');
        $this->addSql('ALTER TABLE sylius_order DROP related_order_id');
    }
}
