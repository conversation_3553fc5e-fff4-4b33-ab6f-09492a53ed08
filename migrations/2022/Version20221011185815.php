<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221011185815 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add CanopyDeploy API endpoints to business units.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD canopy_deploy_order_webhook_url VARCHAR(255) DEFAULT NULL, ADD canopy_deploy_customer_webhook_url VARCHAR(255) DEFAULT NULL, ADD canopy_deploy_password_reset_webhook_url VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP canopy_deploy_order_webhook_url, DROP canopy_deploy_customer_webhook_url, DROP canopy_deploy_password_reset_webhook_url');
    }
}
