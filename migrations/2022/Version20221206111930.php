<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221206111930 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds url to BusinessUnit.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit ADD `url` VARCHAR(255) NOT NULL AFTER `code`');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_business_unit DROP `url`');
    }
}
