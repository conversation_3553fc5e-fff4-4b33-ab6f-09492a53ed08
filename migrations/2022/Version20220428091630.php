<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220428091630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Changed camelCase to underscores in column names';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD prescription_filename VARCHAR(255) DEFAULT NULL, ADD doctor_name VARCHAR(255) DEFAULT NULL, ADD doctor_registration_number VARCHAR(255) DEFAULT NULL, DROP prescriptionFilename, DROP doctorName, DROP doctorRegistrationNumber');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE usageadvice usage_advice VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE usageadvice usage_advice VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order ADD prescriptionFilename VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, ADD doctorName VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, ADD doctorRegistrationNumber VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, DROP prescription_filename, DROP doctor_name, DROP doctor_registration_number');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE usage_advice usageAdvice VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`');
        $this->addSql('ALTER TABLE sylius_order_item_previous CHANGE usage_advice usageAdvice VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');
    }
}
