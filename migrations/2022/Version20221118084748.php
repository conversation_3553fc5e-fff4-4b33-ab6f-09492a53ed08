<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221118084748 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename sylius_order_item.related_order_item_as_parent_id to parent_order_item_id';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED92B22002');
        $this->addSql('DROP INDEX IDX_77B587ED92B22002 ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE related_order_item_as_parent_id parent_order_item_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED38939887 FOREIGN KEY (parent_order_item_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_77B587ED38939887 ON sylius_order_item (parent_order_item_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sylius_order_item DROP FOREIGN KEY FK_77B587ED38939887');
        $this->addSql('DROP INDEX IDX_77B587ED38939887 ON sylius_order_item');
        $this->addSql('ALTER TABLE sylius_order_item CHANGE parent_order_item_id related_order_item_as_parent_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE sylius_order_item ADD CONSTRAINT FK_77B587ED92B22002 FOREIGN KEY (related_order_item_as_parent_id) REFERENCES sylius_order_item (id)');
        $this->addSql('CREATE INDEX IDX_77B587ED92B22002 ON sylius_order_item (related_order_item_as_parent_id)');
    }
}
