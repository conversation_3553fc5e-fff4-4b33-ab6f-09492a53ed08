<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20190101000000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->abortIf(!$this->shouldExecuteMigration(), 'Sylius Plus migrations not yet executed.');

        // Out with the old...
        $this->addSql('DELETE FROM sylius_migrations WHERE version LIKE :version', ['version' => 'Sylius\\\\Plus\\\\%']);
        $this->addSql('DELETE FROM sylius_migrations WHERE version LIKE :version', ['version' => 'Sylius\\\\InvoicingPlugin\\\\%']);
    }

    /**
     * Checks if Sylius Plus migrations are present in sylius_migrations table.
     * If so, this migration should be executed.
     */
    private function shouldExecuteMigration(): bool
    {
        $result = $this->connection->fetchOne('
            SELECT COUNT(version)
            FROM sylius_migrations
            WHERE version = "Sylius\\Plus\\Migrations\\Version20190522121616"
        ');

        return $result === 0;
    }
}
