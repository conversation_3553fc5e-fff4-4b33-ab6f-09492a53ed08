ARG COMPOSER_INSTALL_ARGS='--no-dev --optimize-autoloader --no-interaction --no-scripts'

FROM ghcr.io/superbrave/php:8.3 AS php

ARG COMPOSER_INSTALL_ARGS

ENV COMPOSER_INSTALL_ARGS=$COMPOSER_INSTALL_ARGS

USER root

RUN chown -R www-data:www-data /var/www /app-code \
 && chgrp -R www-data /usr/local/etc/php/ \
 && chmod -R go+rw /usr/local/etc/php /usr/local/var/log

RUN apk update && apk upgrade --ignore libcurl

WORKDIR /var/www

COPY ./.docker/php.conf.d/ /usr/local/etc/php/conf.d/

COPY --chown=www-data:www-data . /var/www/

RUN --mount=type=secret,id=COMPOSER_AUTH cat /run/secrets/COMPOSER_AUTH | base64 -d > /home/<USER>/.composer/auth.json \
 && chown www-data:www-data /home/<USER>/.composer/auth.json

USER www-data

RUN APP_ENV=none APP_DEBUG=0 composer install $(echo $COMPOSER_INSTALL_ARGS | tr -d '"') \
 && APP_ENV=none php -d memory_limit=-1 bin/console assets:install public \
 && mkdir -p /var/www/public/media/image \
 && rm /home/<USER>/.composer/auth.json

FROM ghcr.io/superbrave/node:20 AS node

ARG COMPOSER_INSTALL_ARGS

ENV COMPOSER_INSTALL_ARGS=$COMPOSER_INSTALL_ARGS

USER root

COPY --from=php --chown=www-data:www-data /var/www /var/www

RUN --mount=type=secret,id=NPM_AUTH cat /run/secrets/NPM_AUTH > /home/<USER>/.npmrc \
 && chown www-data:www-data /home/<USER>/.npmrc

USER www-data

RUN NODE_ENV=prod GULP_ENV=prod yarn install \
 && yarn build \
 && rm /home/<USER>/.npmrc

FROM php AS application

ARG COMPOSER_INSTALL_ARGS

ENV COMPOSER_INSTALL_ARGS=$COMPOSER_INSTALL_ARGS

WORKDIR /var/www

COPY --from=node --chown=www-data:www-data /var/www/public/build /var/www/public/build
COPY --from=node --chown=www-data:www-data /var/www/node_modules /var/www/node_modules

RUN APP_ENV=none php bin/console sylius:install:assets --no-debug \
 && APP_ENV=none php bin/console sylius:theme:assets:install public \
 && mkdir -p /var/www/public/media/image \
 && mkdir -p /var/www/public/media/cache

FROM scratch AS ci

COPY --from=ghcr.io/superbrave/php:8.3-pcov / /
COPY --from=application /var/www /var/www

WORKDIR /var/www

USER www-data

HEALTHCHECK --interval=10s --timeout=5s --start-period=5s --retries=3 CMD ["cgi-fcgi", "-bind", "-connect", "127.0.0.1:9000"]

ENTRYPOINT ["/usr/local/bin/docker-entrypoint"]

CMD ["/usr/local/sbin/php-fpm", "-c", "/usr/local/etc/php-fpm.conf"]

FROM ci AS dta_test

RUN APP_SECRET=secret APP_ENV=dta_test php bin/console cache:warmup \
 && APP_SECRET=secret APP_ENV=seeme_dta_test php bin/console cache:warmup \
 && rm -f /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

FROM ci AS acceptance

RUN APP_SECRET=secret APP_ENV=accept php bin/console cache:warmup \
 && APP_SECRET=secret APP_ENV=seeme_accept php bin/console cache:warmup \
 && rm -f /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

WORKDIR /tmp

USER root
RUN version=$(php -r "echo PHP_MAJOR_VERSION.PHP_MINOR_VERSION.(PHP_ZTS ? '-zts' : '');") \
 && architecture=$(uname -m) \
 && curl -A "Docker" -o /tmp/blackfire-probe.tar.gz -D - -L -s https://blackfire.io/api/v1/releases/probe/php/linux/$architecture/$version \
 && mkdir -p /tmp/blackfire \
 && tar zxpf /tmp/blackfire-probe.tar.gz -C /tmp/blackfire \
 && mv /tmp/blackfire/blackfire-*.so $(php -r "echo ini_get ('extension_dir');")/blackfire.so \
 && printf "extension=blackfire.so\nblackfire.agent_socket=tcp://127.0.0.1:8307\n" > /usr/local/etc/php/conf.d/blackfire.ini \
 && rm -rf /tmp/blackfire /tmp/blackfire-probe.tar.gz

RUN docker-php-source delete *

WORKDIR /var/www
USER www-data


FROM ci AS production

RUN APP_SECRET=secret APP_ENV=prod php bin/console cache:warmup \
 && APP_SECRET=secret APP_ENV=seeme_prod php bin/console cache:warmup \
 && rm -f /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
