{"dependencies": {"@github/relative-time-element": "^4.3.0", "@superbrave/communication-system-components": "^0.30.0", "axios": "^0.30.0", "babel-jest": "28.1.0", "babel-polyfill": "^6.26.0", "chart.js": "^3.7.1", "dayjs": "1.11.2", "eslint-plugin-vue": "^9.0.1", "jquery": "^3.5.0", "jquery.dirtyforms": "^2.0.0", "lightbox2": "^2.9.0", "semantic-ui-css": "^2.2.0", "slick-carousel": "^1.8.1"}, "devDependencies": {"@babel/core": "^7.14.0", "@babel/plugin-external-helpers": "^7.12.13", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-runtime": "^7.13.15", "@babel/preset-env": "^7.14.1", "@babel/register": "^7.13.16", "@symfony/webpack-encore": "^2.1.0", "@vue/compiler-sfc": "^3.2.6", "@vue/test-utils": "^2.0.0-rc.6", "dotenv-webpack": "^7.1.0", "eslint": "^8.16.0", "eslint-webpack-plugin": "^3.1.1", "gulp-sass": "^5.1.0", "html-webpack-plugin": "^5.3.1", "pretty-quick": "^3.1.1", "sass": "^1.58.3", "sass-loader": "^13.0.0", "webpack-notifier": "^1.6.0"}, "scripts": {"build": "encore production --progress", "dev:admin": "encore dev-server --hot --port=8100 --config-name=admin"}, "repository": {"type": "git", "url": "git+https://github.com/Sylius/Sylius.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "packageManager": "yarn@1.22.19+sha256.732620bac8b1690d507274f025f3c6cfdc3627a84d9642e38a07452cc00e0f2e"}