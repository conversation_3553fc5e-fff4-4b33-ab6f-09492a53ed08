{"name": "superbrave/checkout-service", "description": "The microlith service responsible for the checkout process.", "type": "project", "license": "proprietary", "require": {"php": "^8.3", "ext-bcmath": "*", "ext-dom": "*", "ext-json": "*", "bjeavons/zxcvbn-php": "^1.3", "cweagans/composer-patches": "^1.7", "defuse/php-encryption": "^2", "galbar/jsonpath": "^3.0", "hwi/oauth-bundle": "^2.1", "league/flysystem-aws-s3-v3": "^2.5", "mollie/mollie-api-php": "^2.63", "monolog/monolog": "^2.10", "nelmio/cors-bundle": "^2.2", "nijens/openapi-bundle": "^2.1", "oneup/flysystem-bundle": "^4.12", "php-http/guzzle7-adapter": "^1.1", "psr/simple-cache": "^1.0", "ramsey/uuid-doctrine": "^1.8", "scienta/doctrine-json-functions": "^5.5", "sentry/sentry-symfony": "^4.13", "smartystreets/phpsdk": "^5.2", "stripe/stripe-php": "^15.2", "superbrave/anamnesis-service-client": "^1.1", "superbrave/auth0-bundle": "^1.0", "superbrave/auth0-http-client": "^2.0.0", "superbrave/consult-system-client": "^3.0", "superbrave/messenger-outbox-bundle": "^1.0", "superbrave/openapi-security-bundle": "^1.0", "superbrave/pharmacy-service-client": "^1.15", "superbrave/verbose-error-http-client": "^2.1", "sylius/refund-plugin": "^1.5.0", "sylius/sylius": "^1.14", "symfony/amqp-messenger": "6.4.*", "symfony/dotenv": "^6.4", "symfony/flex": "^2.4", "symfony/http-client": "^6.4", "symfony/lock": "6.4.*", "symfony/rate-limiter": "6.4.*", "symfony/redis-messenger": "^6.4", "symfony/runtime": "^6.4", "symfony/security-bundle": "^6.4.13", "symfony/slack-notifier": "6.4.*", "symfony/validator": "^6.4.14", "symfony/webpack-encore-bundle": "^1.11"}, "require-dev": {"ext-pdo": "*", "behat/behat": "^3.11", "brianium/paratest": "*", "dama/doctrine-test-bundle": "^6.7", "dmarynicz/behat-parallel-extension": "^1.0", "doctrine/doctrine-fixtures-bundle": "^3.4", "friends-of-behat/mink": "^1.11", "friends-of-behat/mink-browserkit-driver": "^1.6", "friends-of-behat/mink-extension": "^2.7", "friends-of-behat/symfony-extension": "^2.3", "friendsofphp/php-cs-fixer": "^3.49", "helmich/phpunit-json-assert": "^3.4", "nimut/phpunit-merger": "^2.0", "payum/skeleton": "^1.7", "phpmd/phpmd": "^2.13", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.10", "phpstan/phpstan-doctrine": "^1.3", "phpstan/phpstan-phpunit": "^1.4", "phpstan/phpstan-symfony": "^1.2", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.6", "polishsymfonycommunity/symfony-mocker-container": "^1.0", "symfony/browser-kit": "^6.4", "symfony/css-selector": "^6.4", "symfony/debug-bundle": "^6.4", "symfony/dom-crawler": "6.4.*", "symfony/phpunit-bridge": "^6.4", "symfony/web-profiler-bundle": "^6.4", "webmozarts/console-parallelization": "*", "zenstruck/messenger-test": "^1.7"}, "prefer-stable": true, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}, "classmap": ["src/Kernel.php"]}, "scripts": {"auto-scripts": {"php -d memory_limit=-1 bin/console cache:clear --no-warmup --no-debug": "script", "php -d memory_limit=-1 bin/console assets:install %PUBLIC_DIR%": "script"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "post-create-project-cmd": ["@php bin/console sylius:inform-about-gus --ansi", "@php bin/console sylius:show-available-plugins --ansi"], "cache:clear": ["php -d memory_limit=-1 bin/console cache:clear --no-warmup --no-debug", "php -d memory_limit=-1 bin/console cache:pool:clear --all"], "cache:warmup": "php -d memory_limit=-1 bin/console cache:warmup  --no-debug", "check:code-style": "php -d memory_limit=-1 bin/php-cs-fixer fix --allow-risky=yes --dry-run -v", "check:fix-code-style": "php -d memory_limit=-1 bin/php-cs-fixer fix --allow-risky=yes", "check:phpstan": "php -d memory_limit=-1 ./bin/phpstan analyse", "check:phpmd": "php -d memory_limit=-1 ./bin/phpmd src ansi phpmd.ruleset.xml --baseline-file phpmd-baseline", "check": ["@check:fix-code-style", "@check:phpstan"], "test": "@test:phpunit", "test:phpunit": ["@db:test-prepare", "php -d memory_limit=-1 bin/simple-phpunit --testdox --no-coverage"], "test:parallel": ["php bin/paratest --runner=WrapperRunner --testsuite=unit --processes 10", "php bin/paratest --runner=WrapperRunner --testsuite=functional --processes 10", "php bin/behat --tags '~@non-mvp&&~failing' --parallel-feature 10"], "test:with-coverage": ["@db:test-prepare", "php bin/console doctrine:database:create --env=test --if-not-exists --no-debug && php bin/console doctrine:migrations:migrate --env=test --no-interaction --allow-no-migration --no-debug && php -d memory_limit=-1 bin/simple-phpunit --testdox --coverage-html=coverage"], "test:behat": ["@putenv APP_ENV=test", "php bin/behat --strict --no-snippets --tags '~@non-mvp'"], "fixtures": ["@shop-config-load", "php -d memory_limit=-1 bin/console sylius:fixtures:load checkout_service -n --no-debug"], "fixtures:test": "php bin/console parallel:database:prepare --processes=${PROCESS_COUNT:-10} --segment-size=1 --batch-size=1 --env=test", "shop-config-load": "php bin/console shop:configuration:load --no-debug", "load-catalog": "php bin/console catalog:load --no-debug", "db:drop": "php -d memory_limit=-1 bin/console doctrine:database:drop --force --no-debug --if-exists", "db:create": "php -d memory_limit=-1 bin/console doctrine:database:create --no-debug", "db:migrate": "php -d memory_limit=-1 bin/console doctrine:migrations:migrate --no-interaction --no-debug", "db:migrate-test": "php -d memory_limit=-1 bin/console --env=test doctrine:migrations:migrate --no-interaction --no-debug", "db:diff": "php -d memory_limit=-1 bin/console doctrine:migrations:diff", "db:fixtures": ["@db:drop", "@db:create", "php -d memory_limit=-1 bin/console app:database:import checkout_dev.sql --no-debug"], "db:fixtures-seeme": ["@putenv APP_ENV=seeme_dev", "@db:drop", "@db:create", "php -d memory_limit=-1 bin/console app:database:import checkout_seeme_dev.sql --no-debug"], "db:prepare": ["@db:drop", "@db:create", "@db:migrate", "@shop-config-load"], "db:test-prepare": ["@putenv APP_ENV=test", "@cache:clear", "@fixtures:test"], "db:schema-validate": "php bin/console doctrine:schema:validate --skip-sync --no-interaction --no-debug", "db:reload": ["@cache:clear", "@db:fixtures", "@db:migrate", "@shop-config-load"], "db:reload-seeme": ["@putenv APP_ENV=seeme_dev", "@cache:clear", "@db:fixtures-seeme", "@db:migrate", "@shop-config-load"], "cps": "@check:phpstan", "cpm": "@check:phpmd", "dd": "@db:drop", "dc": "@db:create", "dm": "@db:migrate", "mdiff": "@db:diff", "dv": "@db:schema-validate", "dr": "@db:reload", "drs": "@db:reload-seeme", "dp": "@db:prepare", "dtp": "@db:test-prepare"}, "config": {"platform": {"php": "8.3", "ext-exif": "8.3", "ext-gd": "8.3", "ext-sodium": "8.3"}, "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "php-http/discovery": false, "phpstan/extension-installer": true, "symfony/flex": true, "symfony/runtime": true, "symfony/thanks": true}, "bin-dir": "bin"}, "extra": {"symfony": {"allow-contrib": false, "docker": false, "require": "6.4.*"}, "patches": {"symfony/doctrine-messenger": {"REMOVE AFTER UPGRADING symfony/doctrine-messenger to ^7.1 - Avoid DELETE statement flood": "patches/doctrine-messenger-pr-54606.patch"}}}, "repositories": [{"type": "composer", "url": "https://repo.packagist.com/superbrave/"}, {"packagist.org": false}], "minimum-stability": "stable"}