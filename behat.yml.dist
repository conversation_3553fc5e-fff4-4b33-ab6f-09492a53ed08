default:
  suites:
    default:
      contexts:
        - App\Tests\Behat\AdminUserContext
        - App\Tests\Behat\Admin\Api\OrderContext
        - App\Tests\Behat\Admin\Api\OrderItemContext
        - App\Tests\Behat\Admin\Api\ShipmentContext
        - App\Tests\Behat\Admin\AuthenticationContext
        - App\Tests\Behat\Admin\BusinessUnitContext
        - App\Tests\Behat\Admin\CustomerContext
        - App\Tests\Behat\Admin\CustomerPoolContext
        - App\Tests\Behat\Admin\HtmlContext
        - App\Tests\Behat\Admin\ProductContext
        - App\Tests\Behat\Admin\RefundPaymentContext
        - App\Tests\Behat\Api\AuthenticationContext
        - App\Tests\Behat\Api\CartContext
        - App\Tests\Behat\Api\CatalogContext
        - App\Tests\Behat\Api\CheckoutContext
        - App\Tests\Behat\Api\CountriesContext
        - App\Tests\Behat\Api\CrossOriginResourceSharingContext
        - App\Tests\Behat\Api\CustomerContext
        - App\Tests\Behat\Api\EventQueueContext
        - App\Tests\Behat\Api\FeedContext
        - App\Tests\Behat\Api\MarketingSubscriptionContext
        - App\Tests\Behat\Api\OpenApiContext
        - App\Tests\Behat\Api\OrderContext
        - App\Tests\Behat\Api\PaymentContext
        - App\Tests\Behat\Api\PreprCatalogContext
        - App\Tests\Behat\Api\RateLimiterContext
        - App\Tests\Behat\Api\ResponseContext
        - App\Tests\Behat\Api\ShopContext
        - App\Tests\Behat\AuditLogContext
        - App\Tests\Behat\BusinessUnitContext
        - App\Tests\Behat\Catalog\Messenger\MessageHandlerContext
        - App\Tests\Behat\Catalog\ProductContext
        - App\Tests\Behat\Catalog\ProductVariantContext
        - App\Tests\Behat\ChannelContext
        - App\Tests\Behat\Console\ConsoleCommandContext
        - App\Tests\Behat\ConsultSystemContext
        - App\Tests\Behat\CouponContext
        - App\Tests\Behat\CustomerServiceContext
        - App\Tests\Behat\EmailMarketingContext
        - App\Tests\Behat\EnvironmentVariableContext
        - App\Tests\Behat\ExchangeRateContext
        - App\Tests\Behat\Feed\PreprContext
        - App\Tests\Behat\FraudCheckContext
        - App\Tests\Behat\OrderContext
        - App\Tests\Behat\OrderProcessingContext
        - App\Tests\Behat\PaymentContext
        - App\Tests\Behat\PaymentMethodContext
        - App\Tests\Behat\ProductContext
        - App\Tests\Behat\RefundPaymentContext
        - App\Tests\Behat\ResponseContext
        - App\Tests\Behat\ShipmentContext
        - App\Tests\Behat\SupplierContext
        - App\Tests\Behat\SupplierServiceContext
        - App\Tests\Behat\TaxonContext
        - App\Tests\Behat\UtilityContext

  extensions:
    FriendsOfBehat\SymfonyExtension:
      bootstrap: tests/bootstrap.php
    DAMA\DoctrineTestBundle\Behat\ServiceContainer\DoctrineExtension: ~
    DMarynicz\BehatParallelExtension\Extension:
      environments:
        - TEST_TOKEN: 1
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 2
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 3
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 4
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 5
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 6
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 7
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 8
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 9
          SYMFONY_DOTENV_VARS:
        - TEST_TOKEN: 10
          SYMFONY_DOTENV_VARS:
