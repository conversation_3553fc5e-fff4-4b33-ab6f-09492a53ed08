# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    env(BLUECLINIC_ENCRYPTION_KEY): ''
    env(MOLLIE_API_KEY): ''
    env(PARTNERIZE_CAMPAIGN_ID): 'disabled'
    env(PARTNERIZE_USER_API_KEY): ''
    env(PARTNERIZE_USER_APPLICATION_KEY): ''
    env(FRONTEND_MAIN2_APP_URL): ''
    env(FRONTEND_FOLLOW_UP_APP_URL): ''
    env(AWS_ACCESS_KEY_ID): ''
    env(AWS_SECRET_ACCESS_KEY): ''
    env(PUBLIC_STORAGE_BUCKET): ''
    env(SYLIUS_REFUND_LOGO_FILE): ''
    locale: en
    sylius_core.images_dir: '%sylius_core.public_dir%/media/image'
    file.openapi: 'config/openapi.yaml'
    shop_configuration_directory: '%env(string:SHOP_CONFIGURATION_DIRECTORY)%'
    sylius_channel_prefix: '%env(string:SYLIUS_CHANNEL_PREFIX)%'
    amazon.aws.s3.api_version: '2006-03-01'
    amazon.aws.s3.region: 'eu-central-1'
    amazon.aws.s3.key: '%env(AWS_ACCESS_KEY_ID)%'
    amazon.aws.s3.secret: '%env(AWS_SECRET_ACCESS_KEY)%'
    router.request_context.scheme: 'https'

imports:
    - { resource: "services/*" }
    - { resource: "services/catalog/*" }

services:
    # Default configuration for services in *this* file
    _defaults:
        # Automatically injects dependencies in your services
        autowire: true

        # Automatically registers your services as commands, event subscribers, etc.
        autoconfigure: true

        # Allows optimizing the container by removing unused services; this also means
        # fetching services directly from the container via $container->get() won't work
        public: false

        bind:
            $blueclinicEncryptionKey: '%env(string:BLUECLINIC_ENCRYPTION_KEY)%'
            $orderNumberGenerator: '@sylius.sequential_order_number_generator'
            $session: '@Symfony\Component\HttpFoundation\Session\SessionInterface'
            $readonlyConnection: '@doctrine.dbal.readonly_connection'
            $refundPaymentStateApplier: '@App\StateMachine\StateApplier\RefundPaymentStateApplierInterface'
            $kernelEnvironment: '%kernel.environment%'
            $canopyDeployWebhookClient: '@app.canopy_deploy.webhook_client'
            $partnerizeCampaignId: '%env(string:PARTNERIZE_CAMPAIGN_ID)%'
            $orderPaymentStateResolver: '@App\StateMachine\StateResolver\OrderPaymentStateResolver'
            $orderShippingStateResolver: '@App\StateMachine\StateResolver\OrderShippingStateResolver'
            $syliusChannelPrefix: '%sylius_channel_prefix%'
            $cartIsAbandonedAfterSeconds: '%env(int:CART_IS_ABANDONED_AFTER_SECONDS)%'
            $imageAttachmentProcessor: '@App\Catalog\Attachment\ImageProcessor'
            $leafletAttachmentProcessor: '@App\Catalog\Attachment\LeafletProcessor'

    _instanceof:
        Sylius\Bundle\ResourceBundle\Controller\ResourceController:
            autowire: false
        Sylius\Bundle\ResourceBundle\Form\Type\AbstractResourceType:
            autowire: false
        App\Serializer\CustomField\CustomFieldInterface:
            tags: [ 'app.serializer.custom_field' ]
        App\Api\Request\QueryBuilder\Provider\QueryBuilderProviderInterface:
            tags: [ 'app.request.factory.query_builder.provider' ]

    # Makes classes in src/ available to be used as services;
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude:
            - '../src/Api/'
            - '../src/Logging/Sentry/EventSubscriber/CommandTracingSubscriber.php'
            - '../src/Channel/'
            - '../src/Entity/'
            - '../src/Feed/' # configured in services/feed.yaml
            - '../src/Fixture/'
            - '../src/HttpClient/CommunicationMessage/Response/'
            - '../src/Kernel.php'
            - '../src/Locale/'
            - '../src/Migrations/'
            - '../src/Mime/'
            - '../src/OrderProcessing/'
            - '../src/Projection/'
            - '../src/ShopConfiguration/'
            - '../src/Logging/Sentry/SensitiveDataScrubber.php'
            - '../src/Security/Rbac'

    # Controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: [ 'controller.service_arguments' ]

    App\Admin\Controller\:
        resource: '../src/Admin/Controller'
        tags: [ 'controller.service_arguments' ]

    App\Admin\Grid\FieldTypes\UserDateTimeFieldType:
        decorates: 'sylius.grid_field.datetime'
        arguments:
            $dataExtractor: '@sylius.grid.data_extractor'

    App\Admin\Grid\Filter\UserDateTimeZoneFilter:
        decorates: 'sylius.grid_filter.date'
        arguments:
            $dateFilter: '@.inner'

    App\Security\Rbac\EventListener\PermissionCheckerListener:
        tags:
            - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }

    aws.application.public_storage.s3.client:
        class: Aws\S3\S3Client
        arguments:
            -   region: "%amazon.aws.s3.region%"
                version: "%amazon.aws.s3.api_version%"

    App\Catalog\Attachment\ImageProcessor:
        arguments:
            $filesystem: '@oneup_flysystem.images_and_leaflets_filesystem'
            $publicStorageBucket: '%env(PUBLIC_STORAGE_BUCKET)%'

    App\Catalog\Attachment\LeafletProcessor:
        arguments:
            $filesystem: '@oneup_flysystem.images_and_leaflets_filesystem'
            $publicStorageBucket: '%env(PUBLIC_STORAGE_BUCKET)%'

    Redis:
        class: Redis
        lazy: true
        calls:
            -   connect:
                    - '%env(REDIS_HOST)%'
                    - '%env(int:REDIS_PORT)%'

    Symfony\Component\HttpFoundation\Session\Storage\Handler\RedisSessionHandler:
        arguments:
            - '@Redis'

    Monolog\Formatter\JsonFormatter:
        public: true
        calls:
            -   method: includeStacktraces

    # Decorations on Sylius services

    Doctrine\Persistence\ObjectManager:
        alias: doctrine.orm.entity_manager

    App\Command\ShopConfigurationLoadCommand:
        arguments:
            - '@App\ShopConfiguration\ConfigurationLoaderInterface'
            - '%kernel.project_dir%/config/shop_configuration/%shop_configuration_directory%/'
        tags:
            - { name: 'console.command' }

    # Fraud checker service definitions
    App\FraudCheck\FraudCheckerInterface:
        alias: App\FraudCheck\CompositeFraudChecker
    App\StateMachine\Guard\:
        resource: '../src/StateMachine/Guard'
        public: true

    App\StateMachine\Callback\:
        resource: '../src/StateMachine/Callback'
        public: true

    App\StateMachine\StateResolver\:
        resource: '../src/StateMachine/StateResolver'
        public: true

    App\StateMachine\StateApplier\:
        resource: '../src/StateMachine/StateApplier'
        public: true

    App\Supplier\StateMachine\:
        resource: '../src/Supplier/StateMachine'
        public: true

    App\Catalog\Currency\CeilRoundingCurrencyConverter:
        decorates: 'sylius.currency_converter'
        arguments:
            - '@.inner'

    app.catalog.currency.cost_price_converter:
        alias: 'sylius.currency_converter'

    App\Admin\Form\Type\SupplierType:
        arguments:
            - '@doctrine.orm.entity_manager'
            - 'App\Entity\Supplier\Supplier'
            - [ 'sylius' ]
        tags:
            - { name: form.type }

    App\Admin\Form\Type\PaymentMethodChannelType:
        arguments:
            - '@doctrine.orm.entity_manager'
            - 'App\Entity\Payment\PaymentMethodChannel'
            - [ 'sylius' ]
        tags:
            - { name: form.type }

    App\Admin\Form\Type\RefundPaymentType:
        arguments:
            - 'App\Entity\Refund\RefundPayment'
            - [ 'sylius' ]
        tags:
            - { name: form.type }

    App\Admin\Form\Type\SupplierCountryShippingType:
        arguments:
            - 'App\Entity\Supplier\SupplierCountryShipping'
            - [ 'sylius' ]
        tags:
            - { name: form.type }

    App\Admin\Form\Type\SupplierDoctorType:
        arguments:
            - 'App\Entity\Supplier\SupplierDoctor'
            - [ 'sylius' ]
        tags:
            - { name: form.type }

    App\Promotion\Checker\Rule\CartQuantityItemTypeRuleChecker:
        tags:
            - {
                name: 'sylius.promotion_rule_checker',
                type: !php/const App\Promotion\Checker\Rule\CartQuantityItemTypeRuleChecker::TYPE,
                label: 'Has at least n items of type x',
                form_type: 'App\Admin\Form\Type\Rule\CartQuantityItemTypeConfigurationType'
            }

    App\Promotion\Checker\Rule\CartItemContainsAttributeRuleChecker:
        tags:
            - {
                name: sylius.promotion_rule_checker,
                type: !php/const App\Promotion\Checker\Rule\CartItemContainsAttributeRuleChecker::TYPE,
                form_type: 'App\Admin\Form\Type\Rule\CartItemContainsAttributeConfigurationType',
                label: 'Does not contain these attributes'
            }

    App\Promotion\Action\OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommand:
        arguments:
            - '@App\Promotion\Filter\ProductTypeFilter'
            - '@sylius.proportional_integer_distributor'
            - '@sylius.promotion.units_promotion_adjustments_applicator'
        tags:
            - {
                name: sylius.promotion_action,
                type: !php/const App\Promotion\Action\OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommand::TYPE,
                form_type: 'App\Promotion\Action\FormType\OrderFixedDiscountForEachItemAfterNthItemOfTypeConfigurationType',
                label: Item fixed discount after nth item
            }

    #####################################################################################################
    # DISCLAIMER                                                                                        #
    #                                                                                                   #
    # DO NOT REMOVE THIS DEPENDENCY.                                                                    #
    # Sylius core overwrites this listener, without it you won't receive any logs from api endpoints.   #
    #####################################################################################################
    App\EventListener\ErrorListener:
        decorates: 'exception_listener'
        arguments:
            - '@.inner'
            - '%kernel.error_controller%'
            - '@logger'
            - '%kernel.debug%'
        tags:
            - { name: 'kernel.event_subscriber' }
            - { name: 'monolog.logger', channel: 'request' }

    Superbrave\PharmacyServiceClient\ClientInterface: '@Superbrave\PharmacyServiceClient\Client'

    Superbrave\PharmacyServiceClient\Client:
        arguments:
            - '@supplier_service.auth0_client'
            - '%env(string:SUPPLIER_SERVICE_API_BASE_URI)%'

    Superbrave\AnamnesisServiceClient\ClientInterface: '@Superbrave\AnamnesisServiceClient\Client'

    Superbrave\AnamnesisServiceClient\Client:
        arguments:
            - '@anamnesis_service.auth0_client'
            - '%env(string:ANAMNESIS_SERVICE_API_BASE_URI)%'

    SuperBrave\ConsultSystemClient\ClientInterface: '@SuperBrave\ConsultSystemClient\Client'

    SuperBrave\ConsultSystemClient\Client:
        arguments:
            - '@consult_system.auth0_client'

    App\CommunicationSystem\Client\Client:
        arguments:
            $httpClient: '@communication_system.auth0_client'

    App\Security\UserProvider\JwtAdminUserProvider:
        decorates: 'superbrave_auth0.security.user_provider'
        arguments:
            $inner: '@.inner'

    Sylius\Calendar\Provider\DateTimeProviderInterface:
        class: Sylius\Calendar\Provider\Calendar

    Sylius\Component\Core\Factory\CartItemFactoryInterface: '@sylius.factory.order_item'

    Sylius\RefundPlugin\Factory\CreditMemoFactoryInterface:
        alias: Sylius\RefundPlugin\Factory\CreditMemoFactory

    App\StateMachine\StateApplier\RefundPaymentStateApplierInterface:
        alias: App\StateMachine\StateApplier\RefundPaymentStateApplier

    app_order_processing.order_payment_processor.checkout:
        class: App\OrderProcessing\OrderPaymentProcessor
        decorates: sylius.order_processing.order_payment_processor.checkout

    app_order_processing.order_payment_processor.after_checkout:
        class: App\OrderProcessing\OrderPaymentProcessor
        decorates: sylius.order_processing.order_payment_processor.after_checkout
        arguments:
            $targetState: 'new'

    App\OrderProcessing\OrderShipmentProcessor:
        decorates: sylius.order_processing.order_shipment_processor

    App\Serializer\ObjectNormalizer:
        arguments:
            $normalizer: '@serializer.normalizer.object'
            $customFields: !tagged_iterator app.serializer.custom_field

    App\Serializer\ProductOptionValueTranslationNormalizer:
        arguments:
            $normalizer: '@serializer.normalizer.object'

    App\Serializer\CustomField\:
        resource: '../src/Serializer/CustomField'
        bind:
            $channelContext: '@sylius.context.channel.request_based'

    App\Serializer\CustomField\Country\Name:
        arguments:
            $localeContext: '@app.locale.request_language'

    App\Serializer\Webhook\OpenApiWebhookPayloadSerializer:
        arguments:
            $openApiFile: '%kernel.project_dir%/%file.openapi%'

    App\Address\CustomerOrderAddressesSaver:
        decorates: sylius.customer_order_addresses_saver

    App\Doctrine\EventListener\CustomerCreated:
        arguments:
            $channelContext: '@sylius.context.channel'
            $localeContext: '@sylius.context.locale'
        tags:
            -   name: 'doctrine.orm.entity_listener'
                event: 'prePersist'
                entity: 'App\Entity\Customer\Customer'

    # Replaces (disables) Sylius\RefundPlugin\StateResolver\OrderPartiallyRefundedStateResolver with an empty class.
    Sylius\RefundPlugin\StateResolver\OrderPartiallyRefundedStateResolverInterface:
        alias: App\StateMachine\StateResolver\Sylius\OrderPartiallyRefundedStateResolver

    # Replaces (disables) Sylius\RefundPlugin\StateResolver\OrderFullyRefundedStateResolver with an empty class.
    Sylius\RefundPlugin\StateResolver\OrderFullyRefundedStateResolverInterface:
        alias: App\StateMachine\StateResolver\Sylius\OrderFullyRefundedStateResolver

    App\Api\Request\QueryBuilder\Provider\:
        resource: '../src/Api/Request/QueryBuilder/Provider'

    App\Api\Request\QueryBuilder\QueryBuilderFactory:
        arguments:
            $providers: !tagged_iterator app.request.factory.query_builder.provider

    App\CustomerService\HttpClient\CommunicationClientInterface: '@App\CustomerService\HttpClient\CommunicationClient'

    App\CustomerService\HttpClient\CommunicationClient:
        arguments:
            $httpClient: '@dokteronline.auth0_client'

    App\Prescription\HttpClient\LegacyPrescriptionClient:
        arguments:
            $httpClient: '@dokteronline.auth0_client'

    Sylius\RefundPlugin\Checker\OrderRefundingAvailabilityChecker:
        class: App\Refund\Checker\OrderRefundingAvailabilityChecker

    Sylius\RefundPlugin\Checker\OrderRefundsListAvailabilityChecker:
        class: App\Refund\Checker\OrderRefundsListAvailabilityChecker

    sylius.promotion.eligibility_checker.promotion_coupon_per_customer_usage_limit:
        class: App\Promotion\Checker\Eligibility\PromotionCouponPerCustomerUsageLimitEligibilityChecker
        tags:
            - { name: sylius.promotion_coupon_eligibility_checker }
    address.comparator.without_company:
        class: App\Address\AddressComparator

    sylius.customer_unique_address_adder:
        class: Sylius\Component\Core\Customer\CustomerUniqueAddressAdder
        arguments:
            $addressComparator: '@address.comparator.without_company'

    App\Command\Debug\CountOrderSyncCommand:
        arguments:
            $databaseHost: '%env(resolve:DATABASE_HOST)%'
            $databaseName: '%env(resolve:DATABASE_NAME)%'

    # Replaces \Sylius\Component\Promotion\Checker\Eligibility\PromotionDurationEligibilityChecker with our own implementation:
    sylius.promotion_duration_eligibility_checker:
        class: App\Promotion\Checker\Eligibility\PromotionDurationEligibilityChecker
        tags:
            - { name: sylius.promotion_eligibility_checker }

    # Replaces \Sylius\Component\Promotion\Checker\Eligibility\PromotionCouponDurationEligibilityChecker with our own implementation:
    sylius.promotion_coupon_duration_eligibility_checker:
        class: App\Promotion\Checker\Eligibility\PromotionCouponDurationEligibilityChecker
        tags:
            - { name: sylius.promotion_coupon_eligibility_checker }

    sylius.active_promotions_provider:
        class: App\Promotion\Promotion\ActivePromotionsByOrderProvider

    sylius.promotion_coupon_usage_limit_eligibility_checker:
        class: App\Promotion\Checker\Eligibility\PromotionCouponUsageLimitEligibilityChecker
        tags:
            - { name: sylius.promotion_coupon_eligibility_checker }

    sylius.promotion_usage_limit_eligibility_checker:
        class: App\Promotion\Checker\Eligibility\PromotionUsageLimitEligibilityChecker
        tags:
            - { name: sylius.promotion_eligibility_checker }

    App\ConsultSystem\CreateConsultRequestBuilder:
        arguments:
            $anamnesisServiceApiUrl: '%env(resolve:ANAMNESIS_SERVICE_API_BASE_URI)%'

    # Replaces \Sylius\Component\Core\StateResolver\OrderShippingStateResolver with our own implenntation because our
    # partially_shipped business logic is made to be diffirent from Sylius now.
    sylius.state_resolver.order_shipping:
        class: App\StateMachine\StateResolver\OrderShippingStateResolver
        public: true

    App\Admin\Controller\Order\UpdateOrderItemsController:
        arguments:
            $paymentExpiresInSeconds: '%reshipment.payment.expires_in_seconds%'

    App\Serializer\Prepr\TaxonList:
        arguments:
            - '@app.locale.request_language'
        tags:
            - { name: 'serializer.normalizer', priority: 1 } # Priority should at least be 1 higher than App\Serializer\ObjectNormalizer.

    App\Serializer\Prepr\ProductList:
        arguments:
            $localeContext: '@app.locale.request_language'
        tags:
            - { name: 'serializer.normalizer', priority: 1 } # Priority should at least be 1 higher than App\Serializer\ObjectNormalizer.

    App\Promotion\Action\PercentageDiscountWithExcludedProductsPromotionActionCommand:
        arguments:
            - '@sylius.factory.adjustment'
            - '@App\Promotion\Promotion\Filter\ExcludedProductsFilter'
        tags:
            -   name: sylius.promotion_action
                type: !php/const App\Promotion\Action\PercentageDiscountWithExcludedProductsPromotionActionCommand::TYPE
                form_type: App\Promotion\Action\FormType\PercentageWithExcludedProductsDiscountConfigurationType
                label: Order percentage discount with excluded products

    App\Promotion\Action\FixedDiscountWithExcludedProductsPromotionActionCommand:
        arguments:
            - '@sylius.proportional_integer_distributor'
            - '@sylius.promotion.units_promotion_adjustments_applicator'
            - '@App\Promotion\Promotion\Filter\ExcludedProductsFilter'
        tags:
            -   name: sylius.promotion_action
                type: !php/const App\Promotion\Action\FixedDiscountWithExcludedProductsPromotionActionCommand::TYPE
                form_type: App\Promotion\Action\FormType\FixedWithExcludedProductsDiscountConfigurationType
                label: Order fixed discount with excluded products

    App\Payum\MessageHandler\NotifyHandler:
        arguments:
            $payumGatewayRegistry: '@payum'

    #    Todo: remove definition when upgrading doctrine package in https://mv-jira-1.atlassian.net/browse/DV-6722 where this https://github.com/doctrine/DoctrineBundle/pull/1624/files is available
    Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver:
        class: 'Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver'
        arguments:
            $registry: '@doctrine'
            $expressionLanguage: '@doctrine.orm.entity_value_resolver.expression_language'
        tags:
            - { name: 'controller.argument_value_resolver' }

    Payum\Bundle\PayumBundle\Controller\NotifyController:
        class: App\Payum\Controller\NotifyController

    app.catalog.generator.incrementing_slug_generator.product_translation:
        class: App\Catalog\Generator\IncrementingSlugGenerator
        arguments:
            $translationRepository: '@App\Repository\ProductTranslationRepository'

    app.catalog.generator.incrementing_slug_generator.taxon_translation:
        class: App\Catalog\Generator\IncrementingSlugGenerator
        arguments:
            $translationRepository: '@App\Repository\TaxonTranslationRepository'

    App\Catalog\Processor\Product\ProductTranslationProcessor:
        arguments:
            $slugGenerator: '@app.catalog.generator.incrementing_slug_generator.product_translation'

    App\Catalog\MessageHandler\UpsertTaxonHandler:
        arguments:
            $slugGenerator: '@app.catalog.generator.incrementing_slug_generator.taxon_translation'

    payum.token_factory_builder:
        class: App\Payum\TokenFactory\LoadBalancingGatewayTokenFactoryBuilder

    App\Payum\TokenFactory\LoadBalancingGatewayTokenFactory:
        alias: Payum\Core\Security\TokenFactoryInterface

    Payum\Core\Security\CypherInterface: '@payum.dynamic_gateways.cypher'

    messenger.failure.add_error_details_stamp_listener:
        class: App\Messenger\EventListener\AddErrorDetailsStampListener

    App\Admin\Form\Type\BusinessUnitType:
        arguments:
            - App\Entity\BusinessUnit\BusinessUnit
            - [ 'sylius' ]
        tags:
            - { name: form.type }

    App\Admin\Form\Type\BusinessUnitAddressType:
        arguments:
            $dataClass: App\Entity\BusinessUnit\BusinessUnitAddress
        tags:
            - { name: form.type }

    App\Admin\Form\Type\BusinessUnitChoiceType:
        arguments:
            $businessUnitRepository: '@App\Repository\BusinessUnitRepository'
        tags:
            - { name: form.name }
