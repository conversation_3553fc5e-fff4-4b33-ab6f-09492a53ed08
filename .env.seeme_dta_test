APP_DEBUG=0
APP_ENV=seeme_dta_test

# See \App\Tests\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^http(s?):\/\/((deploy-preview-([0-9].*)--seemenopause|test--anamnesis-admin-see-me)\.netlify\.app|consult\.sbtest\.nl|seemenopause(\.dev\.loc|-mvp\.bitpuma\.nl)|seeme-nopause-pentest.bitpuma.nl)$'

DATABASE_HOST=menopause-checkout-service-test.c04pyyj3cfqi.eu-central-1.rds.amazonaws.com
DATABASE_NAME=checkout-service
DATABASE_HOST_READONLY=menopause-checkout-service-test.c04pyyj3cfqi.eu-central-1.rds.amazonaws.com
DATABASE_NAME_READONLY=checkout-service

# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN_APP_URL=https://seemenopause-mvp.bitpuma.nl

JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/seeme_dta_test/public.pem
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/seeme_dta_test/private.pem

AUTH0_DOMAIN=test-ehvg.eu.auth0.com
AUTH0_AUDIENCE='["api://commerce-system.see-me"]'

SUPPLIER_SERVICE_API_BASE_URI=https://pharmacies.sbtest.nl/api/
SUPPLIER_SERVICE_AUTH0_AUDIENCE=api://supplier-service

ANAMNESIS_SERVICE_API_BASE_URI=https://seemenopause.anamnesis-service.sbtest.nl/api/
ANAMNESIS_SERVICE_AUTH0_AUDIENCE=api://anamnesis-system.see-me

CONSULT_SYSTEM_ENABLED=1
CONSULT_SYSTEM_API_BASE_URI=https://consult.sbtest.nl/api/
CONSULT_SYSTEM_AUTH0_AUDIENCE=api://consult-system

COMMUNICATION_SYSTEM_API_BASE_URI=https://communication.sbtest.nl/api/

CHECKOUT_SERVICE_API_HOST=seemenopause.commerce.sbtest.nl
DOKTERONLINE_API_BASE_URI=https://dokteronline.sbaccept.nl
FIXER_IO_API_BASE_URI=http://data.fixer.io/api
MESSENGER_TRANSPORT_DSN=doctrine://default
REDIS_HOST=checkout-service-test.guhda2.ng.0001.euc1.cache.amazonaws.com
SHOP_CONFIGURATION_DIRECTORY=seeme
SYLIUS_CHANNEL_PREFIX=seeme
SYMFONY_DEPRECATIONS_HELPER=999999

PUBLIC_STORAGE_BUCKET=commerce.seemenopause.public.s3.sbtest.nl

PIM_KATANA_API_BASE_URI=https://dokteronline-acc.katanapim.com/api/
PIM_KATANA_BASE_URI=https://dokteronline-acc.katanapim.com/

AUTH0_COMMERCE_SYSTEM_RWA_AUDIENCE=internal://commerce-system.seeme
AUTH0_COMMERCE_SYSTEM_RWA_CLIENT_ORGANIZATION=org_eo3G7Gx5lAEItEtd

RABBITMQ_HOST=amqps://b-611c892a-29de-45b0-a7c0-d7947e866718.mq.eu-central-1.amazonaws.com
RABBITMQ_PORT=5671

BUSINESS_UNIT_SEEME_API_HOSTNAME=seemenopause.commerce.sbtest.nl

# CanopyDeploy Webhook URL's SeeMe-NoPause
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_ORDER=161-test_webhook_dev_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_CUSTOMER=160-test_webhook_dev_01_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_PASSWORD_RESET=162-test_webhook_dev_02_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_REMOVE_ORDER_ITEM=163-test_webhook_dev_03_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_PRODUCT_BACK_IN_STOCK=

AUTO_CAPTURE_DELAY_PERIOD="15 minutes"
