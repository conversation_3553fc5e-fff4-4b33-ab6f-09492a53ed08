APP_DEBUG=0
APP_ENV=seeme_prod

# See \App\Tests\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/((www\.)?(anamnesis.)?seemenopause|consult\.ehealthventuresgroup)\.com$'

DATABASE_HOST=menopause-checkout-service-prod.ch4vx9h5s7nf.eu-central-1.rds.amazonaws.com
DATABASE_NAME=checkout-service
DATABASE_HOST_READONLY=menopause-checkout-service-ro-prod.ch4vx9h5s7nf.eu-central-1.rds.amazonaws.com
DATABASE_NAME_READONLY=checkout-service

# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN_APP_URL=https://seemenopause.com

JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/seeme_prod/public.pem
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/seeme_prod/private.pem

AUTH0_DOMAIN=auth.ehealthventuresgroup.com
AUTH0_AUDIENCE='["api://commerce-system.see-me"]'

SUPPLIER_SERVICE_API_BASE_URI=https://pharmacies.dokteronline.com/api/
SUPPLIER_SERVICE_AUTH0_AUDIENCE=api://supplier-service

ANAMNESIS_SERVICE_API_BASE_URI=https://anamnesis-service.seemenopause.com/api/
ANAMNESIS_SERVICE_AUTH0_AUDIENCE=api://anamnesis-system.see-me

CONSULT_SYSTEM_ENABLED=1
CONSULT_SYSTEM_API_BASE_URI=https://consult.ehealthventuresgroup.com/api/
CONSULT_SYSTEM_AUTH0_AUDIENCE=api://consult-system

COMMUNICATION_SYSTEM_API_BASE_URI=https://communication.ehealthventuresgroup.com/api/

CHECKOUT_SERVICE_API_HOST=commerce.seemenopause.com
DOKTERONLINE_API_BASE_URI=https://api.dokteronline.com
FIXER_IO_API_BASE_URI=http://data.fixer.io/api
MESSENGER_TRANSPORT_DSN=doctrine://default
REDIS_HOST=checkout-service-prod.0wk0ka.ng.0001.euc1.cache.amazonaws.com
SHOP_CONFIGURATION_DIRECTORY=seeme
SYLIUS_CHANNEL_PREFIX=seeme
SYMFONY_DEPRECATIONS_HELPER=999999

PUBLIC_STORAGE_BUCKET=cdn.commerce.seemenopause.com

AUTH0_COMMERCE_SYSTEM_RWA_AUDIENCE=internal://commerce-system.seeme
AUTH0_COMMERCE_SYSTEM_RWA_CLIENT_ORGANIZATION=org_RBlBP4vktiR05xGq

RABBITMQ_HOST=amqps://b-3be24377-b922-4caa-b780-c336405774b0.mq.eu-central-1.amazonaws.com
RABBITMQ_PORT=5671

BUSINESS_UNIT_SEEME_API_HOSTNAME=commerce.seemenopause.com

# CanopyDeploy Webhook URL's SeeMe-NoPause
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_ORDER=171-backend_order
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_CUSTOMER=128-backend_customer
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_PASSWORD_RESET=172-backend_password_reset
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_REMOVE_ORDER_ITEM=170-backend_remove_orderitems
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_PRODUCT_BACK_IN_STOCK=
