{% macro headers(grid, definition, requestAttributes) %}
    {% import '@SyliusUi/Macro/sorting.html.twig' as sorting %}

    {% if definition.actionGroups.bulk is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('bulk'), true)|length > 0 %}
        <th class="center aligned">
            <input data-js-bulk-checkboxes=".bulk-select-checkbox" data-js-bulk-buttons=".sylius-grid-nav__bulk" type="checkbox">
        </th>
    {% endif %}

    {% for field in definition.fields|sort_by('position') %}
        {% if field.enabled %}
            {% if field.isSortable %}
                {{ sorting.tableHeader(grid, field, requestAttributes) }}
            {% else %}
                <th class="sylius-table-column-{{ field.name }}">{{ field.label|trans }}</th>
            {% endif %}
        {% endif %}
    {% endfor %}
    {% if (definition.actionGroups.item is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('item'))|length > 0) or
        (definition.actionGroups.subitem is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('subitem'))|length > 0)
    %}
        <th class="sylius-table-column-actions">{{ 'sylius.ui.actions'|trans }}</th>
    {% endif %}
{% endmacro %}

{% macro row(grid, definition, row) %}
    <tr class="item">
    {% if definition.actionGroups.bulk is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('bulk'), true)|length > 0 %}
        <td><input class="bulk-select-checkbox" type="checkbox" value="{{ row.id }}" /></td>
    {% endif %}
    {% for field in definition.enabledFields|sort_by('position') %}
        <td>{{ sylius_grid_render_field(grid, field, row) }}</td>
    {% endfor %}
    {% if (definition.actionGroups.item is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('item'))|length > 0) or
        (definition.actionGroups.subitem is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('subitem'))|length > 0)
    %}
        <td>
            <div class="ui buttons">
                {% for action in sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('item'))|sort_by('position') %}
                    {{ sylius_grid_render_action(grid, action, row) }}
                {% endfor %}
            </div>
            {% if definition.actionGroups.subitem is defined and sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('subitem'))|length > 0 %}
            <div class="ui divider"></div>
            <div class="ui buttons">
                {% for action in sylius_rbac_get_available_grid_actions(grid, definition.getEnabledActions('subitem'))|sort_by('position') %}
                    {{ sylius_grid_render_action(grid, action, row) }}
                {% endfor %}
            </div>
            {% endif %}
        </td>
    {% endif %}
    </tr>
{% endmacro %}
