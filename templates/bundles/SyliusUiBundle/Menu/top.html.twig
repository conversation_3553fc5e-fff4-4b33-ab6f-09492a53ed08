{% extends '@!SyliusUi/Menu/top.html.twig' %}

{% block items %}
    <div class="ui buttons" style="display: block">
        {% for item in item.children %}
            {{ block('item') }}
        {% endfor %}
    </div>
{% endblock %}

{% block item %}
    {% set type = item.attribute('type') %}
    {% if 'edit' == type and buttons|default(false) %}
        {{ buttons.edit(item.uri, item.label) }}
    {% elseif 'show' == type and buttons|default(false) %}
        {{ buttons.show(item.uri, item.label) }}
    {% elseif 'delete' == type and buttons|default(false) %}
        {{ buttons.delete(item.uri, item.label|trans, true, item.attribute('resource_id')) }}
    {% elseif 'transition' == type %}
        {{ block('transition') }}
    {% elseif 'cancelOrder' == type %}
        {{ block('cancelOrder') }}
    {% elseif 'switchDoctor' == type %}
        {{ block('switchDoctor') }}
    {% elseif 'communicationStatus' == type %}
        {% if "ROLE_READ_TICKETS" not in app.token.roleNames %}
            {{ block('communicationStatus') }}
        {% endif %}
    {% elseif 'switchSupplier' == type %}
        {{ block('switchSupplier') }}
    {% elseif 'refundOrderItems' == type %}
        {{ block('refundOrderItems') }}
    {% elseif 'links' == type %}
        {{ block('links') }}
    {% else %}
        {{ block('link') }}
    {% endif %}
{% endblock %}

{% block communicationStatus %}
    {% set icon = item.labelAttribute('icon') %}
    {% set communicationStatus = item.extras.communicationStatus %}

    {% if communicationStatus.communication.links.view.href is not empty %}
        {% set color = 'orange' %}
    {% elseif communicationStatus.communication is null and not communicationStatus.hasError %}
        {% set color = 'gray' %}
    {% elseif communicationStatus.communication is null and communicationStatus.hasError %}
        {% set color = 'red' %}
    {% endif %}

    <div class="ui {% if icon %} labeled icon{% endif %} floated simple dropdown {% if color %}{{ color }}{% endif %} button">
        {% if icon %}<i class="{{ icon }} icon"></i>{% endif %}
        <span class="text">{{ item.label|trans }}</span>
        <div class="menu" id="cscOrderStatus">
            <div class="item">
                {% if communicationStatus.communication.links.view.href is not empty %}
                    <a href="{{ communicationStatus.communication.links.view.href }}" target="_blank">New message</a>
                {% elseif communicationStatus.communication is null and not communicationStatus.hasError %}
                    <span>No new messages</span>
                {% elseif communicationStatus.communication is null and communicationStatus.hasError %}
                    <span>Error: No connection to CSC</span>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

{% block refundOrderItems %}
    {% set icon = item.labelAttribute('icon') %}
    {% set order = item.extras.order %}
    {% set color = 'orange' %}

    <div class="ui button {% if color %}{{ color }}{% endif %}"
         onclick="$('#refund-order-items-modal').modal('show');">
        {% if icon %}<i class="{{ icon }} icon"></i>{% endif %}{{ item.label|trans }}
    </div>

    <div id="refund-order-items-modal" class="ui modal">
        <i class="close icon"></i>
        <div class="header">
            {{ item.label|trans }}
        </div>

        <div class="content">
            {% set form = order_lines_refund_form(order) %}
            {{ form_start(form) }}

            <div class="ui grid form" id="refund-order-items" data-id={{order.id}} style="margin-bottom: 1rem;">
                <div class="three column row" style="padding: 0.5rem 0">
                    <div class="column">
                        <h5 class="ui header">{{ 'sylius.ui.refund_order_item.items'|trans }}</h5>
                    </div>
                    <div class="column">
                        <h5 class="ui header">{{ 'sylius.ui.refund_order_item.shipment'|trans }}</h5>
                    </div>
                    <div class="column">
                        <h5 class="ui header">{{ 'sylius.ui.refund_order_item.reason'|trans }}</h5>
                    </div>
                </div>
                {% for orderLine in form.orderLines.children %}
                {% set shipment = orderLine.vars.data.orderItem.getShipmentFromUnits() %}

                <div class="three column row" style="padding: 0.5rem 0">
                    <div class="column middle aligned">
                        <div class="ui checkbox">
                            {{ form_widget(orderLine.refundOrderItem, { 'attr': { 'required': true }}) }}
                            {% if sylius_rbac_has_permission("app_admin_order_product_names") %}
                                {{ form_label(orderLine.refundOrderItem) }}
                            {% else %}
                                <label for="{{ orderLine.refundOrderItem.vars.id }}">Product {{orderLine.refundOrderItem.vars.value}} (Hidden)</label>
                            {% endif %}
                        </div>
                    </div>
                    <div class="column middle aligned">
                        <div class="ui grid">
                            <div class="two column row">
                                <div class="four wide column">#{{ shipment.getId }}</div>
                                <div class="twelve wide column">{% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'sylius_shipment', 'state': shipment.getState} %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="column">
                        {{ form_widget(orderLine.reason, { 'attr': { 'class': 'ui dropdown' }}) }}
                    </div>
                </div>
                {% endfor %}
            </div>
            <button type="submit" class="ui button red">Confirm</button>
            {{ form_end(form) }}
        </div>
    </div>
{% endblock %}

{% block switchSupplier %}
    {% set icon = item.labelAttribute('icon') %}
    {# Is used when multipleShipments are not allowed on the channel #}
    {% set alternativeSuppliers = item.extras.alternativeSuppliers %}
    {% set order = item.extras.order %}

    {% set switchAllowed = item.extras.switchAllowed %}
    {% if order.channel.multipleShipmentsAllowed == false %}
        {% set switchAllowed = alternativeSuppliers|length > 0 and switchAllowed %}
    {% endif %}

    {% set color = switchAllowed ? 'blue' : 'grey' %}

    <div
        class="ui button {% if color %}{{ color}}{% endif %} {% if switchAllowed == false %} dropdown{% endif %}"
        {% if switchAllowed %} onclick="$('#change-supplier-modal').modal('show');"{% endif %}
    >
        {% if icon %}<i class="{{ icon }} icon"></i>{% endif %}{{ item.label|trans }}
        {% if switchAllowed == false %}
            <div class="menu">
                <div class="item">
                    {{ 'sylius.ui.supplier_switch_unavailable'|trans }}
                </div>
            </div>
        {% endif %}
    </div>

    <div id="change-supplier-modal" class="ui modal">
        <i class="close icon"></i>
        <div class="header">
            {{ item.label|trans }}
        </div>

        <div class="content">
            {% if order.channel.multipleShipmentsAllowed %}
                {% set form = order_items_switch_supplier_form(order) %}
            {% else %}
                {% set form = order_switch_supplier_form(order) %}
            {% endif %}

            {{ form_start(form) }}

            {% if order.channel.multipleShipmentsAllowed %}
                <span style="margin-bottom: 1rem; display: block; font-size: 1.2em">
                    <b>{{ 'sylius.ui.choose_order_lines'|trans }}</b>
                </span>

                <div class="ui form" id="order-items-checkboxes" data-id={{order.id}} style="margin-bottom: 1rem;">
                    {% for checkbox in form.orderItems.children %}
                        <div class="field">
                            <div class="ui checkbox">
                                {{ form_widget(checkbox, { 'attr': {'required': true}}) }}
                                {% if sylius_rbac_has_permission("app_admin_order_product_names") %}
                                    {{ form_label(checkbox) }}
                                {% else %}
                                    <label for="{{ checkbox.vars.id }}">Product {{checkbox.vars.value}} (Hidden)</label>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="ui form" style="margin-bottom: 1rem;">
                <div class="field">
                    <div class="ui negative message hidden" id="no_suppliers_found_error">
                        <p>{{ 'sylius.ui.no_suppliers_found'|trans }}</p>
                    </div>
                    <label>{{ form_label(form.supplier) }}*</label>
                    {{ form_widget(form.supplier, { 'attr': { 'class': 'ui dropdown'}}) }}
                </div>
            </div>

            <div class="ui form" style="margin-bottom: 1rem;">
                <div class="field">
                    <label>{{ form_label(form.note) }}*</label>
                    {{ form_widget(form.note) }}
                </div>
            </div>

            <button type="submit" class="ui button red">{{ 'sylius.ui.confirm'|trans }}</button>

            {{ form_end(form) }}
        </div>
    </div>
{% endblock %}

{% block switchDoctor %}
    {% set icon = item.labelAttribute('icon') %}
    {% set alternativeDoctors = item.extras.alternativeDoctors %}
    {% set order = item.extras.order %}
    {% set switchAllowed = item.extras.switchAllowed %}
    {% set color = alternativeDoctors|length > 0 and switchAllowed ? 'purple' : 'grey' %}

    <div class="ui {% if icon %} labeled icon{% endif %} floated dropdown {% if color %}{{ color }}{% endif %} button">
        {% if icon %}<i class="{{ icon }} icon"></i>{% endif %}
        <span class="title">{{ item.label|trans }}</span>
        <div class="menu" style="min-width: 300px;" id="switchDoctor">
            <div class="item">
                {% if alternativeDoctors|length > 0 and switchAllowed %}
                    {% set form = order_switch_doctor_form(order) %}
                    {{ form_start(form) }}
                    <div class="ui form" style="margin-bottom: 1rem;">
                        <div class="field">
                            <label>{{ form_label(form.doctor) }}*</label>
                            {{ form_widget(form.doctor) }}
                        </div>
                    </div>
                    <button type="submit" class="ui button red">{{ 'sylius.ui.confirm'|trans }}</button>
                    {{ form_end(form) }}
                {% else %}
                    {{ 'sylius.ui.doctor_switch_unavailable'|trans }}
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

{% block cancelOrder %}
    {% set color = item.labelAttribute('color') %}
    {% set icon = item.labelAttribute('icon') %}

    <div class="ui {% if icon %} labeled icon{% endif %} floated dropdown {% if color %}{{ color }} {% endif %} button">
        {% if icon %}<i class="{{ icon }} icon"></i>{% endif %}
        <span class="text">{{ item.label|trans }}</span>
        <div class="menu" style="min-width: 300px;" id="cancelOrder">
            <div class="item">
                {% set form = cancel_order_form() %}
                {{ form_start(form) }}
                <div class="ui form" style="margin-bottom: 1rem;">
                    <div class="field">
                        <label>{{ form_label(form.reason) }}</label>
                        {{ form_widget(form.reason) }}
                    </div>
                </div>
                <button type="submit" class="ui button red">Cancel order</button>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}
