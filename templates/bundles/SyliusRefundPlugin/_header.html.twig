{% import '@SyliusUi/Macro/flags.html.twig' as flags %}

<div class="ui stackable two column grid">
    <div class="ten wide column">
        <h1 class="ui header">
            <i class="circular cart icon"></i>
            <div class="content">
                {{ 'sylius.ui.order'|trans }} #{{ order.number }} - {{ 'sylius_refund.ui.refunds'|trans }}
                <div class="sub header">
                    <div class="ui horizontal divided list">
                        <div class="item">
                            {{ order.checkoutCompletedAt|format_datetime }}
                        </div>
                        <div class="item" id="sylius-order-state">
                            {% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'sylius_order', 'state': order.state} %}
                        </div>
                        <div class="item" id="sylius-order-currency">
                            {{ order.currencyCode }}
                        </div>
                        <div class="item">
                            {{ flags.fromLocaleCode(order.localeCode) }}{{ order.localeCode|locale_name }}
                        </div>
                        <div class="item">
                            {{ 'sylius.ui.purchased_from'|trans }}
                            <span class="ui large empty horizontal circular label" style="background-color: {{ order.channel.color }}"></span> <strong>{{ order.channel }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </h1>
    </div>
</div>
