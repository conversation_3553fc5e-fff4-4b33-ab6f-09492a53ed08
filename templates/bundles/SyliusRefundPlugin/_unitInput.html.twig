{% set inputName = "sylius_refund_units["~unit.id~"][amount]" %}
{% set hiddenInputName = "sylius_refund_units["~unit.id~"][partial-id]" %}
{% set unitIds = app.request.query.get('unitIds') %}
{% set orderItemUnit = constant('Sylius\\RefundPlugin\\Model\\RefundType::ORDER_ITEM_UNIT') %}

<div class="ui labeled input">
    <div class="ui label">{{ order.currencyCode|sylius_currency_symbol }}</div>

    {% if can_unit_be_refunded(unit.id, orderItemUnit) and unit.id in unitIds %}
        <input value="{{ unit_refund_left(unit.id, orderItemUnit, unit.total) }}" data-refund-input type="text" name="{{ inputName }}" />
    {% else %}
        <input data-refund-input type="text" name="{{ inputName }}" {% if not can_unit_be_refunded(unit.id, constant('Sylius\\RefundPlugin\\Model\\RefundType::ORDER_ITEM_UNIT')) %} disabled{% endif %}/>
    {% endif %}
</div>
