{% extends '@SyliusAdmin/layout.html.twig' %}

{% block title %}{{ 'sylius.ui.order'|trans ~' #'~ order.number }} {{ 'sylius_refund.ui.refunds'|trans }} {{ parent() }}{% endblock %}

{% set customer = order.customer %}
{% set disableButton = order.paymentState == constant('Sylius\\Component\\Core\\OrderPaymentStates::STATE_REFUNDED') ? 'disabled' : '' %}

{% block content %}
    {% include '@SyliusRefundPlugin/_header.html.twig' %}
    {% include '@SyliusRefundPlugin/_breadcrumb.html.twig' %}

    <div class="ui stackable grid">
        <div class="sixteen wide column">
            <div class="ui segment">
                <div class="ui right aligned grid">
                    <div class="sixteen wide column">
                        <button data-refund-clear type="button" class="ui button" {{ disableButton }}>{{ 'sylius_refund.ui.clear_refunds'|trans }}</button>
                        <button data-refund-all type="button" class="ui button primary" {{ disableButton }}>{{ 'sylius_refund.ui.refund_all'|trans }}</button>
                        <div class="ui hidden divider"></div>
                    </div>
                </div>
                <form action="{{ path('sylius_refund_refund_units', {'orderNumber': app.request.attributes.get('orderNumber')}) }}" method="post">
                    <input type="hidden" name="_csrf_token" value="{{ csrf_token(app.request.attributes.get('orderNumber')) }}" />
                    <table id="refunds" class="ui compact celled table">
                        <thead>
                        <tr>
                            <th class="wide sylius-table-column-item">{{ 'sylius.ui.order_item_product'|trans }}</th>
                            <th class="center aligned sylius-table-column-total">{{ 'sylius.ui.total'|trans }}</th>
                            <th class="center aligned">{{ 'sylius_refund.ui.refund_value'|trans }}</th>
                            <th class="center aligned"></th>
                        </tr>
                        </thead>
                        <tbody>
                        {% include '@SyliusRefundPlugin/_items.html.twig' %}
                        {% include '@SyliusRefundPlugin/_shipping.html.twig' %}
                        </tbody>
                    </table>

                    {% include '@SyliusRefundPlugin/_paymentMethod.html.twig' %}
                    {% include '@SyliusRefundPlugin/_footer.html.twig' %}
                    <div class="ui hidden divider"></div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}

    {% include '@SyliusRefundPlugin/_javascripts.html.twig' %}
{% endblock %}
