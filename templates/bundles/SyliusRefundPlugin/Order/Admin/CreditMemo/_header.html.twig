<h1 class="ui header">
    <i class="circular inbox icon"></i>
    <div class="content">
        {{ 'sylius_refund.ui.credit_memo'|trans }} <span id="credit-memo-number">{{ credit_memo.number }}</span>
        <div class="sub header">
            <div class="ui horizontal divided list">
                <div class="item">
                    {{ 'sylius_refund.ui.issued_for_order'|trans }}: #{{ credit_memo.order.number }}
                </div>
                <div class="item">
                    {{ 'sylius_refund.ui.issued_at'|trans }}: {{ credit_memo.issuedAt|date(constant('App\\Admin\\Formatting::DATE')) }}
                </div>
                <div class="item">
                    {{ 'sylius_refund.ui.issued_from'|trans }}
                    {% include '@SyliusAdmin/Common/_channel.html.twig' with {'channel': credit_memo.channel} %}
                </div>
            </div>
        </div>
    </div>
</h1>
