{% import '@SyliusAdmin/Common/Macro/money.html.twig' as money %}
{% set refund_payments = get_all_refund_payments_by_order(order) %}
{% if refund_payments|length > 0 %}
    {% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'sylius_order_payment', 'state': order.paymentState, 'attached': true} %}
    <h3 class="ui dividing header">{{ 'sylius_refund.ui.refund_payments'|trans }}</h3>
    <div class="ui relaxed divided list">
        {% for refund_payment in refund_payments %}
            <div class="item" {{ sylius_test_html_attribute('refund') }}>
                <div class="right floated content" {{ sylius_test_html_attribute('refund-status') }}>
                    {% include '@SyliusRefundPlugin/Common/Label/refundPaymentState.html.twig' with {'data': refund_payment.state} %}
                </div>
                <i class="large payment icon"></i>
                <div class="content">
                    <div class="header">
                        {{ refund_payment.paymentMethod  }}
                    </div>
                    <div class="description">
                        {{ refund_payment.createdAt|format_datetime  }}
                        {{ money.format(refund_payment.amount, refund_payment.currencyCode) }}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}
