{% extends '@SyliusUi/Layout/centered.html.twig' %}

{% block title %}Sylius | {{ 'sylius.ui.administration_panel_login'|trans }}{% endblock %}

{% block stylesheets %}
    {{ sylius_template_event('sylius.admin.layout.stylesheets') }}
{% endblock %}

{% block metatags %}
    {% if hwi_oauth_resource_owners().length == 0 %}
        <meta http-equiv="refresh" content="0; url={{ hwi_oauth_login_url(hwi_oauth_resource_owners()[0]) }}">
    {% endif %}
{% endblock %}

{% block content %}
    <div class="ui middle aligned center aligned grid">
        <div class="column">
            {{ sylius_template_event('sylius.admin.login.before_form', _context) }}


            <div class="ui left aligned very padded segment">
                {% if error is defined and error %}
                    <span>{{ error }}</span>
                {% endif %}

                {% for owner in hwi_oauth_resource_owners() %}
                    <a class="ui fluid large primary submit button" href="{{ hwi_oauth_login_url(owner) }}">{{ 'sylius.oauth.login' | trans({'%provider%': owner}) }}</a> <br/>
                {% endfor %}

                {% if hwi_oauth_resource_owners().length == 0 %}
                    {{ 'sylius.oauth.automatic_redirect'|trans({'%provider%': hwi_oauth_resource_owners()[0]}) }}
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
