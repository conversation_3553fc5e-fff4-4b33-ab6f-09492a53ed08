<div class="ui fluid card" id="customer">
    <div class="content">
        <a href="{{ path('sylius_admin_customer_show', {'id': customer.id}) }}" class="header sylius-customer-name">{{ customer.fullName }}</a>
        <div class="meta">
            <span class="date">{{ 'sylius.ui.customer_since'|trans }} {{ customer.createdAt|format_date }}.</span>
        </div>
    </div>
    <div class="extra content">
        <a href="mailto:{{ customer.email }}">
            <i class="envelope icon"></i>
            {{ customer.email }}
        </a>
    </div>
    {% if customer.phoneNumber is not empty %}
        <div class="extra content">
            <span>
                <i class="phone icon"></i>
                {{ customer.phoneNumber }}
            </span>
        </div>
    {% endif %}
    {% if customer.gender ?? false %}
        <div class="extra content">
            <span>
                <i class="venus mars icon"></i>
                {{ ('app.ui.customer.gender.' ~ customer.gender|lower)|trans }}
            </span>
        </div>
    {% endif %}
    {% if order.customerIp is defined and order.customerIp is not empty %}
        <div class="extra content" id="ipAddress">
            <span>
                <i class="desktop icon"></i>
                {{ order.customerIp }}
            </span>
        </div>
    {% endif %}
</div>
