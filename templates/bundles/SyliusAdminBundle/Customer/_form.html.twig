<div class="ui two column stackable grid">
    {{ form_errors(form) }}
    <div class="column">
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.customer_details'|trans }}</h4>
            <div class="two fields">
                {{ form_row(form.firstName) }}
                {{ form_row(form.lastName) }}
            </div>
            {{ form_row(form.email) }}
            {{ form_row(form.group) }}
            {{ form_row(form.customerPool) }}
        </div>
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.extra_information'|trans }}</h4>
            {{ form_row(form.gender) }}
            {{ form_row(form.birthday) }}
            {{ form_row(form.phoneNumber) }}
            {{ form_row(form.subscribedToNewsletter) }}
        </div>
    </div>
    <div class="column">
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.account_credentials'|trans }}</h4>
            {% if customer.user is empty or customer.user.id is null %}
                <div class="field">
                    <div class="ui toggle checkbox">
                       {{ form_row(form.createUser) }}
                    </div>
                </div>
            {% endif %}
            {% if form.user is defined %}
                <div id="user-form" {% if form.user.vars.data.id is not defined %} style="display: none" {% endif %}>
                    {{ form_row(form.user.enabled) }}
                </div>
            {% endif %}
        </div>
    </div>
</div>
