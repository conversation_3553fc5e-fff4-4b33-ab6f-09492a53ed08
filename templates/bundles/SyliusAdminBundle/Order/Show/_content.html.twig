<div class="ui stackable grid sticky-container">
    <div class="twelve wide column sticky-column">
        {{ sylius_template_event('sylius.admin.order.show.summary', _context) }}
    </div>
    <div class="four wide column">
        {% if "ROLE_CREATE_TICKETS" in app.token.roleNames %}
            <div class="ui segment attached">
                <span class="ui top attached label">
                    <i class="icon chat"></i>
                    Tickets
                </span>

                <div class="content">
                    <div id="csc-create-ticket" data-csc-ordernumber="{{ order.number }}"></div>
                </div>
            </div>
        {% endif %}

        <div class="ui segment">
                <span class="ui {{ get_sylius_label_color('sylius_order', order.state) }} top attached label">
                    <i class="icon {{ get_sylius_label_icon('sylius_order', order.state) }}"></i>
                    Order {{ ('sylius.ui.' ~ order.state)|trans|lower }}
                </span>
            <div class="content">
                <div class="description">
                    <i class="icon clock"></i> {{ order.orderStateUpdatedAt|format_datetime }}
                </div>
                <div class="description">
                    {% if order.cancellation and order.cancellation.reason %}
                        <div>
                            <strong>Cancelled by:</strong>
                            {{ order.cancellation.by.value }}</div>
                        <div>
                            <strong>Cancellation reason:</strong>
                            {{ order.cancellation.reason|cancellationReasonTrans }}</div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% if order.parentOrder %}
            {% include 'Admin/Order/Show/linkedOrder.html.twig' with {
                linkedOrder: order.parentOrder,
                orderType: 'parent'
            } %}
        {% endif %}

        {% if order.followUpOrder %}
            {% include 'Admin/Order/Show/linkedOrder.html.twig' with {
                linkedOrder: order.followUpOrder,
                orderType: 'followUp'
            } %}
        {% endif %}

        {{ sylius_template_event('sylius.admin.order.show.sidebar', _context) }}
    </div>
</div>
