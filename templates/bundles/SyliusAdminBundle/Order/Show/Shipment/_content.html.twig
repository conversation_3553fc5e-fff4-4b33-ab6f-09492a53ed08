<div class="content">
    <div class="header">
        <i class="large truck icon"></i>
        {{ shipment.method }}
    </div>
    <div class="description">
        <i class="globe icon"></i>{{ shipment.method.zone }}
    </div>
    {% if shipment.inventorySource is not null %}
        <div class="description">
            <i class="warehouse icon"></i><span id="inventory-source">{{ shipment.inventorySource.name }}</span>
        </div>
    {% endif %}
    {% if shipment.shippedAt is not empty %}
        <div class="description">
            <i class="calendar alternate icon"></i> <span class="shipped-at-date">{{ shipment.shippedAt|format_datetime }}</span>
        </div>
    {% endif %}

    {% if shipment.reason %}
        <div class="description">
            <i class="sticky note icon"></i>{{ 'sylius.ui.reason'|trans }}: {{ shipment.reason }}
        </div>
    {% endif %}

    {% set trackAndTraceLink = shipment|trackAndTraceLink %}
    <div class="description">
        <i class="barcode icon"></i>
        {% if trackAndTraceLink is not empty %}
            <a href="{{ trackAndTraceLink }}" target="_blank">Track and trace code: {{ shipment.tracking }}</a>
        {% else %}
            <span>No T&T code available</span>
        {% endif %}
    </div>
</div>

