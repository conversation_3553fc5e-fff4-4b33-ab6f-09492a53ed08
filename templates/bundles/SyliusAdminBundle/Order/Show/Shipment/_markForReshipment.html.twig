{% if shipment.state == constant('App\\Entity\\Shipping\\ShipmentInterface::STATE_RETURNED') and not shipment.immutable %}
    {% set path = path('app_admin_order_shipment_mark_for_reshipment', {'orderId': shipment.order.id,'shipmentId': shipment.id}) %}
    <form action="{{ path }}">
        <button class="ui fluid tiny button grey icon" style="margin-top: 10px;" type="submit" data-requires-confirmation>
            <i class="icon redo"></i> {{ 'sylius.ui.mark_for_reshipment'|trans }}
        </button>
    </form>
{% endif %}
