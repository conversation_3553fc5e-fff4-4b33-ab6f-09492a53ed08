<table class="ui celled compact small table order-summary-table">
    <thead>
    <tr>
        <th class="five wide sylius-table-column-item">{{ 'sylius.ui.order_item_product'|trans }}</th>
        <th class="center aligned sylius-table-column-supplier-shipment">{{ 'sylius.ui.supplier_shipment'|trans }}</th>
        <th class="center aligned sylius-table-column-unit_price">{{ 'sylius.ui.unit_price'|trans }}</th>
        <th class="center aligned sylius-table-column-unit_discount">{{ 'sylius.ui.unit_discount'|trans }}</th>
        <th class="center aligned sylius-table-column-unit_order_discount">{{ 'sylius.ui.distributed_order_discount'|trans }}</th>
        <th class="center aligned sylius-table-column-discounted_unit_price">{{ 'sylius.ui.discounted_unit_price'|trans }}</th>
        <th class="center aligned sylius-table-column-quantity">{{ 'sylius.ui.quantity'|trans }}</th>
        <th class="center aligned sylius-table-column-subtotal">{{ 'sylius.ui.subtotal'|trans }}</th>
        <th class="center aligned sylius-table-column-tax">{{ 'sylius.ui.tax'|trans }}</th>
        <th class="center aligned sylius-table-column-total">{{ 'sylius.ui.total'|trans }}</th>
    </tr>
    </thead>
    <tbody>
    {% for item in order.items %}
        {% include '@SyliusAdmin/Order/Show/Summary/_item.html.twig' %}
    {% endfor %}

    {# Custom HTML for adding service products to an order #}
    {% if sylius_rbac_has_permission('app_admin_order_items_update') and order.prescriptionState in constant('App\\StateMachine\\OrderPrescriptionStates::ALLOWED_FOR_SHIPMENT_STATES') %}
        <tr>
            <td colspan="10">
                <div style="padding: 0.5rem;">
                    {% set form = add_service_product_variant_form(order) %}

                    {{ form_start(form) }}
                        <div class="ui accordion">
                            <div class="title{% if not form.vars.valid %} active{%endif%}" style="display: inline-block;">
                                <i class="plus icon"></i>
                                {{ form_label(form.serviceProductVariant, null, { label_attr: { style: 'cursor: pointer;' }}) }}
                            </div>

                            <div class="content{% if not form.vars.valid %} active{%endif%}">
                                <div class="ui action input">
                                    {% if not form.vars.valid %}
                                        <div class="ui error message">
                                            {{ form_errors(form) }}
                                        </div>
                                    {% endif %}

                                    {{ form_widget(form.serviceProductVariant, { attr: { class: 'ui dropdown'}}) }}
                                    {{ form_widget(form.submit, { attr: { class: 'ui primary button' }}) }}
                                </div>
                            </div>
                        </div>
                    {{ form_end(form) }}
                </div>
            </td>
        </tr>
    {% endif %}
    {# End custom HTML #}

    </tbody>
    <tfoot>
    {% include '@SyliusAdmin/Order/Show/Summary/_totals.html.twig' %}
    </tfoot>
</table>
