{% import '@SyliusUi/Macro/flags.html.twig' as flags %}

<h1 class="ui header">
    <i class="circular cart icon"></i>
    <div class="content">
        {{ 'sylius.ui.order'|trans }} #{{ order.number }}
        <div class="sub header">
            <div class="ui horizontal divided list">
                <div class="item">
                    {{ order.checkoutCompletedAt|format_datetime }}
                </div>
                <div class="item" id="sylius-order-state">
                    {% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'sylius_order', 'state': order.state} %}
                    {% if order.returnRequest is not null %}
                        <a class="ui teal tag label" href="{{ path('sylius_plus_admin_return_request_show', {'id': order.returnRequest.id}) }}">
                            {{ 'sylius_plus.ui.returns.replacement'|trans }}
                        </a>
                    {% endif %}
                </div>
                <div class="item" id="sylius-order-currency">
                    {{ order.currencyCode }}
                </div>
                <div class="item">
                    {{ flags.fromLocaleCode(order.localeCode) }}{{ order.localeCode|sylius_locale_name }}
                </div>
                <div class="item">
                    {{ 'sylius.ui.purchased_from'|trans }}
                    <span class="ui large empty horizontal circular label" style="background-color: {{ order.channel.color }}"></span> <strong>{{ order.channel }}</strong>
                </div>
            </div>
        </div>
    </div>
</h1>
