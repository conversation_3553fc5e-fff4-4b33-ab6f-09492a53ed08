{% if order.billingAddress is not null %}
    <h4 class="ui attached styled header top">
        {{ 'sylius.ui.billing_address'|trans }}
    </h4>
    <div class="ui attached segment" id="billing-address">
        {% include '@SyliusAdmin/Common/_address.html.twig' with {'address': order.billingAddress} %}
    </div>
{% endif %}
{% if order.shippingAddress is not null %}
    <h4 class="ui attached styled header{% if order.billingAddress is null %} top{% endif %}">
        {{ 'sylius.ui.shipping_address'|trans }}
    </h4>
    <div class="ui attached segment" id="shipping-address">
        {% include '@SyliusAdmin/Common/_address.html.twig' with {'order': order, 'address': order.shippingAddress, 'shippingAddress': true} %}
    </div>
{% endif %}
<div class="ui attached segment" id="edit-addresses">
    <a href="{{ path('sylius_admin_order_update', {'id': order.id}) }}" class="ui icon labeled tiny fluid button">
        <i class="pencil icon"></i> {{ 'sylius.ui.edit_addresses'|trans }}
    </a>
</div>
