{% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'sylius_order_payment', 'state': order.paymentState, 'attached': true} %}
<h3 class="ui dividing header">{{ 'sylius.ui.payments'|trans }}</h3>
<div class="ui relaxed divided list" id="sylius-payments">
    {% for payment in order.payments %}
        {% include '@SyliusAdmin/Order/Show/_payment.html.twig' %}
    {% endfor %}
</div>
