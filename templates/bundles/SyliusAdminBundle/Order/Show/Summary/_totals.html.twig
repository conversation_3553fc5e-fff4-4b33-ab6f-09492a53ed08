{% import "@SyliusAdmin/Common/Macro/money.html.twig" as money %}

{% set orderShippingPromotionAdjustment = constant('Syl<PERSON>\\Component\\Core\\Model\\AdjustmentInterface::ORDER_SHIPPING_PROMOTION_ADJUSTMENT') %}
{% set shippingAdjustment = constant('Sylius\\Component\\Core\\Model\\AdjustmentInterface::SHIPPING_ADJUSTMENT') %}
{% set taxAdjustment = constant('Sylius\\Component\\Core\\Model\\AdjustmentInterface::TAX_ADJUSTMENT') %}

{% set orderShippingPromotions = sylius_aggregate_adjustments(order.getAdjustmentsRecursively(orderShippingPromotionAdjustment)) %}

<tr>
    <th colspan="8"></th>
    <th colspan="1" id="tax-total" class="right aligned">
        <strong>{{ 'sylius.ui.tax_total'|trans }}</strong>:
        {{ money.format(order.taxTotal, order.currencyCode) }}
    </th>
    <th colspan="1" id="items-total" class="right aligned">
        <strong>{{ 'sylius.ui.items_total'|trans }}</strong>:
        {{ money.format(order.itemsTotal, order.currencyCode) }}
    </th>
</tr>
<tr>
    <td colspan="{{ orderShippingPromotions ? 2 : 6 }}" id="shipping-charges">
        {% if not order.adjustments(shippingAdjustment).isEmpty() %}
            <div class="ui relaxed divided list">
                <div class="item"><strong>{{ 'sylius.ui.shipping'|trans }}:</strong></div>
                {% for shipment in order.shipments %}
                    {% for adjustment in shipment.adjustments(shippingAdjustment) %}
                        <div class="item">
                            <div id="shipping-base-value" class="right floated">{{ money.format(adjustment.amount, order.currencyCode) }}</div>
                            <div class="content">
                                <div id="shipping-adjustment-label" class="description">
                                    <strong>{{ adjustment.label }}</strong>:
                                </div>
                            </div>
                        </div>
                    {% endfor %}

                    {% for adjustment in shipment.adjustments(taxAdjustment) %}
                        <div class="item{% if adjustment.isNeutral %} tax-disabled{% endif %}">
                            <div id="shipping-tax-value" class="right floated">
                                {{ money.format(adjustment.amount, order.currencyCode) }}
                                {% if adjustment.isNeutral %}
                                    <small>({{ 'sylius.ui.included_in_price'|trans }})</small>
                                {% endif %}
                            </div>
                            <div class="content">
                                <div id="shipping-adjustment-label" class="description">
                                    <strong{% if adjustment.isNeutral %} class="tax-disabled"{% endif %}>{{ adjustment.label }}</strong>:
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% endfor %}
            </div>
        {% else %}
            <p><small>{{ 'sylius.ui.no_shipping_charges'|trans }}</small></p>
        {% endif %}
    </td>
    {% if not orderShippingPromotions is empty %}
    <td colspan="3" id="promotion-shipping-discounts">
        <div class="ui relaxed divided list">
            <div class="item"><strong>{{ 'sylius.ui.shipping_discount'|trans }}:</strong></div>
            {% for label, amount in orderShippingPromotions %}
                <div class="item">
                    <div id="shipping-discount-value" class="right floated">
                        {{ money.format(amount, order.currencyCode) }}
                    </div>
                </div>
            {% endfor %}
        </div>
    </td>
    {% endif %}
    <td colspan="4" id="shipping-total" class="right aligned">
        <strong>{{ 'sylius.ui.shipping_total'|trans }}</strong>:
        {{ money.format(order.shippingTotal, order.currencyCode) }}
    </td>
</tr>

{% include '@SyliusAdmin/Order/Show/Summary/_totalsPromotions.html.twig' %}

<tr>
    <td colspan="10" id="total" class="ui large header right aligned">
        <strong>{{ 'sylius.ui.order_total'|trans }}</strong>:
        {{ money.format(order.total, order.currencyCode) }}
    </td>
</tr>
