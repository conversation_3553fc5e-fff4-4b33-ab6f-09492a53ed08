{% import "@SyliusAdmin/Common/Macro/money.html.twig" as money %}

{% set orderPromotionAdjustment = constant('<PERSON>yl<PERSON>\\Component\\Core\\Model\\AdjustmentInterface::ORDER_PROMOTION_ADJUSTMENT') %}
{% set unitPromotionAdjustment = constant('Sylius\\Component\\Core\\Model\\AdjustmentInterface::ORDER_UNIT_PROMOTION_ADJUSTMENT') %}
{% set shippingAdjustment = constant('Sylius\\Component\\Core\\Model\\AdjustmentInterface::SHIPPING_ADJUSTMENT') %}
{% set taxAdjustment = constant('Sylius\\Component\\Core\\Model\\AdjustmentInterface::TAX_ADJUSTMENT') %}

{% set variant = item.variant %}
{% set product = variant.product %}

{% set aggregatedUnitPromotionAdjustments = item.getAdjustmentsTotalRecursively(unitPromotionAdjustment) + item.getAdjustmentsTotalRecursively(orderPromotionAdjustment) %}
{% set subtotal = (item.unitPrice * item.quantity) + aggregatedUnitPromotionAdjustments %}

{% set taxIncluded = sylius_admin_order_unit_tax_included(item) %}
{% set taxExcluded = sylius_admin_order_unit_tax_excluded(item) %}
{% set itemUnit = item.units.first %}

<tr>
    <td class="single line">
        {% include '@SyliusAdmin/Product/_info.html.twig' %}
    </td>
    <td class="left aligned supplier-shipment">
        {% if item.variant.supplier.name|default(false) %}
            <div class="left floated content">
                <span class="ui blue label">
                        <i class="warehouse icon"></i>
                        <span>{{ item.variant.supplier.name }}</span><br>
                </span>
            </div>
            <div class="ui fitted divider hidden"></div>
        {% endif %}
        {% if itemUnit.shipment|default(false) %}
        <div class="left floated content">
            <a href="{{ path('sylius_admin_shipment_show', {'id': itemUnit.shipment.id}) }}" class="ui {{ get_sylius_label_color('sylius_shipment', itemUnit.shipment.state) }} label">
                <i class="truck icon"></i>
                <span>{{ itemUnit.shipment.id }}</span>
            </a>
        </div>
        {% endif %}
    </td>
    <td class="right aligned unit-price">
        {{ money.format(item.unitPrice, order.currencyCode) }}
    </td>
    <td class="right aligned unit-discount">
        {{ money.format(item.units.first.adjustmentsTotal(unitPromotionAdjustment), order.currencyCode) }}
    </td>
    <td class="right aligned unit-order-discount">
        <span style="font-style: italic;">~ {{ money.format(item.units.first.adjustmentsTotal(orderPromotionAdjustment), order.currencyCode) }}</span>
    </td>
    <td class="right aligned discounted-unit-price">
        {{ money.format(item.fullDiscountedUnitPrice, order.currencyCode) }}
    </td>
    <td class="right aligned quantity">
        {{ item.quantity }}
    </td>
    <td class="right aligned subtotal">
        {{ money.format(subtotal, order.currencyCode) }}
    </td>
    <td class="right aligned tax">
        <div class="tax-excluded">{{ money.format(taxExcluded, order.currencyCode) }}</div>
        <div class="tax-disabled">
            <div class="tax-included"> {{ money.format(taxIncluded, order.currencyCode) }}
            </div>
            <small>({{ 'sylius.ui.included_in_price'|trans }})</small>
        </div>
    </td>
    <td class="right aligned total">
        {{ money.format(item.total, order.currencyCode) }}<br>
        {% if sylius_rbac_has_permission('app_admin_order_items_update') and product.type == constant('App\\Entity\\Product\\ProductType::SERVICE').value %}
            {% set form = remove_service_product_variant_form(order, item) %}
            {% if form is defined and form is not null %}
                <br>
                <div>
                    {{ form_start(form) }}
                        {{ form_widget(form.serviceProductVariant, { attr: { style: 'display:none!important;'}}) }}
                        {{ form_widget(form.submit, { label: '<i class="icon trash alternate" style="margin: 0;"></i>', label_html: true, attr: { class: 'ui button red' }}) }}
                    {{ form_end(form) }}
                </div>
            {% endif %}
        {% endif %}
    </td>
</tr>
