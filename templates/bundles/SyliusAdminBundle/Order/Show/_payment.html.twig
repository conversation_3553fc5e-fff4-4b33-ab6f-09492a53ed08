{% import "@SyliusAdmin/Common/Macro/money.html.twig" as money %}
{% import '@SyliusUi/Macro/labels.html.twig' as label %}

<div class="item">
    <div class="right floated content">
        {% include '@SyliusAdmin/Common/Label/paymentState.html.twig' with {'data': payment.state} %}
    </div>
    <i class="large payment icon"></i>
    <div class="content">
        <div class="header">
            {{ payment.method }} {% if payment.paymentServiceProviderName %}({{ payment.paymentServiceProviderName }}){% endif %}
        </div>
        <div class="description">
            {{ payment.createdAt|format_datetime }}<br>
            {{ money.format(payment.amount, payment.order.currencyCode) }}
        </div>
    </div>
    {% if sm_can(payment, constant('\\App\\StateMachine\\PaymentTransitions::TRANSITION_FORCE_PAY'), constant('\\Sylius\\Component\\Payment\\PaymentTransitions::GRAPH')) %}
        <div class="ui segment">
            <form action="{{ path('sylius_admin_order_payment_complete', {'orderId': order.id, 'id': payment.id}) }}" method="post" novalidate>
                <input type="hidden" name="_csrf_token" value="{{ csrf_token(payment.id) }}" />
                <input type="hidden" name="_method" value="PUT">
                <button type="submit" class="ui icon labeled tiny blue fluid loadable button"><i class="check icon"></i> {{ 'app.ui.payment.force_pay'|trans }}</button>
            </form>
        </div>
    {% endif %}
</div>
