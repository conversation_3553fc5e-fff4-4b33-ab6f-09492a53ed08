<!-- BEGIN bundles/SyliusAdminBundle/Order/Label/_state.html.twig -->
{% if data is defined %} {% set state = data %} {% endif %}
{% if options is defined and options.vars is defined %}
    {% if options.vars.graph is defined %}
        {% set graph = options.vars.graph %}
    {% endif %}
    {% if options.vars.state is defined %}
        {% set state = options.vars.state %}
    {% endif %}
{% endif %}

<span class="ui {{ get_sylius_label_color(graph, state) }} {% if attached is defined and attached == true %} top attached{% endif %} label">
    <i class="{{ get_sylius_label_icon(graph, state) }} icon"></i>
    <span>{{ ('sylius.ui.' ~ state)|trans }}</span>
</span>
<!-- END bundles/SyliusAdminBundle/Order/Label/_state.html.twig -->
