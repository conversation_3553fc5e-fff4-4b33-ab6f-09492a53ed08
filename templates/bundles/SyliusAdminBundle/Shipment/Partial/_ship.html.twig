{% import '@SyliusUi/Macro/buttons.html.twig' as buttons %}

<div class="ui hidden divider"></div>
<div class="ui center aligned basic spaceless segment">
    {{ form_start(form, {'action': path('sylius_admin_order_shipment_ship', configuration.vars.route.parameters), 'attr': {'class': 'ui loadable form', 'novalidate': 'novalidate'}}) }}
    <input type="hidden" name="_method" value="PUT">

    <div class="ui right action input" style="flex-wrap: wrap; display: flex;">
        {{ form_widget(form.tracking, {'attr': {'placeholder': 'sylius.ui.tracking_code'|trans ~ '...', 'style': 'width: auto'}}) }}
        <button type="submit" class="ui labeled icon teal button"><i class="plane icon"></i> {{ 'sylius.ui.ship'|trans }}</button>
    </div>

    {{ form_row(form._token) }}
    {{ form_end(form, {'render_rest': false}) }}
</div>
