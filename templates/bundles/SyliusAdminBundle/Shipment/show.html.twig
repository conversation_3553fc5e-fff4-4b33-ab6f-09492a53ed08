{% extends '@SyliusAdmin/layout.html.twig' %}

{% import '@SyliusUi/Macro/buttons.html.twig' as buttons %}

{% set referer = app.request.headers.get('referer') %}

{% block title %}{{ 'sylius.ui.shipment'|trans }} | {{ shipment.id }}{% endblock %}

{% block content %}
    <div class="ui stackable two column grid">
        <div class="twelve wide column">
            {% include '@SyliusAdmin/Shipment/Show/_header.html.twig' %}
        </div>
        <div class="four wide right aligned column">
            {{ buttons.default(referer|default(app.request.getSchemeAndHttpHost() ~ path('sylius_admin_shipment_index')), '', 'back', 'arrow alternate circle left outline') }}
        </div>
    </div>

    {% include '@SyliusAdmin/Shipment/Show/_breadcrumb.html.twig' %}
    <div class="ui segment spaceless sylius-grid-table-wrapper">
        <table class="ui sortable stackable very basic celled table">
            <thead>
            <tr>
                <th>{{ 'sylius.ui.product'|trans }}</th>
                <th>{{ 'sylius.ui.variant'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {#
            Modified to not trigger an error for order item units without a variant.
            Not all order items have a variant due to missing data from legacy systems during data migration.
            #}
            {% for unit in shipment.units %}
                {% set variant = unit.orderItem.variant %}
                {% if sylius_rbac_has_permission("app_admin_order_product_names") or variant.prescriptionRequired == false %}
                    {% set productName = unit.orderItem.variant ? unit.orderItem.product.name : 'unknown' %}
                    {% set variantName = unit.orderItem.variant ? unit.orderItem.variant.name : unit.orderItem.variantName %}
                {% else %}
                    {% if variant.product.getType() == 'consult' %}
                        {% set productName = 'Consult' %}
                        {% set variantName = 'Consult' %}
                    {% else %}
                        {% set productName = 'Product' %}
                        {% set variantName = 'Product' %}
                    {% endif %}
                {% endif %}
                <tr class="item">
                    <td>{{ productName }}</td>
                    <td>{{ variantName }}</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}
