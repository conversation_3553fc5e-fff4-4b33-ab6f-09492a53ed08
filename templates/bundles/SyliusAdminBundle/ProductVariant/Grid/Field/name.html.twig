<span class="ui small header">
    {% set product = data.product %}
    {{ data.name is null ? product.name : data.name }}
</span>
<span class="ui small">
    {{ data.caption }}
</span>
{% if data.optionValues|length > 0 %}
    <br>
    <div class="ui horizontal divided list">
        {% for optionValue in data.optionValues %}
            <div class="item">
                {{ optionValue.value }}
            </div>
        {% endfor %}
    </div>
{% endif %}
