{% form_theme form '@SyliusUi/Form/imagesTheme.html.twig' %}

<div class="ui two column stackable grid">
    {{ form_errors(form) }}
    <div class="column">
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.general_info'|trans }}</h4>
            <div class="two fields">
                {{ form_row(form.username) }}
                {{ form_row(form.email) }}
            </div>
        </div>
        <div class="ui segment">
            {{ form_row(form.plainPassword) }}
            {{ form_row(form.enabled) }}
        </div>
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.additional_information'|trans }}</h4>
            <div class="two fields">
                {{ form_row(form.firstName) }}
                {{ form_row(form.lastName) }}
            </div>
            <div class="field" id="add-avatar">
                <label>{{ 'sylius.ui.avatar'|trans }}</label>
                {{ form_row(form.avatar, {'label': false}) }}
            </div>
            {% if admin_user.avatar is not null %}
                <button formaction="{{ path('sylius_admin_admin_user_remove_avatar', {'id': app.request.attributes.get('id'), '_csrf_token': csrf_token(app.request.attributes.get('id'))}) }}" type="submit" class="ui icon red labeled button">
                    <i class="icon trash"></i> {{ 'sylius.ui.delete'|trans }}
                </button>
            {% endif %}
        </div>
    </div>
    <div class="column">
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.preferences'|trans }}</h4>
            {{ form_row(form.localeCode) }}
            {{ form_row(form.timeZone) }}
        </div>
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius_plus.ui.roles'|trans }}</h4>
            {{ form_row(form.rolesResources) }}
            {{ form_row(form.enablePermissionChecker) }}
        </div>
        <div class="ui segment">
            <h4 class="ui dividing header">{{ 'sylius.ui.channel'|trans }}</h4>
            {{ form_row(form.channel) }}
        </div>
    </div>
</div>
