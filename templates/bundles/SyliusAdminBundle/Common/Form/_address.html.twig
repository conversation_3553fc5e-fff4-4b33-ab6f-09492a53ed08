{% set isPickupPointAddress = form.vars.value.pickupPointAddress is not empty %}

{% if isPickupPointAddress %}
    <div class="ui warning message" style="display: block; margin-bottom: 0;">
        <div class="header">
            {{ 'sylius.ui.address_not_editable.title'|trans }}
        </div>
        {{ 'sylius.ui.address_not_editable.text'|trans }}
    </div>
{% endif %}

<div style="{% if isPickupPointAddress %}display: none;{% endif %}">
    <div class="two fields">
        {{ form_row(form.firstName) }}
        {{ form_row(form.lastName) }}
    </div>
    {{ form_row(form.company) }}
    {{ form_row(form.street) }}
    {{ form_row(form.countryCode) }}
    <div class="province-container field" data-url="{{ path('sylius_admin_ajax_render_province_form') }}">
        {% if form.provinceCode is defined %}
            {{ form_row(form.provinceCode, {'attr': {'class': 'ui dropdown'}}) }}
        {% endif %}
    </div>
    <div class="two fields">
        {{ form_row(form.city) }}
        {{ form_row(form.postcode) }}
    </div>
    {{ form_row(form.phoneNumber) }}
</div>
