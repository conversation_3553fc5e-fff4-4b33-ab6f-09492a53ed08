{% import "@SyliusUi/Macro/flags.html.twig" as flags %}

<address>
    {% if address.pickupPointAddress is not empty %}
        <b>DHL ServicePoint</b><br/>
        {{ address.pickupPointAddress.name }}<br/>
        {{ address.pickupPointAddress.street }}<br/>
        {{ address.pickupPointAddress.postcode }} {{ address.pickupPointAddress.city }}<br/>
        {% if address.additionalAddressInformation %}
            {{ address.additionalAddressInformation }}<br/>
        {% endif %}
    {% else %}
        <strong>{{ address.firstName }} {{ address.lastName }}</strong><br/>
        {% if address.company %}
            {{ address.company }}<br/>
        {% endif %}
        {{ address.phoneNumber }}<br/>
        {{ address.street }}<br/>
        {{ address.city }}<br/>
        {% if address|sylius_province_name is not empty %}
            {{ address|sylius_province_name }}<br/>
        {% endif %}
        {{ flags.fromCountryCode(address.countryCode) }}
        {{ address.countryCode|sylius_country_name|upper }} <a href="{{ path('sylius_admin_order_index', {'criteria[shippingAddressPostcode][shippingAddressPostcode]': address.postcode}) }}">{{ address.postcode }}</a>
    {% endif %}
</address>
{% if order %}
    {% set commentForm = fraudCheckComment(order) %}
    {% if commentForm is not empty and shippingAddress %}
        <br>
       {% if not is_granted('ROLE_UPDATE_FRAUD_CHECK_CONFIGURATION') %}
           {{ commentForm.comment.vars.value }}
       {% else %}
           <div class="ui form">
           {{ form_label(commentForm) }}
           {{ form_errors(commentForm) }}
           {{ form_start(commentForm) }}
           {{ form_row(commentForm.comment) }}
           <br>
           {{ form_row(commentForm.submit) }}
           {{ form_end(commentForm) }}
           </div>
       {% endif %}
    {% endif %}
{% endif %}
