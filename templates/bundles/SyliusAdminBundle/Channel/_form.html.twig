{{ form_errors(form) }}
<div class="ui two column stackable grid">
    <div class="column">
        <h4 class="ui top attached large header">{{ 'sylius.ui.general'|trans }}</h4>
        <div class="ui attached segment">
            {{ form_errors(form) }}
            <div class="fields">
                <div class="six wide field">
                    {{ form_row(form.code) }}
                </div>
                <div class="seven wide field">
                    {{ form_row(form.name) }}
                </div>
                <div class="three wide field">
                    {{ form_row(form.color) }}
                </div>
            </div>
            <div class="ui hidden divider"></div>
            {{ form_row(form.enabled) }}
            <div class="ui hidden divider"></div>
            {{ form_row(form.businessUnit) }}
            <div class="ui hidden divider"></div>
            <div class="ui icon negative orange message sylius-flash-message">
                <i class="warning icon"></i>
                <div class="content">
                    <div class="header">
                        {{ 'app.ui.addPrescriptionMedicationDirectlyToCart.warning'|trans }}
                    </div>
                </div>
            </div>
            {{ form_row(form.addPrescriptionMedicationDirectlyToCart) }}
            {% if channel.isAddPrescriptionMedicationDirectlyToCart() is same as(false) %}
                {{ form_row(form.medicationChannel) }}
            {% endif %}
        </div>
        <div class="ui attached segment">
            {{ form_row(form.countries) }}
        </div>
        <div class="ui attached segment">
            <div class="field">
                {{ form_label(form.hostname) }}
                <div class="ui labeled input">
                    <div class="ui label">https://</div>
                    {{ form_widget(form.hostname) }}
                </div>
                {{ form_errors(form.hostname) }}
            </div>
            {{ form_row(form.contactEmail) }}
            {{ form_row(form.description, {'attr': {'rows' : '3'}}) }}
        </div>
        <div class="ui hidden divider"></div>
        <h4 class="ui top attached large header">{{ 'sylius.ui.money'|trans }}</h4>
        <div class="ui attached segment">
            <div class="two fields">
                {{ form_row(form.baseCurrency) }}
                {{ form_row(form.currencies) }}
            </div>
        </div>
        <div class="ui attached segment">
            {{ form_row(form.defaultTaxZone) }}
            {{ form_row(form.taxCalculationStrategy) }}
        </div>
    </div>
    <div class="column">
        <h4 class="ui top attached large header">{{ 'sylius.ui.look_and_feel'|trans }}</h4>
        <div class="ui attached segment">
            {{ form_row(form.themeName) }}
        </div>
        <div class="ui attached segment">
            {{ form_row(form.locales) }}
            {{ form_row(form.defaultLocale) }}
        </div>
        <div class="ui attached segment">
            {{ form_row(form.menuTaxon) }}
        </div>
        <div class="ui hidden divider"></div>
        <div class="ui attached segment">
            {{ form_row(form.skippingShippingStepAllowed) }}
            {{ form_row(form.skippingPaymentStepAllowed) }}
            {{ form_row(form.accountVerificationRequired) }}
            <div class="ui hidden divider"></div>
            {{ form_row(form.customerPool) }}
            <div class="ui hidden divider"></div>
            {{ form_row(form.multipleConsultsAllowed) }}
            {{ form_row(form.multipleShipmentsAllowed) }}
            {{ form_row(form.pickupPointsAllowed) }}
        </div>
    </div>
</div>
