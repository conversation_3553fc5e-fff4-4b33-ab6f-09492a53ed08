{% set hasChannel = sylius_admin_has_channel() %}
{% if hasChannel == false %}
    {% set selected = channels|first %}
    {% set code = app.request.query.get('channel') %}

    {% for channel in channels %}
        {% if channel.code is same as(code) %}
            {% set selected = channel %}
        {% endif %}
    {% endfor %}

    <div class="ui floating dropdown labeled icon button">
        <i class="share alternate icon"></i>
        <span class="text">
            {% include '@SyliusAdmin/Common/_channel.html.twig' with {'channel': selected} %}
        </span>
        <div class="menu">
            <div class="ui icon search input">
                <i class="search icon"></i>
                <input type="text" placeholder="{{ 'sylius.ui.search'|trans }}...">
            </div>
            <div class="scrolling menu">

                {% for channel in channels %}
                    <a href="{{ path('sylius_admin_dashboard', {'channel': channel.code}) }}" class="item">
                        {% include '@SyliusAdmin/Common/_channel.html.twig' %}
                    </a>
                {% endfor %}
            </div>
        </div>
    </div>
{% endif %}
