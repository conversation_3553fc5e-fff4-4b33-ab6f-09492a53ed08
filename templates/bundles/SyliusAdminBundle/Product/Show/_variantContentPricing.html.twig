{% import "@SyliusAdmin/Common/Macro/money.html.twig" as money %}

<div class="column">
    <div class="ui segment">
        <div class="ui small header">
            {{ 'sylius.ui.country_details'|trans }}
        </div>

        {% set currencies = sylius_channels_currencies() %}
        {% set groupedVariant  = sylius_create_grouped_product_variant(variant.product, variant.codeWithoutSupplierCountry) %}
        <div class="ui top attached tabular menu middle aligned">
            {% for combinedChannelPricing in groupedVariant.pricing %}
                <a class="item{{ loop.first ? ' active' : '' }}" data-tab="{{ variant.codeWithoutSupplierCountry }}_{{ combinedChannelPricing.channelCode }}" style="padding: 0.5rem 0.75rem;">
                    {{ combinedChannelPricing.channelCode|replace({'dok_':'', 'blueclinic_': 'BC_', 'seeme_': ''})|upper }} <label class="ui {{ combinedChannelPricing.enabled ? 'green' : 'red' }} empty circular label"></label>
                </a>
            {% endfor %}
        </div>

        {% for channelPricing in groupedVariant.pricing %}
            <div class="ui bottom attached tab segment{{ loop.first ? ' active' : '' }}" data-tab="{{ variant.codeWithoutSupplierCountry }}_{{ channelPricing.channelCode }}">
                <div class="ui row grid two column">
                    <div class="ui thirteen wide column">
                        <div class="ui celled grid">
                            <div class="row four column">
                                <div class="column">
                                    <strong>Pharmacy</strong>
                                </div>
                                <div class="column">
                                    <strong>Variant Code</strong>
                                </div>
                                <div class="column">
                                    <strong>Variant ID</strong>
                                </div>
                                <div class="column">
                                    <strong>Stock</strong>
                                </div>
                            </div>
                            {% for productVariant in  groupedVariant.variants %}
                                <div class="row four column">
                                    <div class="column">
                                        {{ productVariant.supplier.getName() }}
                                    </div>
                                    <div class="column break-text">
                                        {{ productVariant.code }}
                                    </div>
                                    <div class="column">
                                        {{ productVariant.supplierVariantCode }}
                                    </div>
                                    <div class="column">
                                        {% set isInStock = productVariant.isInStockForChannelCode(channelPricing.channelCode) %}
                                        {% if isInStock %}
                                            In Stock
                                        {% else %}
                                            Out of Stock
                                        {% endif %}
                                        <label class="ui {{ isInStock ? 'green' : 'red' }} empty circular label"></label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="ui three wide column">
                        <div class="ui celled grid">
                            <div class="row">
                                <div class="column">
                                    <strong>Price</strong>
                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    {{ money.format(channelPricing.price, currencies[channelPricing.channelCode]) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
