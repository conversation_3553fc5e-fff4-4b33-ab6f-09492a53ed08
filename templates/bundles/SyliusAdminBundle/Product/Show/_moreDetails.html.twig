{% import '@SyliusUi/Macro/flags.html.twig' as flags %}

<div id="more-details" class="ui styled fluid spaceless accordion">
    <div class="title">
        <i class="dropdown icon"></i>
        Translations
    </div>

    <div class="content">
        <div class="ui">
            <div class="ui top attached tabular menu middle aligned">
                {% for translation in product.translations %}
                    <a class="item{{ loop.first ? ' active' : ''}}" data-tab="{{ translation.locale }}">
                        {{ translation.locale|upper}}
                    </a>
                {% endfor %}
            </div>
                {% for translation in product.translations %}
                    <div class="ui bottom attached tab segment{{ loop.first ? ' active' : ''}}" data-tab="{{ translation.locale }}" style="margin: 0;">
                        <table class="ui basic celled table">
                            <tbody>
                            <tr>
                                <td class="three wide"><strong class="gray text">{{ 'sylius.ui.name'|trans }}</strong></td>
                                <td>{{ translation.name }}</td>
                            </tr>
                            <tr>
                                <td class="three wide"><strong class="gray text">{{ 'sylius.ui.slug'|trans }}</strong></td>
                                <td>{{ translation.slug }}</td>
                            </tr>
                            <tr>
                                <td class="three wide"><strong class="gray text">{{ 'sylius.ui.description'|trans }}</strong></td>
                                <td>{{ translation.description|nl2br }}</td>
                            </tr>
                            <tr>
                                <td class="three wide"><strong class="gray text">{{ 'sylius.ui.meta_keywords'|trans }}</strong></td>
                                <td>{{ translation.metaKeywords }}</td>
                            </tr>
                            <tr>
                                <td class="three wide"><strong class="gray text">{{ 'sylius.ui.meta_description'|trans }}</strong></td>
                                <td>{{ translation.metaDescription }}</td>
                            </tr>
                            <tr>
                                <td class="three wide"><strong class="gray text">{{ 'sylius.ui.short_description'|trans }}</strong></td>
                                <td>{{ translation.shortDescription }}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                {% endfor %}
        </div>
    </div>
</div>
