<tr class="variants-accordion__title item-{{ loop.index	}}">
    <td class="center aligned">
        <button class="ui basic icon button" style="box-shadow: none">
            <i class="counterclockwise rotated dropdown icon"></i>
        </button>
    </td>
    <td>
        <div class="ui items">
            <div class="item">
                <div class="ui image">
                    {% if variant.hasImages %}
                        {% include '@SyliusAdmin/Product/_mainImage.html.twig' with {'product': variant, 'filter': 'sylius_admin_product_large_thumbnail'} %}
                    {% else %}
                        {% include '@SyliusAdmin/Product/_mainImage.html.twig' with {'product': product, 'filter': 'sylius_admin_product_large_thumbnail'} %}
                    {% endif %}
                </div>
                <div class="middle aligned content">
                    <div><strong class="variant-name">{{ variant.name }}</strong></div>
                    <small class="gray text variant-code">{{ variant.codeWithoutSupplierCountry }}</small>
                </div>
            </div>
        </div>
    </td>
    <td>
        {% for optionValue in variant.optionValues %}
            <div><span class="gray text">{{ optionValue.option.name }}:</span> {{ optionValue.value }}</div>
        {% endfor %}
    </td>
    <td>
        {% if variant.prescriptionRequired %}
            <div class="ui label teal">RX</div>
        {% else %}
            <div class="ui label">OTC</div>
        {% endif %}
    </td>
</tr>
