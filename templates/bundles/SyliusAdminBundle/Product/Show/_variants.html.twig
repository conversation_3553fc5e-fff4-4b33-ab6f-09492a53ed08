{% import "@SyliusAdmin/Common/Macro/money.html.twig" as money %}

<div id="variants">
    <h4 class="ui top attached large header">{{ 'sylius.ui.list_variants'|trans }}</h4>
    <div class="ui attached spaceless segment">
        <table class="ui very basic table" style="padding-top: 20px">
            <thead>
                <tr>
                    <th></th>
                    <th>{{ 'sylius.ui.name'|trans }}</th>
                    <th>{{ 'sylius.ui.options'|trans }}</th>
                    <th>{{ 'sylius.ui.type'|trans }}</th>
                </tr>
            </thead>
            <tbody class="variants-accordion">
                {% for variant in product.uniqueVariantsByCode %}
                    {% include '@SyliusAdmin/Product/Show/_variantItem.html.twig' %}
                    {% include '@SyliusAdmin/Product/Show/_variantContent.html.twig' %}
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
