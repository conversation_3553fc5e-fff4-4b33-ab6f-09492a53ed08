<div id="media" class="ui styled fluid accordion">
    <div class="title">
        <i class="dropdown icon"></i>
        {{ 'sylius.ui.media'|trans }}
    </div>
    <div class="content">
        {% if product.images|length >= 1 %}
            <div class="ui small images">
                {% for image in product.images %}
                    {% if use_webpack %}
                        {% set path = image.path is not null ? image.path : asset('build/admin/images/200x200.png', 'admin') %}
                    {% else %}
                        {% set path = image.path is not null ? image.path : asset('assets/admin/img/200x200.png') %}
                    {% endif %}
                    <div class="ui bordered image">
                        {% if product.isConfigurable() and product.variants|length > 0 %}
                            {% include '@SyliusAdmin/Product/Show/_imageVariants.html.twig' %}
                        {% endif %}
                        <a href="{{ image.path }}" data-lightbox="sylius-product-image">
                            <img src="{{ path }}" data-large-thumbnail="{{ image.path }}" alt="{{ product.name }}" />
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>
