<div class="ui header">
    {% if sylius_rbac_has_permission("app_admin_order_product_names") or (variant is not empty and variant.prescriptionRequired == false) %}
        {% include '@SyliusAdmin/Product/_mainImage.html.twig' with {'product': product, 'filter': 'sylius_admin_product_tiny_thumbnail'} %}
        {% set productName = item.productName %}
        {% set variantCode = variant.code %}
        {% set variantName = item.variantName %}
    {% else %}
        {% if item.variant.product.getType() == 'consult' %}
            {% set name = "Consult (hidden)" %}
        {% elseif item.variant.product.getType() == 'medication' %}
            {% set name = "Product #{item.getId()} (hidden)" %}
        {% elseif item.variant.product.getType() == 'service' %}
            {% set name = "Service (hidden)" %}
        {% elseif variant is empty %}
            {% set name = "Medication (hidden)" %}
        {% endif %}

        {% set productName = name %}
        {% set variantCode = name %}
        {% set variantName = name %}
    {% endif %}

    <div class="content">
        <div class="sylius-product-name" title="{{ productName }}">{{ productName }}</div>
        <span class="sub header sylius-product-variant-code" title="{{ variantCode }}">
            {{ variantCode }}
        </span>
    </div>
</div>
{% if product.hasOptions() and sylius_rbac_has_permission("app_admin_order_product_names") %}
    <div class="ui horizontal divided list sylius-product-options">
        {% for optionValue in variant.optionValues %}
            <div class="item" data-sylius-option-name="{{ optionValue.name }}">
                {{ optionValue.value }}
            </div>
        {% endfor %}
    </div>
{% elseif variant is empty %}
    <div class="ui horizontal divided list">
        <div class="item sylius-product-variant-name">
            {{ variantName }}
        </div>
    </div>
{% endif %}
