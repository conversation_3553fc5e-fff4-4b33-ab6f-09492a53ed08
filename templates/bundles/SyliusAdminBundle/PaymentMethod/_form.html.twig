{% import '@SyliusUi/Macro/flags.html.twig' as flags %}

{{ form_errors(form) }}

<div class="ui segment">
    <h4 class="ui dividing header">{{ 'sylius.ui.details'|trans }}</h4>
    {{ form_errors(form) }}

    <div class="two fields">
        {{ form_row(form.code) }}
        {{ form_row(form.position) }}
    </div>
    <div class="two fields">
        {{ form_row(form.icon) }}
    </div>
    <div class="two fields">
        {{ form_row(form.enabled) }}
        {{ form_row(form.differentAddressAllowed) }}
    </div>
    <div class="two fields">
        <div class="field"></div>
        {{ form_row(form.reshipmentCostsAllowed) }}
    </div>
    {{ form_label(form.paymentMethodChannels) }}
    <div class="four fields">
        <div class="field"></div>
        <div class="field">{{ 'app.ui.payment_method.maximum_order_total'|trans }}</div>
    </div>
    {% for paymentMethodChannel in form.paymentMethodChannels %}
        <div class="four fields">
            {{ form_row(paymentMethodChannel.enabled) }}
            {{ form_row(paymentMethodChannel.maximumOrderTotal) }}
        </div>
    {% endfor %}
</div>

<div class="ui segment">
    <h4 class="ui dividing header">{{ 'sylius.ui.gateway_configuration'|trans }}</h4>

    {% if resource.gatewayConfig.factoryName == 'stripe_checkout' %}
        <div class="ui icon negative orange message sylius-flash-message">
            <i class="close icon"></i>
            <i class="warning icon"></i>
            <div class="content">
                <div class="header">
                    {{ 'sylius.ui.gateway.no_sca_support_notice'|trans }}
                </div>
            </div>
        </div>
    {% endif %}
    {% if resource.gatewayConfig.factoryName == 'paypal_express_checkout' %}
        <div class="ui icon negative orange message sylius-flash-message">
            <i class="close icon"></i>
            <i class="warning icon"></i>
            <div class="content">
                <div class="header">
                    {% autoescape false %}{{ 'sylius.ui.gateway.pay_pal_express_checkout_deprecation_notice'|trans }}{% endautoescape %}
                </div>
            </div>
        </div>
    {% endif %}

    {{ form_row(form.gatewayConfig.factoryName) }}
    {% if form.gatewayConfig.config is defined %}
        {% for field in form.gatewayConfig.config %}
            {% if loop.index is odd and not loop.last %}<div class="two fields">{% endif %}
            {{ form_row(field) }}
            {% if loop.index is even %}</div>{% endif %}
        {% endfor %}
    {% endif %}
</div>

<div class="ui styled fluid accordion">
    {% for locale, translationForm in form.translations %}
        <div class="title{% if loop.first %} active{% endif %}">
            <i class="dropdown icon"></i>
            {{ flags.fromLocaleCode(locale) }} {{ locale|sylius_locale_name }}
        </div>
        <div class="content{% if loop.first %} active{% endif %}">
            {{ form_row(translationForm.name) }}
            {{ form_row(translationForm.description) }}
            <div class="ui compact message">
                <p>
                    <i class="icon info circle"></i> {{ 'sylius.ui.the_instructions_below_will_be_displayed_to_the_customer'|trans }}.
                </p>
            </div>
            {{ form_row(translationForm.instructions) }}
        </div>
    {% endfor %}
</div>
