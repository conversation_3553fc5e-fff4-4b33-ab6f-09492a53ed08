{% import '@SyliusUi/Macro/buttons.html.twig' as buttons %}
{% import '@SyliusUi/Macro/flags.html.twig' as flags %}

<h1 class="ui header">
    <i class="circular cart icon"></i>
    <div class="content">
        {{ 'sylius.ui.order'|trans }} #{{ order.number }}
        <div class="sub header">
            <div class="ui horizontal divided list">
                <div class="item">
                    {{ order.checkoutCompletedAt|format_date }}
                </div>
                <div class="item">
                    {% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'sylius_order', 'state': order.state} %}
                    {% if order.returnRequest is not null %}
                        <span class="ui teal tag label">{{ 'sylius_plus.ui.returns.replacement'|trans }}</span>
                    {% endif %}
                </div>
                <div class="item">
                    {{ order.currencyCode }}
                </div>
                <div class="item">
                    {{ flags.fromLocaleCode(order.localeCode) }}{{ order.localeCode|sylius_locale_name }}
                </div>
            </div>
        </div>
    </div>
</h1>

{% if order.paymentState in ['awaiting_payment'] %}
    {{ buttons.default(path('sylius_shop_order_show', {'tokenValue': order.tokenValue}), 'sylius.ui.pay', null, 'credit card alternative', 'fluid blue') }}
{% endif %}

{% if order.paymentState == 'paid' and order.channel.returnRequestsAllowed == true %}
    {{ buttons.default(path('sylius_plus_return_request', {'number': order.number}), 'sylius_plus.ui.returns.request_return_or_refund', null, 'reply', 'fluid green') }}
{% endif %}
