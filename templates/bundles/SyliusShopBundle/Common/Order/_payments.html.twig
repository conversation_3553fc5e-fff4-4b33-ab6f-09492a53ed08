{% import "@SyliusShop/Common/Macro/money.html.twig" as money %}

{% set state = order.paymentState %}

{% if state != 'cart' %}
    {% include "@SyliusShop/Common/Order/Label/PaymentState/orderPaymentState.html.twig" %}
{% endif %}

{% for payment in order.payments %}
    {% set state = payment.state %}

    <div class="ui small icon message">
        <i class="payment icon"></i>
        <div class="content">
            <div class="header" id="sylius-payment-method" {{ sylius_test_html_attribute('payment-method') }}>
                {{ payment.method }}
            </div>
            <p {{ sylius_test_html_attribute('payment-price') }}>{{ money.format(payment.amount, payment.currencyCode) }}</p>
            {% if payment.amount != payment.order.total and state == 'processing' %}
                <i>{{ 'sylius.pay_pal.different_amount'|trans }}</i>
            {% endif %}
            {% if state != 'cart' %}
                <p id="payment-status" {{ sylius_test_html_attribute('payment-state') }}>
                    {% include "@SyliusShop/Common/Order/Label/PaymentState/singlePaymentState.html.twig" with { 'state': state } %}
                </p>
            {% endif %}
        </div>
    </div>
{% endfor %}
