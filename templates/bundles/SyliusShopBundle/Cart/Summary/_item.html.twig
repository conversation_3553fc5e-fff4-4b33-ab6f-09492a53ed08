{% import "@SyliusShop/Common/Macro/money.html.twig" as money %}

{% set product_variant = item.variant %}

<tr {{ sylius_test_html_attribute('cart-product-row', item.productName) }}>
    <td class="single line" {{ sylius_test_html_attribute('cart-item', loop_index|default(null) ) }}>
        {% include '@SyliusShop/Product/_info.html.twig' with {'variant': product_variant} %}
        {% if item.warnings|length > 0 %}
            <ul class="ui negative message">
                {% for warning in item.warnings %}
                    <li>{{ warning }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    </td>
    <td class="right aligned">
        {% if item.unitPrice != item.discountedUnitPrice %}
            <span class="sylius-regular-unit-price" {{ sylius_test_html_attribute('cart-product-regular-unit-price') }}>
                <span class="old-price">{{ money.convertAndFormat(item.unitPrice) }}</span>
            </span>
        {% endif %}
        <span class="sylius-unit-price" {{ sylius_test_html_attribute('cart-product-unit-price', item.productName) }}>{{ money.convertAndFormat(item.discountedUnitPrice) }}</span>
    </td>
    <td class="center aligned">
        <span class="sylius-quantity ui form">{{ form_widget(form.quantity, sylius_test_form_attribute('cart-item-quantity-input', item.productName)|sylius_merge_recursive({'attr': {'form': main_form}})) }}</span>
    </td>
    <td class="center aligned">
        <form action="{{ path('sylius_shop_cart_item_remove', {'id': item.id}) }}" method="post">
            <input type="hidden" name="_method" value="DELETE" />
            <input type="hidden" name="_csrf_token" value="{{ csrf_token(item.id) }}" />
            <button type="submit" class="ui circular icon button sylius-cart-remove-button" {{ sylius_test_html_attribute('cart-remove-button', item.productName) }} ><i class="remove icon"></i></button>
        </form>
    </td>
    <td class="right aligned">
        <span class="sylius-total" {{ sylius_test_html_attribute('cart-product-subtotal') }}>{{ money.convertAndFormat(item.subtotal) }}</span>
    </td>
</tr>
