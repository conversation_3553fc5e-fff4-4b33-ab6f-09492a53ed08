{% set variant = variant is defined ? variant : product.variants.first %}

<div class="column">
    <div id="notes">
        <h4 class="ui top attached large header">{{ 'Notes' }}</h4>
        <div class="ui attached segment">
            <table class="ui very basic celled table">
                <tbody>
                    {% if variant.note %}
                        <tr><td>{{ variant.note.message|raw }}</td></tr>
                    {% else %}
                        <tr><td>No notes available for this variant</td></tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
