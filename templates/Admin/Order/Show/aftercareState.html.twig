<div class="ui segment" id="aftercare-state">
    <span class="ui gray top attached label">
        <i class="comments icon"></i>
        {{ 'app.ui.aftercare_state'|trans }}
    </span>
    <div class="content">
        <div class="description">
            {% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'order_aftercare', 'state': order.aftercareState} %}
        </div>
    </div>
</div>
