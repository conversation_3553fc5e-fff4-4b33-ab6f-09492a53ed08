{% if sylius_rbac_has_permission('app_admin_order_notes') %}
    <h4 class="ui top attached styled header">{{ 'app.ui.note.title'|trans }}</h4>
    <div class="ui attached segment" id="order-notes">
        <table class="ui celled compact small table">
            <thead>
            <tr>
                <th class="two wide">{{ 'app.ui.note.author'|trans }}</th>
                <th class="two wide">{{ 'app.ui.note.created_at'|trans }}</th>
                <th class="twelve wide">{{ 'app.ui.note.message'|trans }}</th>
            </tr>
            </thead>
            <tbody>
                {% for orderNote in order.orderNotes %}
                    <tr>
                        <td class="two wide">{{ orderNote.authorName }}</td>
                        <td class="two wide">{{ orderNote.createdAt|date(constant('App\\Admin\\Formatting::DATETIME')) }}</td>
                        <td class="twelve wide">{{ orderNote.message|nl2br }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        {% set form = order_note_form(order) %}
        {{ form_start(form) }}
        <div class="ui form" style="margin-bottom: 1rem;">
            <div class="field">
                <label>{{ form_label(form.message) }}</label>
                {{ form_widget(form.message) }}
            </div>
        </div>
        <button type="submit" class="ui button red">{{ 'app.ui.note.add'|trans }}</button>
        {{ form_end(form) }}
    </div>
{% endif %}