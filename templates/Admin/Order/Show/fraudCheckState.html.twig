{% set canFraudCheckApprove = sm_can(order, constant('\\App\\StateMachine\\OrderFraudCheckTransitions::TRANSITION_APPROVE'), constant('\\App\\StateMachine\\OrderFraudCheckTransitions::GRAPH')) %}
{% set canFraudCheckPutOnHold = sm_can(order, constant('\\App\\StateMachine\\OrderFraudCheckTransitions::TRANSITION_ON_HOLD'), constant('\\App\\StateMachine\\OrderFraudCheckTransitions::GRAPH')) %}
{% set canFraudCheckCancel = sm_can(order, constant('\\App\\StateMachine\\OrderFraudCheckTransitions::TRANSITION_CANCEL'), constant('\\App\\StateMachine\\OrderFraudCheckTransitions::GRAPH')) %}
<div class="ui segment" id="fraud-check-state">
    <span class="ui gray top attached label">
        <i class="doctor icon"></i>
        {{ 'app.ui.fraud_check_state'|trans }}
    </span>
    <div class="content">
        <div class="description">
            {% include 'bundles/SyliusAdminBundle/Order/Label/_state.html.twig' with {'graph': 'order_fraud_check', 'state': order.fraudCheckState} %}
        </div>
        {% if sylius_rbac_has_permission('app_fraud_check_approve_orders') %}
            {% if canFraudCheckApprove or canFraudCheckPutOnHold or canFraudCheckCancel %}
                <div class="ui divider"></div>
                <div class="ui small buttons">
                    {% if canFraudCheckApprove %}
                        <form action="{{ path('app_order_fraud_check_approve', { 'id': order.id }) }}" method="POST">
                            <input type="hidden" name="_method" value="PUT">
                            <button type="submit" class="ui icon labeled tiny green  loadable button" data-requires-confirmation>
                                <i class="check icon"></i>
                                {{ 'app.ui.fraud_check.approve'|trans }}
                            </button>
                        </form>
                    {% endif %}

                    {% if canFraudCheckPutOnHold %}
                        <form action="{{ path('app_order_fraud_check_hold', { 'id': order.id }) }}" method="POST">
                            <input type="hidden" name="_method" value="PUT">
                            <button type="submit" class="ui icon labeled tiny yellow  loadable button" data-requires-confirmation>
                                <i class="clock icon"></i>
                                {{ 'app.ui.fraud_check.hold'|trans }}
                            </button>
                        </form>
                    {% endif %}

                    {% if canFraudCheckCancel %}
                        <form action="{{ path('app_order_fraud_check_cancel', { 'id': order.id }) }}" method="POST">
                            <input type="hidden" name="_method" value="PUT">
                            <button type="submit" class="ui icon labeled tiny red  loadable button" data-requires-confirmation>
                                <i class="ban icon"></i>
                                {{ 'app.ui.fraud_check.cancel'|trans }}
                            </button>
                        </form>
                    {% endif %}
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>
