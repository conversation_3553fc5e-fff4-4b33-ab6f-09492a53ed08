{% from '@SyliusAdmin/Macro/translationForm.html.twig' import translationForm %}

<div class="ui column stackable grid">
    <div class="column">
        <div class="ui segment">
            {{ form_errors(form) }}
            {{ form_row(form.name) }}
            {{ form_row(form.identifier) }}
            {{ form_row(form.enabled) }}
            {{ form_row(form.handlingFee) }}
            {{ form_row(form.dailyOrderLimit) }}
            <h4 class="ui dividing header">Country shipping</h4>
            {% for countryShipping in form.supplierCountriesShipping %}
                <div class="ui segment">
                    <h5>{{ form_label(countryShipping) }}</h5>
                    {{ form_widget(countryShipping) }}
                </div>
            {% endfor %}
            <h4 class="ui dividing header">Doctor registration numbers</h4>
            {{ form_row(form.supplierDoctors) }}
        </div>
    </div>
</div>
