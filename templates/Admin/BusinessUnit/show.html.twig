{% extends '@SyliusAdmin/layout.html.twig' %}

{% import '@SyliusUi/Macro/buttons.html.twig' as buttons %}

{% block title %}{{ 'sylius.ui.business_unit'|trans ~ ' ' ~ business_unit.id}} {{ parent() }}{% endblock %}

{% block content %}
    <div class="ui stackable two column grid">
        <div class="ten wide column">
            {% include 'Admin/BusinessUnit/Show/_header.html.twig' %}
        </div>
        <div class="six wide right aligned column">
            {% set path = path('app_admin_business_unit_index') %}
            {{ buttons.default(path, '', 'back', 'arrow alternate circle left outline') }}
        </div>
    </div>

    {% include 'Admin/BusinessUnit/Show/_breadcrumb.html.twig' %}

    <div class="ui stackable segment grid">
        <div class="twelve wide column">
            <table class="ui celled compact small table fixed">
                <thead>
                <tr>
                    <th class="four wide sylius-table-column-name">{{ 'sylius.ui.company'|trans }}</th>
                    <th class="four wide sylius-table-column-name">{{ 'sylius.ui.representative'|trans }}</th>
                    <th class="four wide sylius-table-column-name">{{ 'sylius.ui.order_number_prefix'|trans }}</th>
                    <th class="four wide sylius-table-column-name">{{ 'sylius.ui.tax_id'|trans }}</th>
                </tr>
                </thead>
                <tbody>
                    <tr>
                        <td id="business_unit_companyName" class="single line line-item-name">
                            {{ business_unit.companyName }}
                        </td>
                        <td id="business_unit_representative" class="single line line-item-code">
                            {{ business_unit.representative }}
                        </td>
                        <td id="business_unit_order_number_prefix" class="single line line-item-order-number-prefix">
                            {{ business_unit.orderNumberPrefix }}
                        </td>
                        <td id="business_unit_taxId" class="right aligned line-item-unit-price">
                            {{ business_unit.taxId }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="four wide column">
            {% include 'Admin/BusinessUnit/Show/_address.html.twig' %}
        </div>
    </div>
{% endblock %}
