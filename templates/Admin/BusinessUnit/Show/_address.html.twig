{% set address = business_unit.address %}

<h4 class="ui top attached styled header">
    {{ 'sylius.ui.address'|trans }}
</h4>
<div class="ui attached segment" id="business_unit_address">
    <address>
        {% if address.street %}
            <b>{{ 'sylius.ui.street'|trans }}</b> {{ address.street }}<br/>
        {% endif %}
        {% if address.city %}
            <b>{{ 'sylius.ui.city'|trans }}</b> {{ address.city }}<br/>
        {% endif %}
        {% if address.countryCode %}
            <i class="{{ address.countryCode|lower }} flag"></i>
            {{ address.countryCode|sylius_country_name|upper }}
        {% endif %}
        {% if address.postcode %}
            {{ address.postcode }}
        {% endif %}
    </address>
</div>
