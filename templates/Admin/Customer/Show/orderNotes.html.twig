{% if sylius_rbac_has_permission('app_admin_order_notes') %}
    <div class="ui stackable grid">
        <div class="sixteen wide column">
            <h4 class="ui top attached styled header">{{ 'app.ui.note.title'|trans }}</h4>
            <div class="ui attached segment" id="order-notes">
                <table class="ui celled compact small table">
                    <thead>
                    <tr>
                        <th class="two wide">{{ 'app.ui.note.author'|trans }}</th>
                        <th class="two wide">{{ 'app.ui.note.created_at'|trans }}</th>
                        <th class="two wide">{{ 'app.ui.note.order'|trans }}</th>
                        <th class="ten wide">{{ 'app.ui.note.message'|trans }}</th>
                    </tr>
                    </thead>
                    <tbody>
                        {% set notes = customer_order_notes(customer) %}
                        {% for note in notes %}
                            <tr>
                                <td class="two wide">{{ note.authorName }}</td>
                                <td class="two wide">{{ note.createdAt|date(constant('App\\Admin\\Formatting::DATETIME')) }}</td>
                                <td class="two wide"><a href="{{ path('sylius_admin_order_show', {'id': note.order.id}) }}">#{{ note.order.number }}</a></td>
                                <td class="ten wide">{{ note.message|nl2br }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endif %}
