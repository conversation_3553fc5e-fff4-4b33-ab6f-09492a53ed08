{% if sylius_rbac_has_permission('app_customer_fraud_check_trust_level') %}
    <div id="fraudcheck-info" class="ui stackable grid">
        <div class="eight wide column">
            <h4 class="ui top attached styled header">
                {{ 'app.ui.fraud_check.singular'|trans }}
            </h4>
            <div class="ui attached segment small statistic">
                {% set TrustLevel = enum('App\\Entity\\Customer\\TrustLevel') %}
                <div class="description">
                    <label>Currently:</label>
                    {% include 'Admin/Customer/_trust_level.html.twig' with { customer: customer} %}
                </div>

                <div class="ui divider"></div>
                <div class="ui small buttons">
                    {% if customer.trustLevel != TrustLevel.Trusted %}
                        <form action="{{ path('app_customer_fraud_check_trust_level', { 'id': customer.id, 'trustLevel': TrustLevel.Trusted.value }) }}"
                              method="POST">
                            <button type="submit" class="ui icon labeled tiny green fluid loadable button"
                                    data-requires-confirmation>
                                <i class="check icon"></i>
                                {{ 'app.ui.fraud_check.trusted'|trans }}
                            </button>
                        </form>
                    {% endif %}

                    {% if customer.trustLevel != TrustLevel.Neutral %}
                        <form action="{{ path('app_customer_fraud_check_trust_level', { 'id': customer.id, 'trustLevel': TrustLevel.Neutral.value }) }}"
                              method="POST">
                            <button type="submit" class="ui icon labeled tiny gray fluid loadable button"
                                    data-requires-confirmation>
                                <i class="question circle outline icon"></i>
                                {{ 'app.ui.fraud_check.neutral'|trans }}
                            </button>
                        </form>
                    {% endif %}

                    {% if customer.trustLevel != TrustLevel.Fraudulent %}
                        <form action="{{ path('app_customer_fraud_check_trust_level', { 'id': customer.id, 'trustLevel': TrustLevel.Fraudulent.value }) }}"
                              method="POST">
                            <button type="submit" class="ui icon labeled tiny red fluid loadable button"
                                    data-requires-confirmation>
                                <i class="ban icon"></i>
                                {{ 'app.ui.fraud_check.fraudulent'|trans }}
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    </div>
{% endif %}
