{% set TrustLevel = enum('App\\Entity\\Customer\\TrustLevel') %}
{% if customer.trustLevel == TrustLevel.Neutral %}
    <span class="ui gray label">
        <i class="question circle outline icon"></i>
        <span>{{ customer.trustLevel.value }}</span>
    </span>
{% elseif customer.trustLevel == TrustLevel.Fraudulent %}
    <span class="ui red label">
        <i class="ban icon"></i>
        <span>{{ customer.trustLevel.value }}</span>
    </span>
{% elseif customer.trustLevel == TrustLevel.Trusted %}
    <span class="ui green label">
        <i class="check icon"></i>
        <span>{{ customer.trustLevel.value }}</span>
    </span>
{% endif %}
