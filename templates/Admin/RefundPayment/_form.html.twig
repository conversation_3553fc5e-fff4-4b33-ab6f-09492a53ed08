{% import '@SyliusAdmin/Common/Macro/money.html.twig' as money %}
{% from '@SyliusAdmin/Macro/translationForm.html.twig' import translationForm %}

<!--BEGIN BLOCK templates/Admin/RefundPayment/_form.html.twig -->
<div class="ui grid">
    <div class="wide mobile ten wide computer column">
        <h4 class="ui top attached large header">Action</h4>
        <div class="ui attached segment">
            {{ form_row(form.state) }}
            {{ form_row(form.note) }}
        </div>
    </div>
    <div class="wide mobile six wide computer column">
        <h4 class="ui top attached large header">Details</h4>
        <div class="ui attached segment">
            {{ form_errors(form) }}
            <div class="field">
                <label><b>{{ 'sylius.ui.order'|trans }}:</b></label>
                {% include "@SyliusAdmin/Order/Grid/Field/number.html.twig" with {data: {id: refund_payment.order.id, number: refund_payment.order.number}} %}
            </div>
            <div class="field">
                <label><b>{{ 'sylius.ui.amount'|trans }}:</b></label>
                {% include "Admin/RefundPayment/Grid/amount.html.twig" with {data: {amount: refund_payment.amount, currencyCode: refund_payment.currencyCode}} %}
            </div>
            <div class="field">
                <label><b>{{ 'sylius.ui.payments'|trans }}:</b></label>
                {% for payment in refund_payment.order.payments %}
                    {{ 'app.ui.payment.id.singular'|trans }} #{{ payment.id }}:

                    {% include "Admin/RefundPayment/Grid/amount.html.twig" with {data: {amount: payment.amount, currencyCode: payment.currencyCode}} %}

                    {{ payment.method }}
                    ({{ payment.paymentServiceProviderName }})

                    {% if not loop.last %}<br />{% endif %}
                {% endfor %}
            </div>
            {% if refund_payment.reason %}
                <div class="field">
                    <label><b>{{ 'app.ui.refund.payment.reason'|trans }}:</b></label>
                    <p>{{ refund_payment.reason.value }}</p>
                </div>
            {% endif %}
            {% if refund_payment.order.cancellation.by.value %}
                <div class="field">
                    <label><b>{{ 'app.ui.refund.cancellation.by'|trans }}:</b></label>
                    <p>{{ refund_payment.order.cancellation.by.value }}</p>
                </div>
            {% endif %}
            {% if refund_payment.order.cancellation.reason %}
                <div class="field">
                    <label><b>{{ 'app.ui.refund.cancellation.reason'|trans }}:</b></label>
                    <p>{{ refund_payment.order.cancellation.reason|cancellationReasonTrans }}</p>
                </div>
            {% endif %}
            <div class="field">
                <label><b>{{ 'sylius.ui.created_at'|trans }}:</b></label>
                {{ refund_payment.createdAt|date(constant('App\\Admin\\Formatting::DATETIME')) }}
            </div>
        </div>
    </div>
</div>
<!--END BLOCK templates/Admin/RefundPayment/_form.html.twig -->
