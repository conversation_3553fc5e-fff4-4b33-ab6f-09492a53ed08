{# Based on @SyliusAdmin/Common/Label/paymentState.html.twig #}
{%
    set viewOptions = {
        completed: { icon: 'check', color: 'green' },
        declined: { icon: 'ban', color: 'red' },
        new: { icon: 'clock', color: 'olive' },
    }
%}

{% set value = 'sylius.ui.' ~ data %}

<span class="ui {{ viewOptions[data]['color'] }} label">
    <i class="{{ viewOptions[data]['icon'] }} icon"></i>
    {{ value|trans }}
</span>
