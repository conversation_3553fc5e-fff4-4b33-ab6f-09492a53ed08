<div class="ui segment" style="overflow-x: auto;">
    <h3 class="ui dividing header" id="audit-entries">Audit logs</h3>
    {% for auditEntry in auditObject.auditEntries %}
        <div class="ui grid" style="margin: 0.5rem 0 0 0;">
            <div class="one wide icon-wrapper" style="padding: 0;">
                <i class="circular icon {{ auditEntry.actionIcon }}"></i>
            </div>
            <div class="fifteen wide" style="padding: 0.25rem 0;">
                <span style="font-weight: 700; padding-left: 0.5rem;">{{ auditEntry.username ? auditEntry.username : auditEntry.userType.value }}</span>
                <span style="color: #9a9a9a">
                    {{ auditEntry.messageWithInterpolatedContext }} - <relative-time datetime="{{ auditEntry.createdAt|format_datetime }}">{{ auditEntry.createdAt|format_datetime(pattern="dd-MM-YYYY HH:MM") }}</relative-time>
                </span>
            </div>
        </div>
    {% endfor %}
    {% if auditObject.auditEntries is empty %}
        <p>There are no entries to show</p>
    {% endif %}
</div>
