<!DOCTYPE html>
<html>
    <head>
        {% block preconnect %}
        {% endblock %}
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}{% endblock %}</title>
        {% block gtm_script %}
            {% if GTM_ID is defined and GTM_ID is not empty %}
                <!-- Google Tag Manager -->
                <script>(function (w, d, s, l, i) {
                        w[l] = w[l] || [];
                        w[l].push({
                            'gtm.start':

                                new Date().getTime(), event: 'gtm.js'
                        });
                        var f = d.getElementsByTagName(s)[0],

                            j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
                        j.async = true;
                        j.src =

                            'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                        f.parentNode.insertBefore(j, f);

                    })(window, document, 'script', 'dataLayer', '{{ GTM_ID }}');
                </script>
            <!-- End Google Tag Manager -->
            {% endif %}
        {% endblock %}
        {% block fonts %}
        {% endblock %}
        {% block favicon %}
        {% endblock %}
        {% block stylesheets %}{% endblock %}
    </head>
    <body>
        {% block gtm_noscript %}
            {% if GTM_ID is defined and GTM_ID is not empty %}
                <!-- Google Tag Manager (noscript) -->
                <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-{{ GTM_ID }}}}"
                height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
                <!-- End Google Tag Manager (noscript) -->
            {% endif %}
        {% endblock %}
        {% block body %}{% endblock %}
        {% block javascripts %}{% endblock %}
    </body>
</html>
