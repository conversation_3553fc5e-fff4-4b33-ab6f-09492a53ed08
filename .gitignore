# General ignores
/.idea/
.DS_Store
/bin
!/bin/console
/private/invoices/*

/config/secrets/**/*.decrypt.private.php
/config/jwt/**/*.key

# Sylius ignores
/public/assets
/public/css
/public/js
/public/media
!/public/media/image/.gitkeep

# Frontend
/node_modules
/assets/translations/**/*.json

# Editor
.vscode

###> Cypress ###
/cypress/screenshots
/cypress/videos
###< Cypress ###

###> symfony/framework-bundle ###
/.env.*.local
/.env.local
/.env.local.php
/public/bundles
/var/
/vendor/
###< symfony/framework-bundle ###

###> lexik/jwt-authentication-bundle ###
/config/jwt/*.pem
/config/jwt/prod/*.pem
/config/jwt/accept/*.pem
/config/jwt/test/*.pem
!/config/jwt/*-test.pem
###< lexik/jwt-authentication-bundle ###

###> symfony/phpunit-bridge ###
/coverage/
.phpunit
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> friendsofphp/php-cs-fixer ###
/.php_cs
/.php_cs.cache
###< friendsofphp/php-cs-fixer ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

/.helm/charts/*.tgz

###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

Makefile.local
Makefile.*.local
/supervisord.pid

/.php-cs-fixer.cache

###> friends-of-behat/symfony-extension ###
/behat.yml
###< friends-of-behat/symfony-extension ###
/docker-compose.xdebug.yml
