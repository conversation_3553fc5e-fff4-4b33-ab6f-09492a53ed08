APP_DEBUG=1
APP_ENV=seeme_dev

# See \App\Tests\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^(.*)$'

DATABASE_HOST=database
DATABASE_NAME='checkout_service_seeme'
DATABASE_HOST_READONLY=database
DATABASE_NAME_READONLY='checkout_service_seeme'

# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN_APP_URL=https://seemenopause.commerce.ehvg.dev

JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/seeme_dev/public.pem
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/seeme_dev/private.pem

AUTH0_DOMAIN=development-ehvg.eu.auth0.com
AUTH0_AUDIENCE='["api://commerce-system.see-me"]'

SUPPLIER_SERVICE_API_BASE_URI=https://pharmacies.ehvg.dev/api/
SUPPLIER_SERVICE_AUTH0_AUDIENCE=api://supplier-service

ANAMNESIS_SERVICE_API_BASE_URI=https://seemenopause.anamnesis-service.ehvg.dev/api/
ANAMNESIS_SERVICE_AUTH0_AUDIENCE=api://anamnesis-system.see-me

CONSULT_SYSTEM_ENABLED=1
CONSULT_SYSTEM_API_BASE_URI=https://consult.ehvg.dev/api/
CONSULT_SYSTEM_AUTH0_AUDIENCE=api://consult-system

COMMUNICATION_SYSTEM_API_BASE_URI=https://communication.ehvg.dev/api/

CHECKOUT_SERVICE_API_HOST=seemenopause.commerce.ehvg.dev
DOKTERONLINE_API_BASE_URI=http://dokkie
FIXER_IO_API_KEY=null
MESSENGER_TRANSPORT_DSN=doctrine://default
REDIS_HOST=redis
RESHIPMENT_PAYMENT_EXPIRES_IN_SECONDS=600
SHOP_CONFIGURATION_DIRECTORY=seeme
SYLIUS_CHANNEL_PREFIX=seeme

PUBLIC_STORAGE_BUCKET=commerce.seemenopause.public.s3.ehvg.dev

AUTH0_COMMERCE_SYSTEM_RWA_AUDIENCE=internal://commerce-system.seeme
AUTH0_COMMERCE_SYSTEM_RWA_CLIENT_ORGANIZATION=org_qKJbWtfQOIBJ9KRO

RABBITMQ_HOST=amqp://rabbitmq
RABBITMQ_USERNAME=rabbitmq
RABBITMQ_PASSWORD=rabbitmq
RABBITMQ_PORT=5672

BUSINESS_UNIT_SEEME_API_HOSTNAME=seemenopause.commerce.ehvg.dev

# CanopyDeploy Webhook URL's SeeMe-NoPause
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_ORDER=161-test_webhook_dev_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_CUSTOMER=160-test_webhook_dev_01_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_PASSWORD_RESET=162-test_webhook_dev_02_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_REMOVE_ORDER_ITEM=163-test_webhook_dev_03_smnp
CANOPY_DEPLOY_SEEME_WEBHOOK_URL_PRODUCT_BACK_IN_STOCK=
