<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Currency;

use App\Catalog\Currency\CostPriceCurrencyConverter;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Model\ExchangeRateInterface;
use Sylius\Component\Currency\Repository\ExchangeRateRepositoryInterface;

class CostPriceCurrencyConverterTest extends TestCase
{
    private ExchangeRateRepositoryInterface $exchangeRateRepository;
    private CostPriceCurrencyConverter $converter;

    protected function setUp(): void
    {
        $this->exchangeRateRepository = $this->createMock(ExchangeRateRepositoryInterface::class);
        $this->converter = new CostPriceCurrencyConverter($this->exchangeRateRepository);
    }

    public function testConvertWithSameCurrency(): void
    {
        $result = $this->converter->convert(1000, 'EUR', 'EUR');

        $this->assertSame(1000, $result);
    }

    public function testConvertWithDirectExchangeRate(): void
    {
        $exchangeRate = $this->createMock(ExchangeRateInterface::class);
        $exchangeRate->method('getRatio')->willReturn(1.2);

        $this->exchangeRateRepository
            ->method('findOneWithCurrencyPair')
            ->with('USD', 'EUR')
            ->willReturn($exchangeRate);

        $result = $this->converter->convert(1000, 'USD', 'EUR');

        $this->assertSame(1200, $result);
    }

    public function testConvertWithReverseExchangeRate(): void
    {
        $exchangeRate = $this->createMock(ExchangeRateInterface::class);
        $exchangeRate->method('getRatio')->willReturn(0.5903); // EUR to GBP

        $this->exchangeRateRepository
            ->method('findOneWithCurrencyPair')
            ->willReturnMap([
                ['GBP', 'EUR', null], // No direct rate
                ['EUR', 'GBP', $exchangeRate], // Reverse rate found
            ]);

        $result = $this->converter->convert(2265, 'GBP', 'EUR');

        // 2265 * (1 / 0.5903) ≈ 2265 * 1.6944 ≈ 3837 (rounded)
        $this->assertSame(3837, $result);
    }

    public function testConvertWithNoExchangeRate(): void
    {
        $this->exchangeRateRepository
            ->method('findOneWithCurrencyPair')
            ->willReturn(null);

        $result = $this->converter->convert(1000, 'USD', 'EUR');

        // Should return original value when no exchange rate is found
        $this->assertSame(1000, $result);
    }
}
