<?xml version="1.0"?>
<ruleset name="PHP Mess Detector ruleset"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0
                     http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation="
                     http://pmd.sf.net/ruleset_xml_schema.xsd">
    <description>
        EHVG Ruleset
    </description>

    <rule ref="rulesets/cleancode.xml">
        <exclude name="StaticAccess" />
    </rule>

    <rule ref="rulesets/codesize.xml">
        <exclude name="ExcessiveParameterList" />
    </rule>

    <rule ref="rulesets/controversial.xml" />

    <rule ref="rulesets/design.xml" />

    <rule ref="rulesets/naming.xml">
        <exclude name="ShortVariable" />
        <exclude name="ShortMethodName" />
    </rule>

    <rule ref="rulesets/naming.xml/ShortVariable">
        <properties>
            <property name="exceptions" value="id,io" />
        </properties>
    </rule>
    <rule ref="rulesets/naming.xml/ShortMethodName">
        <properties>
            <property name="exceptions" value="up,down" />
        </properties>
    </rule>

    <rule ref="rulesets/unusedcode.xml">
        <!--
        This UnusedFormalParameter rule does not support exceptions like other rules, and PHPStan enforces this.
        We need an exception for `$schema` in Migrations since the abstract function requires the parameter but it's not
        required to use the $schema in the migration.
         -->
        <exclude name="UnusedFormalParameter" />
    </rule>
</ruleset>
