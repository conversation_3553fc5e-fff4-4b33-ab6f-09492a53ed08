APP_ENV=accept

DATABASE_HOST=checkout-service-accept.csv7sodpridp.eu-central-1.rds.amazonaws.com
DATABASE_NAME=checkout-service
DATABASE_HOST_READONLY=checkout-service-accept.csv7sodpridp.eu-central-1.rds.amazonaws.com
DATABASE_NAME_READONLY=checkout-service

# Use only in shop config; URL will be fetched from the business unit
FRONTEND_FOLLOW_UP_APP_URL=https://acceptance--blueclinic.netlify.app
# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN_APP_URL=https://acceptance--dokteronline.netlify.app
# Use only in shop config; URL will be fetched from the business unit
FRONTEND_MAIN2_APP_URL=https://acceptance--doctoronline-uk.netlify.app

JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/accept/public.pem
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/accept/private.pem

# Current Auth0 tenant
AUTH0_DOMAIN=auth.sbaccept.nl
AUTH0_AUDIENCE='["api://commerce-system.dokteronline"]'

SUPPLIER_SERVICE_API_BASE_URI=https://pharmacies.sbaccept.nl/api/
SUPPLIER_SERVICE_AUTH0_AUDIENCE=api://supplier-service

ANAMNESIS_SERVICE_API_BASE_URI=https://anamnesis-service.sbaccept.nl/api/
ANAMNESIS_SERVICE_AUTH0_AUDIENCE=api://anamnesis-system.dokteronline

DOKTERONLINE_API_BASE_URI=https://dokteronline.sbaccept.nl
DOKTERONLINE_AUTH0_AUDIENCE=api://dok-beheer

CONSULT_SYSTEM_API_BASE_URI=https://consult.sbaccept.nl/api/
CONSULT_SYSTEM_AUTH0_AUDIENCE=api://consult-system

COMMUNICATION_SYSTEM_API_BASE_URI=https://communication.sbaccept.nl/api/

CHECKOUT_SERVICE_API_HOST=dokteronline.commerce.sbaccept.nl
FIXER_IO_API_BASE_URI=http://data.fixer.io/api
MESSENGER_TRANSPORT_DSN=checkout-service-accept.4oibn8.ng.0001.euc1.cache.amazonaws.com:6379/messages
REDIS_HOST=checkout-service-accept.4oibn8.ng.0001.euc1.cache.amazonaws.com
RESHIPMENT_PAYMENT_EXPIRES_IN_SECONDS=600

# See \App\Tests\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/((imperium-)?acceptance--((anamnesis-admin-)?dokteronline|blueclinic|doctoronline-uk)\.netlify\.app|consult\.sbaccept\.nl)$'

SHOP_CONFIGURATION_DIRECTORY=dokteronline
SYLIUS_CHANNEL_PREFIX=dok

PUBLIC_STORAGE_BUCKET=commerce.dokteronline.public.s3.sbaccept.nl

AUTH0_COMMERCE_SYSTEM_RWA_AUDIENCE=internal://commerce-system.dokteronline
AUTH0_COMMERCE_SYSTEM_RWA_CLIENT_ORGANIZATION=org_YN2gJ0DlnJRmia2A

KLARNA_BASE_URI=https://api.playground.klarna.com
PAYPAL_BASE_URI=https://api-m.sandbox.paypal.com
CM_BASE_URI=https://testsecure.docdatapayments.com

BUSINESS_UNIT_DOKTERONLINE_API_HOSTNAME=dokteronline.commerce.sbaccept.nl
BUSINESS_UNIT_DOCTORONLINE_API_HOSTNAME=doctoronline.commerce.sbaccept.nl
BUSINESS_UNIT_BLUECLINIC_API_HOSTNAME=blueclinic.commerce.sbaccept.nl

# CanopyDeploy (production) Webhook URL's Doctoronline
CANOPY_DEPLOY_DOCTORONLINE_WEBHOOK_URL_ORDER=271-backend_order
CANOPY_DEPLOY_DOCTORONLINE_WEBHOOK_URL_CUSTOMER=272-backend_customer
CANOPY_DEPLOY_DOCTORONLINE_WEBHOOK_URL_PASSWORD_RESET=273-backend_password_reset
CANOPY_DEPLOY_DOCTORONLINE_WEBHOOK_URL_REMOVE_ORDER_ITEM=269-backend_remove_orderitems
CANOPY_DEPLOY_DOCTORONLINE_WEBHOOK_URL_PRODUCT_BACK_IN_STOCK=
SMARTY_ENABLED=0

AUTO_CAPTURE_DELAY_PERIOD="15 minutes"
