<?php

declare(strict_types=1);

namespace App\Catalog\Currency;

use <PERSON><PERSON><PERSON>\Component\Currency\Converter\CurrencyConverterInterface;
use Sylius\Component\Currency\Repository\ExchangeRateRepositoryInterface;

final class CostPriceCurrencyConverter implements CurrencyConverterInterface
{
    public function __construct(
        private readonly ExchangeRateRepositoryInterface $exchangeRateRepository,
    ) {
    }

    public function convert(int $value, string $sourceCurrencyCode, string $targetCurrencyCode): int
    {
        if ($sourceCurrencyCode === $targetCurrencyCode) {
            return $value;
        }

        // First try to find a direct exchange rate from source to target
        $exchangeRate = $this->exchangeRateRepository->findOneWithCurrencyPair(
            $sourceCurrencyCode,
            $targetCurrencyCode
        );

        if ($exchangeRate !== null) {
            return (int) ceil($value * $exchangeRate->getRatio());
        }

        // If no direct rate found, try the reverse direction
        $reverseExchangeRate = $this->exchangeRateRepository->findOneWithCurrencyPair(
            $targetCurrencyCode,
            $sourceCurrencyCode
        );

        if ($reverseExchangeRate !== null) {
            // Use the inverse of the reverse rate
            $inverseRatio = 1.0 / $reverseExchangeRate->getRatio();
            return (int) ceil($value * $inverseRatio);
        }

        // If no exchange rate found in either direction, return the original value
        // This maintains backward compatibility
        return $value;
    }
}
