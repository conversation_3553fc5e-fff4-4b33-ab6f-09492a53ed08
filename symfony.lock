{"alcohol/iso4217": {"version": "4.0.0"}, "api-platform/core": {"version": "2.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "05b57782a78c21a664a42055dc11cf1954ca36bb"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/Entity/.gitignore"]}, "babdev/pagerfanta-bundle": {"version": "v2.11.0"}, "behat/transliterator": {"version": "v1.5.0"}, "clue/stream-filter": {"version": "v1.6.0"}, "composer/pcre": {"version": "1.0.1"}, "composer/semver": {"version": "3.3.2"}, "composer/xdebug-handler": {"version": "2.0.5"}, "dama/doctrine-test-bundle": {"version": "8.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.2", "ref": "896306d79d4ee143af9eadf9b09fd34a8c391b70"}, "files": ["config/packages/dama_doctrine_test_bundle.yaml"]}, "doctrine/annotations": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "2.2.0"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/common": {"version": "3.3.0"}, "doctrine/data-fixtures": {"version": "1.5.3"}, "doctrine/dbal": {"version": "2.13.9"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "ddddd8249dd55bbda16fa7a45bb7499ef6f8e90e"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.4"}, "doctrine/instantiator": {"version": "1.4.1"}, "doctrine/lexer": {"version": "1.2.3"}, "doctrine/migrations": {"version": "3.4.2"}, "doctrine/orm": {"version": "2.12.2"}, "doctrine/persistence": {"version": "2.5.3"}, "doctrine/sql-formatter": {"version": "1.1.2"}, "egulias/email-validator": {"version": "3.2"}, "fakerphp/faker": {"version": "v1.19.0"}, "fig/link-util": {"version": "1.2.0"}, "friends-of-behat/symfony-extension": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "1e012e04f573524ca83795cd19df9ea690adb604"}, "files": ["behat.yml.dist", "config/services_test.yaml", "features/demo.feature", "tests/Behat/DemoContext.php"]}, "friendsofphp/php-cs-fixer": {"version": "2.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.19", "ref": "34d1a22f840953909c581b8f993093b14cc9491b"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.12"}, "friendsofsymfony/rest-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.2", "ref": "fa845143b7e0a4c70aedd1a88c549e6d977e9ae5"}}, "gedmo/doctrine-extensions": {"version": "v3.7.0"}, "guzzlehttp/guzzle": {"version": "6.5.6"}, "guzzlehttp/promises": {"version": "1.5.1"}, "guzzlehttp/psr7": {"version": "1.8.5"}, "hamcrest/hamcrest-php": {"version": "v2.0.1"}, "hwi/oauth-bundle": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "20154480d558409ad3eb9de3644817c81fad2268"}, "files": ["config/packages/hwi_oauth.yaml", "config/routes/hwi_oauth_routing.yaml"]}, "imagine/imagine": {"version": "1.3.2"}, "jms/metadata": {"version": "2.6.1"}, "jms/serializer": {"version": "3.17.1"}, "jms/serializer-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "384cec52df45f3bfd46a09930d6960a58872b268"}}, "justinrainbow/json-schema": {"version": "5.2.12"}, "knplabs/gaufrette": {"version": "v0.8.3"}, "knplabs/knp-gaufrette-bundle": {"version": "v0.7.1"}, "knplabs/knp-menu": {"version": "v3.3.0"}, "knplabs/knp-menu-bundle": {"version": "v3.2.0"}, "knplabs/knp-snappy": {"version": "v1.4.1"}, "knplabs/knp-snappy-bundle": {"version": "1.9", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.5", "ref": "c81bdcf4a9d4e7b1959071457f9608631865d381"}}, "laminas/laminas-code": {"version": "4.5.1"}, "laminas/laminas-stdlib": {"version": "3.7.1"}, "lcobucci/clock": {"version": "2.2.0"}, "lcobucci/jwt": {"version": "4.1.5"}, "league/flysystem-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "913dc3d7a5a1af0d2b044c5ac3a16e2f851d7380"}, "files": ["config/packages/flysystem.yaml", "var/storage/.gitignore"]}, "league/uri": {"version": "6.6.0"}, "league/uri-components": {"version": "2.4.1"}, "league/uri-interfaces": {"version": "2.3.0"}, "lexik/jwt-authentication-bundle": {"version": "2.15", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "5b2157bcd5778166a5696e42f552ad36529a07a6"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "liip/imagine-bundle": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.8", "ref": "d1227d002b70d1a1f941d91845fcd7ac7fbfc929"}}, "marcj/topsort": {"version": "1.1.0"}, "mockery/mockery": {"version": "1.5.0"}, "monolog/monolog": {"version": "2.6.0"}, "myclabs/deep-copy": {"version": "1.11.0"}, "myclabs/php-enum": {"version": "1.8.3"}, "namshi/jose": {"version": "7.2.3"}, "nelmio/cors-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nijens/openapi-bundle": {"version": "1.2.3"}, "nikic/php-parser": {"version": "v4.14.0"}, "nyholm/psr7": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "4a8c0345442dcca1d8a2c65633dcf0285dd5a5a2"}, "files": ["config/packages/nyholm_psr7.yaml"]}, "oneup/flysystem-bundle": {"version": "4.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "3ae1b83985e89138f5443bbc2d9b8c074b497d49"}, "files": ["config/packages/oneup_flysystem.yaml"]}, "pagerfanta/pagerfanta": {"version": "v2.7.3"}, "paragonie/random_compat": {"version": "v9.99.100"}, "payum/iso4217": {"version": "1.0.2"}, "payum/payum-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.4", "ref": "518ac22defa04a8a1d82479ed362e2921487adf0"}}, "phar-io/manifest": {"version": "2.0.3"}, "phar-io/version": {"version": "3.2.1"}, "php-cs-fixer/diff": {"version": "v1.3.1"}, "php-http/discovery": {"version": "1.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "php-http/httplug": {"version": "2.3.0"}, "php-http/message": {"version": "1.13.0"}, "php-http/message-factory": {"version": "v1.0.2"}, "php-http/promise": {"version": "1.1.0"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.3.0"}, "phpdocumentor/type-resolver": {"version": "1.6.1"}, "phpspec/prophecy": {"version": "v1.15.0"}, "phpstan/extension-installer": {"version": "1.1.0"}, "phpstan/phpdoc-parser": {"version": "1.5.1"}, "phpstan/phpstan": {"version": "0.12.99"}, "phpstan/phpstan-symfony": {"version": "0.12.44"}, "phpunit/php-code-coverage": {"version": "7.0.15"}, "phpunit/php-file-iterator": {"version": "2.0.5"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "1.2.1"}, "phpunit/php-timer": {"version": "2.1.3"}, "phpunit/phpunit": {"version": "8.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.7", "ref": "b5b34fa22319ac1f7f6c180b30e160841c154a1e"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "polishsymfonycommunity/symfony-mocker-container": {"version": "v1.0.4"}, "psr/cache": {"version": "2.0.0"}, "psr/container": {"version": "1.1.2"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "2.0.1"}, "psr/log": {"version": "2.0.0"}, "psr/simple-cache": {"version": "1.0.1"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ramsey/uuid": {"version": "3.9.6"}, "ramsey/uuid-doctrine": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "471aed0fbf5620b8d7f92b7a5ebbbf6c0945c27a"}, "files": ["config/packages/ramsey_uuid_doctrine.yaml"]}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "1.0.2"}, "sebastian/comparator": {"version": "3.0.3"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "3.0.3"}, "sebastian/environment": {"version": "4.2.4"}, "sebastian/exporter": {"version": "3.1.4"}, "sebastian/global-state": {"version": "3.0.2"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "3.0.4"}, "sebastian/object-reflector": {"version": "1.1.2"}, "sebastian/recursion-context": {"version": "3.0.1"}, "sebastian/resource-operations": {"version": "2.0.2"}, "sebastian/type": {"version": "1.1.4"}, "sebastian/version": {"version": "2.0.1"}, "seld/jsonlint": {"version": "1.9.0"}, "sensio/framework-extra-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "sentry/sentry-symfony": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "3d8dc8c0f689e2e94f860d2e36f81fcc26348069"}, "files": ["config/packages/sentry.yaml"]}, "sonata-project/block-bundle": {"version": "4.13", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.11", "ref": "b4edd2a1e6ac1827202f336cac2771cb529de542"}}, "sonata-project/form-extensions": {"version": "1.16", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.4", "ref": "9c8a1e8ce2b1f215015ed16652c4ed18eb5867fd"}}, "stella-maris/clock": {"version": "0.1.4"}, "stof/doctrine-extensions-bundle": {"version": "1.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "e805aba9eff5372e2d149a9ff56566769e22819d"}}, "superbrave/auth0-bundle": {"version": "dev-main"}, "superbrave/auth0-http-client": {"version": "dev-php8"}, "superbrave/messenger-outbox-bundle": {"version": "1.0.2"}, "superbrave/openapi-security-bundle": {"version": "1.0.0"}, "superbrave/pharmacy-service-client": {"version": "dev-php8"}, "superbrave/verbose-error-http-client": {"version": "2.1.2"}, "swiftmailer/swiftmailer": {"version": "v6.3.0"}, "sylius-labs/association-hydrator": {"version": "v1.1.4"}, "sylius-labs/doctrine-migrations-extra-bundle": {"version": "v0.1.4"}, "sylius-labs/polyfill-symfony-event-dispatcher": {"version": "v1.1.0"}, "sylius-labs/polyfill-symfony-framework-bundle": {"version": "v1.0.0"}, "sylius-labs/polyfill-symfony-security": {"version": "v1.0.0"}, "sylius/calendar": {"version": "v0.3.0"}, "sylius/fixtures-bundle": {"version": "v1.7.0"}, "sylius/grid-bundle": {"version": "v1.11.0"}, "sylius/mailer-bundle": {"version": "v1.7.1"}, "sylius/refund-plugin": {"version": "1.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.4", "ref": "a3f813f608c6f04bd7d0b4cefd73a96bf378c390"}}, "sylius/registry": {"version": "v1.6.0"}, "sylius/resource-bundle": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.6", "ref": "bfd4306c8e26b4aed0790ebde89a2c949e1398a2"}}, "sylius/sylius": {"version": "v1.11.5"}, "sylius/theme-bundle": {"version": "v2.2.0"}, "symfony/amqp-messenger": {"version": "v5.4.5"}, "symfony/asset": {"version": "v5.4.7"}, "symfony/browser-kit": {"version": "v5.4.3"}, "symfony/cache": {"version": "v5.4.9"}, "symfony/cache-contracts": {"version": "v2.5.1"}, "symfony/config": {"version": "v5.4.9"}, "symfony/console": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v5.4.3"}, "symfony/debug-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/dependency-injection": {"version": "v5.4.3"}, "symfony/deprecation-contracts": {"version": "v2.5.1"}, "symfony/doctrine-bridge": {"version": "v5.4.9"}, "symfony/doctrine-messenger": {"version": "v5.4.8"}, "symfony/dom-crawler": {"version": "v6.1.0"}, "symfony/dotenv": {"version": "v6.1.0"}, "symfony/error-handler": {"version": "v5.4.9"}, "symfony/event-dispatcher": {"version": "v5.4.9"}, "symfony/event-dispatcher-contracts": {"version": "v3.1.0"}, "symfony/expression-language": {"version": "v5.4.8"}, "symfony/filesystem": {"version": "v5.4.9"}, "symfony/finder": {"version": "v5.4.8"}, "symfony/flex": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v5.4.9"}, "symfony/framework-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.4.9"}, "symfony/http-client-contracts": {"version": "v2.5.1"}, "symfony/http-foundation": {"version": "v5.4.9"}, "symfony/http-kernel": {"version": "v5.4.9"}, "symfony/intl": {"version": "v5.4.8"}, "symfony/lock": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/mailer": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "2bf89438209656b85b9a49238c4467bff1b1f939"}, "files": ["config/packages/mailer.yaml"]}, "symfony/messenger": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "dfe610928a5c61619bdfc830cd7fa7f091368023"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v6.1.0"}, "symfony/monolog-bridge": {"version": "v5.4.3"}, "symfony/monolog-bundle": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "213676c4ec929f046dfde5ea8e97625b81bc0578"}, "files": ["config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v5.4.3"}, "symfony/password-hasher": {"version": "v5.4.8"}, "symfony/phpunit-bridge": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "97cb3dc7b0f39c7cfc4b7553504c9d7b7795de96"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-ctype": {"version": "v1.25.0"}, "symfony/polyfill-iconv": {"version": "v1.25.0"}, "symfony/polyfill-intl-grapheme": {"version": "v1.25.0"}, "symfony/polyfill-intl-icu": {"version": "v1.25.0"}, "symfony/polyfill-intl-idn": {"version": "v1.25.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.25.0"}, "symfony/polyfill-mbstring": {"version": "v1.25.0"}, "symfony/polyfill-php56": {"version": "v1.20.0"}, "symfony/polyfill-php72": {"version": "v1.25.0"}, "symfony/polyfill-php73": {"version": "v1.25.0"}, "symfony/polyfill-php80": {"version": "v1.25.0"}, "symfony/polyfill-php81": {"version": "v1.25.0"}, "symfony/process": {"version": "v5.4.8"}, "symfony/property-access": {"version": "v5.4.8"}, "symfony/property-info": {"version": "v5.4.9"}, "symfony/proxy-manager-bridge": {"version": "v5.4.6"}, "symfony/redis-messenger": {"version": "v5.4.6"}, "symfony/routing": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "85de1d8ae45b284c3c84b668171d2615049e698f"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "98f1f2b0d635908c2b40f3675da2d23b1a069d30"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.4.8"}, "symfony/security-csrf": {"version": "v5.4.9"}, "symfony/security-http": {"version": "v5.4.9"}, "symfony/serializer": {"version": "v5.4.9"}, "symfony/service-contracts": {"version": "v2.5.1"}, "symfony/slack-notifier": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8fb9603326990013efbe6d71c2dd78ada316808a"}}, "symfony/stopwatch": {"version": "v5.4.5"}, "symfony/string": {"version": "v5.4.9"}, "symfony/swiftmailer-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "f0b2fccdca2dfd97dc2fd5ad216d5e27c4f895ac"}, "files": ["config/packages/dev/swiftmailer.yaml", "config/packages/swiftmailer.yaml", "config/packages/test/swiftmailer.yaml"]}, "symfony/templating": {"version": "v5.4.3"}, "symfony/translation": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da64f5a2b6d96f5dc24914517c0350a5f91dee43"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.5.1"}, "symfony/twig-bridge": {"version": "v5.4.9"}, "symfony/twig-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "bb2178c57eee79e6be0b297aa96fc0c0def81387"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.4.9"}, "symfony/var-exporter": {"version": "v5.4.9"}, "symfony/web-link": {"version": "v6.1.0"}, "symfony/web-profiler-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "24bbc3d84ef2f427f82104f766014e799eefcc3e"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "642c9239e1c76e037f5b65446d09eba836ea147f"}, "files": ["assets/app.js", "assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js", "assets/styles/app.css", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/workflow": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["config/packages/workflow.yaml"]}, "symfony/yaml": {"version": "v5.4.3"}, "theseer/tokenizer": {"version": "1.2.1"}, "twig/intl-extra": {"version": "v2.15.0"}, "twig/twig": {"version": "v2.15.1"}, "webmozart/assert": {"version": "1.10.0"}, "willdurand/hateoas": {"version": "3.8.0"}, "willdurand/hateoas-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "34df072c6edaa61ae19afb2f3a239f272fecab87"}}, "willdurand/jsonp-callback-validator": {"version": "v2.0.0"}, "willdurand/negotiation": {"version": "3.1.0"}, "winzou/state-machine": {"version": "0.4.3"}, "winzou/state-machine-bundle": {"version": "0.5.1"}, "zenstruck/messenger-test": {"version": "v1.7.2"}}